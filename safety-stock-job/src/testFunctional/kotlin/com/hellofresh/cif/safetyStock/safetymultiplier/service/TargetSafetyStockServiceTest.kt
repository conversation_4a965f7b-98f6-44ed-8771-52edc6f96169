package com.hellofresh.cif.safetyStock.safetymultiplier.service

import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.model.SkuRiskRating.LOW
import com.hellofresh.safetystock.repo.SafetyStockRepository
import com.hellofresh.safetystock.service.targetSafetyStock.TargetSafetyStockImport
import com.hellofresh.safetystock.service.targetSafetyStock.TargetSafetyStockImportService
import com.hellofresh.safetystock.service.targetSafetyStock.TargetSafetyStockService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import java.math.BigDecimal.ZERO
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class TargetSafetyStockServiceTest {

    private var targetSafetyStockImportService = mockk<TargetSafetyStockImportService>(relaxed = true)
    private var safetyStockRepository = mockk<SafetyStockRepository>(relaxed = true)

    private val targetSafetyStockService = TargetSafetyStockService(
        targetSafetyStockImportService = targetSafetyStockImportService,
        safetyStockRepository = safetyStockRepository,
    )

    @Test
    fun `should not upsert safety stocks if the targetSafetyStocks is empty`() {
        // Given
        coEvery { targetSafetyStockImportService.process() } returns emptyList()

        // When
        runBlocking {
            targetSafetyStockService.process()
        }

        // Then
        coVerify(exactly = 0) {
            safetyStockRepository.upsertSafetyStocks(safetyStocks = any(), isAlgorithmForecastVariance = false)
        }
    }

    @Test
    fun `should upsert safety stocks if the targetSafetyStocks is not empty`() {
        // Given
        val skuId = UUID.randomUUID()
        val import = TargetSafetyStockImport(
            dcCode = "BX",
            skuId = skuId,
            week = "2025-W24",
            safetyStock = 12L,
            strategy = "WEEKS_COVERAGE",
        )

        val slot = slot<List<SafetyStock>>()

        coEvery { targetSafetyStockImportService.process() } returns listOf(import)
        coEvery {
            safetyStockRepository.upsertSafetyStocks(safetyStocks = capture(slot), isAlgorithmForecastVariance = false)
        } returns Unit

        // When
        runBlocking { targetSafetyStockService.process() }

        // Then
        coVerify(exactly = 1) {
            safetyStockRepository.upsertSafetyStocks(
                safetyStocks = any(),
                isAlgorithmForecastVariance = false
            )
        }

        val captured = slot.captured
        assert(captured.size == 1)
        val safetyStock = captured[0]
        assert(safetyStock.dcCode == "BX")
        assert(safetyStock.skuId == skuId)
        assert(safetyStock.week == "2025-W24")
        assert(safetyStock.value == 12L)
        assert(safetyStock.strategy == "WEEKS_COVERAGE")
        assert(safetyStock.configuration.skuRiskRating == LOW)
        assert(safetyStock.configuration.riskMultiplier == ZERO)
        assert(safetyStock.configuration.bufferPercentage == ZERO)
    }
}
