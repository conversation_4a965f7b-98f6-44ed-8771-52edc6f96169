package com.hellofresh.cif.safetyStock.repo

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import com.hellofresh.cif.safety.stock.job.schema.Tables
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStocksRecord
import com.hellofresh.cif.safetyStock.service.TestPrepare
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.safetystock.repo.SafetyStockRepositoryImpl.DcAndWeek
import com.hellofresh.safetystock.service.SAFETY_STOCK_DEFAULT_STRATEGY
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import random

private const val TARGET_SAFETY_STOCK_STRATEGY_WEEKS_COVER = "WEEKS_COVER"
private const val TARGET_SAFETY_STOCK_STRATEGY_TARGET_INVENTORY = "TARGET_INVENTORY"
private const val TARGET_SAFETY_STOCK_STRATEGY_ALGORITHM_FORECASTVARIANCE = "ALGORITHM_FORECASTVARIANCE"

class SafetyStockRepositoryTest : TestPrepare() {

    @Test
    fun `should persist safety stocks successfully`() {
        val safetyStock1 = SafetyStock.random()
        val safetyStock2 = SafetyStock.random(dcCode = "VE", week = "2024-W21")

        runBlocking { safetyStockRepository.upsertSafetyStocks(listOf(safetyStock1, safetyStock2), false) }

        assertSafetyStock(listOf(safetyStock1, safetyStock2))
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `should persist and conditionally update safety stocks based on isAlgorithmForecastVariance`(
        isAlgorithmForecastVariance: Boolean
    ) {
        // Step 1: Insert with safety stock = 1000
        val safetyStock1 = SafetyStock.random().copy(
            strategy = TARGET_SAFETY_STOCK_STRATEGY_WEEKS_COVER,
            value = 1000,
        )
        val safetyStock2 = SafetyStock.random(
            dcCode = "VE",
            week = "2025-W25",
        ).copy(
            strategy = if (isAlgorithmForecastVariance) {
                TARGET_SAFETY_STOCK_STRATEGY_ALGORITHM_FORECASTVARIANCE
            } else {
                TARGET_SAFETY_STOCK_STRATEGY_TARGET_INVENTORY
            },
            value = 1000,
        )

        runBlocking {
            safetyStockRepository.upsertSafetyStocks(listOf(safetyStock1, safetyStock2), isAlgorithmForecastVariance)
        }

        // Step 2: Update same keys, safety stock = 2000
        val updatedSafetyStock1 = safetyStock1.copy(value = 2000)
        val updatedSafetyStock2 = safetyStock2.copy(value = 2000)

        runBlocking {
            safetyStockRepository.upsertSafetyStocks(
                listOf(updatedSafetyStock1, updatedSafetyStock2),
                isAlgorithmForecastVariance
            )
        }

        // Step 3: Assert expected behavior
        val expectedUpdatedSafetyStock1 = if (isAlgorithmForecastVariance) safetyStock1 else updatedSafetyStock1

        assertSafetyStock(listOf(expectedUpdatedSafetyStock1, updatedSafetyStock2))
    }

    @ParameterizedTest
    @CsvSource(
        "value",
        "configuration",
        "strategy",
    )
    fun `should update the safety stock when fields are different`(fieldName: String) {
        val safetyStock = SafetyStock.random()

        runBlocking {
            safetyStockRepository.upsertSafetyStocks(
                safetyStocks = listOf(safetyStock),
                isAlgorithmForecastVariance = false
            )
        }

        val record = assertSafetyStock(safetyStock)

        val updatedSafetyStock = safetyStock.copy()

        // Updates copy field
        var randomSafetyStock = SafetyStock.random()
        val originalField = updatedSafetyStock::class.java.getDeclaredField(fieldName)
        originalField.isAccessible = true
        val originalFieldValue = originalField.get(updatedSafetyStock)
        var randomField = randomSafetyStock::class.java.getDeclaredField(fieldName)
        randomField.isAccessible = true
        while (originalFieldValue == randomField.get(randomSafetyStock)) {
            randomSafetyStock = SafetyStock.random()
            randomField = randomSafetyStock::class.java.getDeclaredField(fieldName)
            randomField.isAccessible = true
        }

        originalField.set(updatedSafetyStock, randomField.get(randomSafetyStock))
        originalField.isAccessible = false

        assertNotEquals(safetyStock, updatedSafetyStock)

        runBlocking { safetyStockRepository.upsertSafetyStocks(listOf(updatedSafetyStock), false) }

        val updatedRecord = assertSafetyStock(updatedSafetyStock)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertTrue(record.updatedAt.isBefore(updatedRecord.updatedAt))
    }

    @Test
    fun `should not update the safety stocks when fields are exactly the same`() {
        val safetyStock = SafetyStock.random()

        runBlocking {
            safetyStockRepository.upsertSafetyStocks(
                safetyStocks = listOf(safetyStock),
                isAlgorithmForecastVariance = false
            )
        }

        val record = assertSafetyStock(safetyStock)

        runBlocking {
            safetyStockRepository.upsertSafetyStocks(
                safetyStocks = listOf(safetyStock),
                isAlgorithmForecastVariance = false
            )
        }

        val updatedRecord = assertSafetyStock(safetyStock)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertEquals(record.updatedAt, updatedRecord.updatedAt)
    }

    @Test
    fun `should fetch safety stocks from given week and dcs`() {
        val week = "2024-W33"
        val safetyStock1 = SafetyStock.random(week = week)
            .copy(strategy = SAFETY_STOCK_DEFAULT_STRATEGY)
        val safetyStock2 = safetyStock1.copy(week = "2024-W34")
            .copy(strategy = SAFETY_STOCK_DEFAULT_STRATEGY)
        val week2 = "2024-W30"
        val safetyStock3 = SafetyStock.random(dcCode = "VE", week = "2024-W21")
            .copy(strategy = SAFETY_STOCK_DEFAULT_STRATEGY)
        val safetyStock4 = safetyStock3.copy(week = week2)
        val safetyStock5 = safetyStock3.copy(week = "2024-W32")

        runBlocking {
            safetyStockRepository.upsertSafetyStocks(
                listOf(
                    safetyStock1,
                    safetyStock2,
                    safetyStock3,
                    safetyStock4,
                    safetyStock5,
                ),
                false,
            )
        }

        val safetyStocks = runBlocking {
            safetyStockRepository.fetchSafetyStocksFromDcAndWeek(
                setOf(
                    DcAndWeek(safetyStock1.dcCode, week),
                    DcAndWeek(safetyStock3.dcCode, week2),
                ),
            )
        }

        assertEquals(
            setOf(safetyStock1, safetyStock2),
            safetyStocks.filter { it.dcCode == safetyStock1.dcCode }.toSet(),
        )
        assertEquals(
            setOf(safetyStock4, safetyStock5),
            safetyStocks.filter { it.dcCode == safetyStock4.dcCode }.toSet(),
        )
    }

    private fun assertSafetyStock(safetyStock: SafetyStock) = assertSafetyStock(
        listOf(safetyStock),
    ).first()

    private fun assertSafetyStock(safetyStocks: List<SafetyStock>): List<SafetyStocksRecord> {
        val records = dsl.selectFrom(Tables.SAFETY_STOCKS).fetch()
        assertEquals(safetyStocks.size, records.size)
        safetyStocks.forEach { safetyStock ->
            val record = records.first { it.skuId == safetyStock.skuId }
            assertSafetyStock(safetyStock, record)
        }
        return records
    }

    private fun assertSafetyStock(
        safetyStock: SafetyStock,
        record: SafetyStocksRecord
    ) {
        assertEquals(safetyStock.dcCode, record.dcCode)
        assertEquals(safetyStock.skuId, record.skuId)
        assertEquals(safetyStock.week, record.week)
        assertEquals(safetyStock.value, record.safetyStock)
        assertEquals(safetyStock.strategy, record.strategy)
        assertEquals(
            safetyStock.configuration,
            objectMapper.readValue<Configuration>(record.configuration.data()),
        )
    }
}
