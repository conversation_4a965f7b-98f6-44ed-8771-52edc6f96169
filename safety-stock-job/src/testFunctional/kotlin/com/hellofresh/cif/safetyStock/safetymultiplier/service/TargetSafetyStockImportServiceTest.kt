package com.hellofresh.cif.safetyStock.safetymultiplier.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.safetystock.service.targetSafetyStock.TargetSafetyStockImportService
import com.hellofresh.sku.models.SkuSpecification
import default
import io.mockk.coEvery
import io.mockk.mockk
import java.io.ByteArrayInputStream
import java.io.File
import java.nio.file.Files
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class TargetSafetyStockImportServiceTest {
    private val s3Importer = mockk<S3Importer>()
    private val dcConfigService = mockk<DcConfigService>(relaxed = true)
    private val skuSpecificationService = mockk<SkuSpecificationService>(relaxed = true)
    private val skuSpecification = SkuSpecification.Companion.default

    private val targetSafetyStockImportService = TargetSafetyStockImportService(
        s3Importer,
        dcConfigService,
        skuSpecificationService,
        ""
    )

    private val testBucketName = "hf-bi-dwh-uploader"
    private val skuId = UUID.randomUUID()
    private val dcVE = DistributionCenterConfiguration.default("VE")
    private val dcBX = dcVE.copy(
        dcCode = "BX",
        market = "DACH",
    )

    @BeforeEach
    fun beforeEach() {
        coEvery { dcConfigService.dcConfigurations } returns mapOf(dcVE.dcCode to dcVE, dcBX.dcCode to dcBX)
    }

    @Test
    fun `target Safety Stock process should return empty list when no files found`() {
        coEvery {
            s3Importer.listObjects(testBucketName, any())
        } returns emptyList()

        runBlocking {
            val targetSafetyStocks = targetSafetyStockImportService.process()
            assertTrue(targetSafetyStocks.isEmpty())
        }
    }

    @Test
    fun `target Safety Stock process should return parsed target safety stock data`() {
        val key = "ip_safety_stock/target_safety_stock/DACH/BX/safety_stock_calculated.csv"
        val s3File = S3File(bucket = testBucketName, key = key)
        val fileContent = readFileContent(key)

        coEvery { s3Importer.listObjects(testBucketName, any()) } returns listOf(s3File)
        coEvery { s3Importer.fetchObjectContent(testBucketName, key) } returns ByteArrayInputStream(fileContent)
        coEvery { skuSpecificationService.skuCodeLookUp(any()) } returns
            mapOf(SkuCodeDcKey(skuSpecification.skuCode, dcBX.dcCode) to (skuId to skuSpecification))

        runBlocking {
            val targetSafetyStocks = targetSafetyStockImportService.process()
            assertEquals(1, targetSafetyStocks.size)
            assertEquals("BX", targetSafetyStocks.first().dcCode)
            assertEquals(skuId, targetSafetyStocks.first().skuId)
            assertEquals("2025-W24", targetSafetyStocks.first().week)
            assertEquals("WEEKS_COVERAGE", targetSafetyStocks.first().strategy)
            assertEquals(13078, targetSafetyStocks.first().safetyStock)
        }
    }

    @ParameterizedTest(name = "should not process target safety stock if the file contains {0}")
    @MethodSource("getTargetSafetyStockInvalidInputs")
    fun `should not process invalid target safety stock files`(description: String, key: String) {
        val s3File = S3File(bucket = testBucketName, key = key)
        val fileContent = readFileContent(key)

        coEvery { s3Importer.listObjects(testBucketName, any()) } returns listOf(s3File)
        coEvery { s3Importer.fetchObjectContent(testBucketName, key) } returns ByteArrayInputStream(fileContent)
        coEvery { skuSpecificationService.skuCodeLookUp(any()) } returns
            mapOf(SkuCodeDcKey(skuSpecification.skuCode, dcBX.dcCode) to (skuId to skuSpecification))

        runBlocking {
            val result = targetSafetyStockImportService.process()
            assertEquals(0, result.size, "Failed case: $description")
        }
    }

    @Test
    fun `target Safety Stock with ALGORITHM_FORECASTVARIANCE strategy should be processed`() {
        val key = "ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated_with_ALGORITHM_FORECASTVARIANCE.csv"
        val s3File = S3File(bucket = testBucketName, key = key)
        val fileContent = readFileContent(key)

        coEvery { s3Importer.listObjects(testBucketName, any()) } returns listOf(s3File)
        coEvery { s3Importer.fetchObjectContent(testBucketName, key) } returns ByteArrayInputStream(fileContent)
        coEvery { skuSpecificationService.skuCodeLookUp(any()) } returns
            mapOf(SkuCodeDcKey(skuSpecification.skuCode, dcVE.dcCode) to (skuId to skuSpecification))

        runBlocking {
            val targetSafetyStocks = targetSafetyStockImportService.process()
            assertEquals(1, targetSafetyStocks.size)
            assertEquals("VE", targetSafetyStocks.first().dcCode)
            assertEquals(skuId, targetSafetyStocks.first().skuId)
            assertEquals("2025-W25", targetSafetyStocks.first().week)
            assertEquals("ALGORITHM_FORECASTVARIANCE", targetSafetyStocks.first().strategy)
            assertEquals(0, targetSafetyStocks.first().safetyStock)
        }
    }

    private fun readFileContent(fileName: String): ByteArray {
        val file = File(this::class.java.classLoader.getResource(fileName)!!.toURI())
        return Files.readAllBytes(file.toPath())
    }

    companion object {
        @JvmStatic
        fun getTargetSafetyStockInvalidInputs(): Stream<Arguments> = Stream.of(
            Arguments.of(
                "invalid production week",
                "ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated_invalid_production_week.csv"
            ),
            Arguments.of(
                "invalid sku code",
                "ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated_invalid_sku_code.csv"
            ),
            Arguments.of(
                "invalid strategy",
                "ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated_invalid_strategy.csv"
            ),
            Arguments.of(
                "invalid DC code",
                "ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated_invalid_dc_code.csv"
            ),
            Arguments.of(
                "not a CSV file",
                "ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated_not_a_csv.txt"
            ),
            Arguments.of(
                "empty or null safety stock",
                "ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated_with_empty_safety_stock.csv"
            )
        )
    }
}
