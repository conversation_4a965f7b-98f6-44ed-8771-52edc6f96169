package com.hellofresh.cif.safetyStock.service

import com.hellofresh.safetystock.service.SafetyStockBase
import kotlin.test.Test
import org.junit.jupiter.api.Assertions

class SafetyStockBaseTest : SafetyStockBase() {

    @Test
    fun `should verify cap safety stock`() {
        val result = capSafetyStock(10, 5)
        Assertions.assertEquals(5, result)

        val result2 = capSafetyStock(5, 10)
        Assertions.assertEquals(5, result2)
    }
}
