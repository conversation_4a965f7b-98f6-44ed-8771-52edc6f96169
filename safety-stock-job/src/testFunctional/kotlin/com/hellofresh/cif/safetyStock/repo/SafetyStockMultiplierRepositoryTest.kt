package com.hellofresh.cif.safetyStock.repo

import com.hellofresh.cif.safety.stock.job.schema.enums.SkuRiskRating
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockMultiplierRecord
import com.hellofresh.cif.safetyStock.service.TestPrepare
import com.hellofresh.cif.safetystock.model.SafetyStockMultipliers.Companion.DEFAULT_TARGET_WEED_RISK_MULTIPLIER
import com.hellofresh.cif.safetystock.model.SafetyStockMultipliers.Companion.DEFAULT_WEEK_COVER_RISK_MULTIPLIER
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepositoryImpl.Companion.toSkuRiskRating
import java.util.Random
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class SafetyStockMultiplierRepositoryTest : TestPrepare() {

    @Test
    fun `fetches safety stock multiplier by dc codes`() {
        val record1 = randomMultiplierRecord()
        val record2 = randomMultiplierRecord()
        dsl.batchInsert(record1, record2).execute()

        val results =
            runBlocking {
                safetyStockMultiplierRepository.fetchSafetyStockMultiplier(
                    setOf(record1.dcCode, record2.dcCode),
                )
            }

        with(
            results.getSafetyStockMultiplier(
                record1.dcCode,
                record1.skuId,
                toSkuRiskRating(record1.skuRiskRating),
                record1.targetWeek,
                record1.weekCover,
            ),
        ) {
            assertEquals(record1.targetWeekMultiplier, this.targetWeekMultiplier)
            assertEquals(record1.weekCoverMultiplier, this.weekCoverMultiplier)
        }

        with(
            results.getSafetyStockMultiplier(
                record2.dcCode,
                record2.skuId,
                toSkuRiskRating(record2.skuRiskRating),
                record2.targetWeek,
                record2.weekCover,
            ),
        ) {
            assertEquals(record2.targetWeekMultiplier, this.targetWeekMultiplier)
            assertEquals(record2.weekCoverMultiplier, this.weekCoverMultiplier)
        }

        with(
            results.getSafetyStockMultiplier(
                record1.dcCode,
                UUID.randomUUID(),
                toSkuRiskRating(record1.skuRiskRating),
                record1.targetWeek,
                record1.weekCover,
            ),
        ) {
            assertEquals(DEFAULT_TARGET_WEED_RISK_MULTIPLIER, this.targetWeekMultiplier)
            assertEquals(DEFAULT_WEEK_COVER_RISK_MULTIPLIER, this.weekCoverMultiplier)
        }
    }

    @Test
    fun `fetches safety stock multiplier by dc codes and week cover`() {
        val record1 = randomMultiplierRecord()
        val record2 = randomMultiplierRecord().apply { this.dcCode = record1.dcCode }
        dsl.batchInsert(record1, record2).execute()

        val results =
            runBlocking {
                safetyStockMultiplierRepository.fetchSafetyStockMultiplier(
                    setOf(record2.dcCode),
                    record2.weekCover,
                )
            }

        with(
            results.getSafetyStockMultiplier(
                record1.dcCode,
                UUID.randomUUID(),
                toSkuRiskRating(record1.skuRiskRating),
                record1.targetWeek,
                record1.weekCover,
            ),
        ) {
            assertEquals(DEFAULT_TARGET_WEED_RISK_MULTIPLIER, this.targetWeekMultiplier)
            assertEquals(DEFAULT_WEEK_COVER_RISK_MULTIPLIER, this.weekCoverMultiplier)
        }

        with(
            results.getSafetyStockMultiplier(
                record2.dcCode,
                record2.skuId,
                toSkuRiskRating(record2.skuRiskRating),
                record2.targetWeek,
                record2.weekCover,
            ),
        ) {
            assertEquals(record2.targetWeekMultiplier, this.targetWeekMultiplier)
            assertEquals(record2.weekCoverMultiplier, this.weekCoverMultiplier)
        }
    }

    private fun randomMultiplierRecord() =
        SafetyStockMultiplierRecord().apply {
            this.dcCode = UUID.randomUUID().toString()
            this.skuId = UUID.randomUUID()
            this.skuRiskRating = SkuRiskRating.entries.random()
            this.targetWeek = Random().nextLong()
            this.weekCover = Random().nextLong()
            this.targetWeekMultiplier = Random().nextDouble().toBigDecimal()
            this.weekCoverMultiplier = Random().nextDouble().toBigDecimal()
        }
}
