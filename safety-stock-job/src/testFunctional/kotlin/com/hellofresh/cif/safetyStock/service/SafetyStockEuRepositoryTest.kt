package com.hellofresh.cif.safetyStock.service

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.safetystock.service.SafetyStockEuRepository
import com.hellofresh.safetystock.service.SafetyStockFormulaParams
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SafetyStockEuRepositoryTest : TestPrepare() {
    private val safetyStockRepository = SafetyStockEuRepository(
        SafetyStockFormulaParams(
            stdDevLeadTimeFactor,
            minimumAllowedMLOR,
            true,
        ),
        dsl,
    )

    // Samples used are coming from a sheet calculator in CIF-2145
    @Test
    fun `should fetch safety stock value for all sku and dc with different reoccurrence factors`() {
        // given
        val secondSku = UUID.randomUUID() to defaultSku.second.copy(skuCode = "OTHERCODE")
        val thirdSku = UUID.randomUUID() to defaultSku.second.copy(skuCode = "OTHEROTHERCODE")
        val secondDcCode = "DC2"
        insertSkuSpecifications(
            mapOf(
                defaultSafetyStockSku,
                secondSku.first to secondSku.second,
                thirdSku.first to thirdSku.second,
            ),
        )
        val dcConfigs = setOf(defaultDcConfig, defaultDcConfig.copy(dcCode = secondDcCode))
        insertSupplierDetails(defaultSafetyStockSku.first, leadTime to minimumAllowedMLOR)
        insertSupplierDetails(secondSku.first, leadTime to minimumAllowedMLOR)
        insertSupplierDetails(thirdSku.first, leadTime to minimumAllowedMLOR)

        insertDemand(
            listOf(
                // reoccurrence factor 0.5
                createDemand(20023, defaultDcConfig.getLatestProductionStart()),
                createDemand(6453, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
                createDemand(9372, defaultDcConfig.getLatestProductionStart().plusWeeks(2)),
                createDemand(39621, defaultDcConfig.getLatestProductionStart().plusWeeks(3)),
                // reoccurrence factor 1
                createDemand(54301, defaultDcConfig.getLatestProductionStart(), secondSku.first),
                createDemand(1628, defaultDcConfig.getLatestProductionStart().plusWeeks(1), secondSku.first),
                createDemand(4723, defaultDcConfig.getLatestProductionStart().plusWeeks(2), secondSku.first),
                createDemand(105, defaultDcConfig.getLatestProductionStart().plusWeeks(3), secondSku.first),
                createDemand(3456, defaultDcConfig.getLatestProductionStart().plusWeeks(4), secondSku.first),

                // reoccurrence factor 0
                createDemand(2328, defaultDcConfig.getLatestProductionStart(), thirdSku.first, secondDcCode),
                createDemand(
                    196,
                    defaultDcConfig.getLatestProductionStart().plusWeeks(1),
                    thirdSku.first,
                    secondDcCode,
                ),
            ),
        )

        // When
        val skuSafetyStock = runBlocking { safetyStockRepository.fetchSkuSafetyStock(dcConfigs) }

        // then
        with(ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)) {
            Assertions.assertEquals(
                19884,
                skuSafetyStock[createSafetyStockKey(defaultDcCode, dcWeek, defaultSafetyStockSku.first)]?.safetyStock
            )
            Assertions.assertEquals(
                53581,
                skuSafetyStock[createSafetyStockKey(defaultDcCode, dcWeek, secondSku.first)]?.safetyStock
            )
            Assertions.assertEquals(
                0,
                skuSafetyStock[createSafetyStockKey(secondDcCode, dcWeek, thirdSku.first)]?.safetyStock
            )
        }
    }

    @Test
    fun `safety stock calculation uses max lead time from available suppliers`() {
        // given
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        insertSupplierDetails(
            defaultSafetyStockSku.first,
            listOf(
                14 to minimumAllowedMLOR,
                10 to minimumAllowedMLOR,
                35 to minimumAllowedMLOR - 1,
            ),
        )
        insertDemand(
            listOf(
                // reoccurrence factor 0.5
                createDemand(1000000, defaultDcConfig.getLatestProductionStart().minusDays(1)),
                createDemand(5044, defaultDcConfig.getLatestProductionStart()),
                createDemand(41891, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
                createDemand(3552, defaultDcConfig.getLatestProductionStart().plusWeeks(2)),
                createDemand(622, defaultDcConfig.getLatestProductionStart().plusWeeks(5)),
                createDemand(802, defaultDcConfig.getLatestProductionStart().plusWeeks(6)),
            ),
        )

        // When
        val skuSafetyStock =
            runBlocking { safetyStockRepository.fetchSkuSafetyStock(setOf(defaultDcConfig)) }

        // then

        assertEquals(7, skuSafetyStock.size)
        val currentDcWeek = DcWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)
        assertEquals(
            41123,
            skuSafetyStock[
                createSafetyStockKey(
                    defaultDcConfig.dcCode,
                    currentDcWeek,
                    defaultSafetyStockSku.first
                )
            ]?.safetyStock,
        )
    }

    @Test
    fun `safety stock calculation uses same number of demand weeks data from future and past weeks`() {
        // given
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        insertSupplierDetails(defaultSafetyStockSku.first, leadTime to minimumAllowedMLOR)

        insertDemand(
            listOf(
                createDemand(20023, defaultDcConfig.getLatestProductionStart()),
                createDemand(6453, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
                createDemand(9372, defaultDcConfig.getLatestProductionStart().plusWeeks(2)),
                createDemand(39621, defaultDcConfig.getLatestProductionStart().plusWeeks(3)),
                createDemand(54322, defaultDcConfig.getLatestProductionStart().plusWeeks(4)),
                createDemand(333, defaultDcConfig.getLatestProductionStart().plusWeeks(5)),
                createDemand(1235, defaultDcConfig.getLatestProductionStart().plusWeeks(6)),
            ),
        )

        // When
        val skuSafetyStock =
            runBlocking { safetyStockRepository.fetchSkuSafetyStock(setOf(defaultDcConfig)) }

        // then
        Assertions.assertEquals(7, skuSafetyStock.size)
        val currentWeek = ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)
        for (productionWeek in currentWeek..currentWeek.plusWeeks(6)) {
            val safetyStockKey =
                createSafetyStockKey(defaultDcConfig.dcCode, productionWeek.dcWeek, defaultSafetyStockSku.first)
            Assertions.assertEquals(
                56990,
                skuSafetyStock[safetyStockKey]?.safetyStock,
            ) { "Wrong safet stock for key $safetyStockKey" }
        }
    }

    @Test
    fun `safety stock calculation uses future number of demand weeks if there is any demand further demand for dc`() {
        // given
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        insertSupplierDetails(defaultSafetyStockSku.first, leadTime to minimumAllowedMLOR)

        insertDemand(
            listOf(
                // reoccurrence factor 1
                createDemand(10000000, defaultDcConfig.getLatestProductionStart().minusWeeks(2)),
                createDemand(10000000, defaultDcConfig.getLatestProductionStart().minusWeeks(1)),
                createDemand(5044, defaultDcConfig.getLatestProductionStart()),
                createDemand(41891, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
                createDemand(3552, defaultDcConfig.getLatestProductionStart().plusWeeks(2)),
                createDemand(622, defaultDcConfig.getLatestProductionStart().plusWeeks(5)),
                createDemand(802, defaultDcConfig.getLatestProductionStart().plusWeeks(6)),
                createDemand(************, defaultDcConfig.getLatestProductionStart().plusWeeks(12), UUID.randomUUID()),
            ),
        )

        // When
        val skuSafetyStock =
            runBlocking { safetyStockRepository.fetchSkuSafetyStock(setOf(defaultDcConfig)) }

        // then

        Assertions.assertEquals(7, skuSafetyStock.size)
        val currentWeek = ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)

        Assertions.assertEquals(
            41123,
            skuSafetyStock[
                createSafetyStockKey(defaultDcConfig.dcCode, currentWeek.dcWeek, defaultSafetyStockSku.first)
            ]?.safetyStock,
        )
    }

    @Test
    fun `safety stock calculation uses past weeks demand where there is no further demand`() {
        // given
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        insertSupplierDetails(defaultSafetyStockSku.first, leadTime to minimumAllowedMLOR)

        insertDemand(
            listOf(
                // reoccurrence factor 1
                createDemand(622, defaultDcConfig.getLatestProductionStart().minusWeeks(5)),
                createDemand(802, defaultDcConfig.getLatestProductionStart().minusWeeks(4)),
                createDemand(3552, defaultDcConfig.getLatestProductionStart().minusWeeks(3)),
                createDemand(41891, defaultDcConfig.getLatestProductionStart().minusWeeks(2)),
                createDemand(5044, defaultDcConfig.getLatestProductionStart().minusWeeks(1)),
            ),
        )

        // When
        val skuSafetyStock =
            runBlocking { safetyStockRepository.fetchSkuSafetyStock(setOf(defaultDcConfig)) }

        // then

        Assertions.assertEquals(1, skuSafetyStock.size)
        val currentWeek = ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)

        Assertions.assertEquals(
            41123,
            skuSafetyStock[
                createSafetyStockKey(defaultDcConfig.dcCode, currentWeek.dcWeek, defaultSafetyStockSku.first)
            ]?.safetyStock,
        )
    }

    @Test
    fun `fetch safety stock value should not return results when mlor is lower than limit`() {
        // given
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        insertDemand(
            listOf(
                createDemand(91, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
            ),
        )
        insertSupplierDetails(defaultSafetyStockSku.first, leadTime to minimumAllowedMLOR - 1)

        // When
        val skuSafetyStock = runBlocking {
            safetyStockRepository.fetchSkuSafetyStock(setOf(defaultDcConfig))
        }

        // then
        Assertions.assertEquals(0, skuSafetyStock.size)
    }

    @Test
    fun `fetch safety stock value should return empty results when empty dc codes requested`() {
        // When
        val skuSafetyStock = runBlocking {
            safetyStockRepository
                .fetchSkuSafetyStock(
                    emptySet(),
                )
        }

        // then
        Assertions.assertEquals(0, skuSafetyStock.size)
    }

    @Test
    fun `should fetch safety stock value not more then weekly demand`() {
        // given
        val dcCode = "EU"
        val productionStart = defaultDcConfig.getLatestProductionStart()

        insertSkuSpecifications(
            mapOf(
                defaultSafetyStockSku,
            ),
        )
        val dcConfigs = setOf(defaultDcConfig, defaultDcConfig.copy(dcCode = dcCode))
        insertSupplierDetails(defaultSafetyStockSku.first, leadTime to minimumAllowedMLOR)

        insertDemand(
            listOf(
                createDemand(1200, productionStart, defaultSku.first, dcCode = dcCode),
                createDemand(1300, productionStart.plusWeeks(1), defaultSku.first, dcCode = dcCode),
                createDemand(1000, productionStart.plusWeeks(2), defaultSku.first, dcCode = dcCode),
            ),
        )

        val skuSafetyStock = runBlocking { safetyStockRepository.fetchSkuSafetyStock(dcConfigs) }

        with(ProductionWeek(productionStart, defaultDcConfig.productionStart)) {
            val totalDemand = 1200L + 1300L + 1000L
            // safety stock should be less than or equal to total demand of the week
            val result: Boolean = totalDemand >= skuSafetyStock[SafetyStockKey(dcCode, dcWeek, defaultSku.first)]?.safetyStock!!
            Assertions.assertTrue(result)
        }
    }

    @Test
    fun `fetch safety stock value should return empty results when no supplier sku details exist`() {
        // When
        val skuSafetyStock = runBlocking {
            safetyStockRepository
                .fetchSkuSafetyStock(setOf(defaultDcConfig))
        }

        // then
        Assertions.assertEquals(0, skuSafetyStock.size)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "0.2,46,true,10289",
            "0.2,46,false,20577",
            "0.4,46,true,10816",
            "0.4,46,false,21631",
            "0.4,100,true,null",
            "0.4,null,true,10816",
        ],
        nullValues = ["null"],
    )
    fun `safety stock is calculated using different parameters`(
        leadTimeFactor: Double,
        minimumAllowedMlor: Int?,
        recurrenceFactorEnabled: Boolean,
        expectedSafetyStock: Long?
    ) {
        // given
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))

        val dcConfigs = setOf(defaultDcConfig)

        insertSupplierDetails(defaultSku.first, leadTime to minimumAllowedMLOR)

        insertDefaultDemand()

        val safetyStockRepository = SafetyStockEuRepository(
            SafetyStockFormulaParams(
                leadTimeFactor,
                minimumAllowedMlor,
                recurrenceFactorEnabled,
            ),
            dsl,
        )

        // When
        val skuSafetyStock = runBlocking { safetyStockRepository.fetchSkuSafetyStock(dcConfigs) }

        // then
        if (expectedSafetyStock != null) {
            with(ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)) {
                Assertions.assertEquals(
                    expectedSafetyStock,
                    skuSafetyStock[SafetyStockKey(defaultDcCode, dcWeek, defaultSku.first)]?.safetyStock,
                )
            }
        } else {
            assertTrue(skuSafetyStock.isEmpty())
        }
    }
}
