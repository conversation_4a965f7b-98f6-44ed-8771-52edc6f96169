package com.hellofresh.cif.safetyStock.service

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockBufferRecord
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.safetystock.repo.buffer.SafetyStockBuffer
import com.hellofresh.safetystock.service.SafetyStockBuffersFormulaService
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SafetyStockBufferFormulaServiceTest : TestPrepare() {

    private val defaultUSSafetyStockSku = defaultSafetyStockSku.first to defaultSafetyStockSku.second.copy(
        skuCode = UUID.randomUUID().toString(),
        market = "US",
    )

    private val safetyStockBuffersFormulaService = SafetyStockBuffersFormulaService(
        safetyStockDemandRepository,
        safetyStockBufferRepository,
    )

    @Test
    fun `safety stock calculation uses same number of demand weeks data from future and current weeks`() {
        // given
        insertSkuSpecifications(mapOf(defaultUSSafetyStockSku))

        dsl.batchInsert(
            SafetyStockBufferRecord().apply {
                this.dcCode = defaultUSDcCode
                this.week = DcWeek(defaultUSDcConfig.getLatestProductionStart(), defaultUSDcConfig.productionStart).value
                this.skuId = defaultUSSafetyStockSku.first
                this.buffer = BigDecimal("0.2")
            },
        ).execute()

        val expectedSafetyStock = arrayOf<Long>(2000, 8654, 0, 63, 2687, 1823)

        insertDemand(
            listOf(
                createDemand(10000, defaultUSDcConfig.getLatestProductionStart(), dcCode = defaultUSDcConfig.dcCode),
                createDemand(
                    43269,
                    defaultUSDcConfig.getLatestProductionStart().plusWeeks(1),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
                createDemand(
                    0,
                    defaultUSDcConfig.getLatestProductionStart().plusWeeks(2),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
                createDemand(
                    316,
                    defaultUSDcConfig.getLatestProductionStart().plusWeeks(3),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
                createDemand(
                    13436,
                    defaultUSDcConfig.getLatestProductionStart().plusWeeks(4),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
                createDemand(
                    9113,
                    defaultUSDcConfig.getLatestProductionStart().plusWeeks(5),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
            ),
        )

        // When
        val skuSafetyStocks = runBlocking {
            safetyStockBuffersFormulaService.calculateSafetyStocks(
                setOf(defaultUSDcConfig),
                SafetyStockConfigurations(emptyList()),
            )
        }

        // then
        assertEquals(6, skuSafetyStocks.size)
        skuSafetyStocks.forEachIndexed { index, stock ->
            assertEquals(expectedSafetyStock[index], stock.value)
        }
    }

    @Test
    fun `safety stock calculation uses same number of demand weeks data from past weeks`() {
        // given
        insertSkuSpecifications(mapOf(defaultUSSafetyStockSku))

        dsl.batchInsert(
            SafetyStockBufferRecord().apply {
                this.dcCode = defaultUSDcCode
                this.week = DcWeek(defaultUSDcConfig.getLatestProductionStart(), defaultUSDcConfig.productionStart).value
                this.skuId = defaultUSSafetyStockSku.first
                this.buffer = BigDecimal("0.2")
            },
        ).execute()

        val expectedSafetyStock = arrayOf<Long>(2000)
        insertDemand(
            listOf(
                createDemand(
                    10000,
                    defaultUSDcConfig.getLatestProductionStart(),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
                createDemand(
                    43269,
                    defaultUSDcConfig.getLatestProductionStart().minusWeeks(2),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
                createDemand(
                    8000,
                    defaultUSDcConfig.getLatestProductionStart().minusWeeks(3),
                    dcCode = defaultUSDcConfig.dcCode,
                ),
            ),
        )

        // When
        val skuSafetyPastStocks = runBlocking {
            safetyStockBuffersFormulaService.calculateSafetyStocks(
                setOf(defaultUSDcConfig),
                SafetyStockConfigurations(emptyList()),
            )
        }

        // then
        assertEquals(1, skuSafetyPastStocks.size)
        skuSafetyPastStocks.forEachIndexed { index, stock ->
            assertEquals(expectedSafetyStock[index], stock.value)
        }
    }

    @Test
    fun `safety stock used buffer configuration`() {
        // given

        val currentWeek =
            ProductionWeek(defaultUSDcConfig.getLatestProductionStart(), defaultUSDcConfig.productionStart)
        val ukSkuId = defaultUSSafetyStockSku.first
        val safetyStockParamsByWeek = mapOf(
            currentWeek to Pair(BigDecimal("0.2"), 5044L),
            currentWeek.plusWeeks(1) to Pair(BigDecimal("0.1"), 1456L),
            currentWeek.plusWeeks(2) to Pair(BigDecimal("0.35"), 654L),
        )

        safetyStockParamsByWeek.forEach { (week, params) ->
            insertSafetyStockBuffer(
                defaultUSDcCode,
                week.dcWeek.value,
                ukSkuId,
                params.first,
            )
        }

        insertSkuSpecifications(mapOf(defaultUSSafetyStockSku))
        insertDemand(
            safetyStockParamsByWeek.map { (week, params) ->
                createDemand(
                    params.second,
                    week.dcWeek.getStartDateInDcWeek(defaultUSDcConfig.productionStart, defaultUSDcConfig.zoneId),
                    ukSkuId,
                    dcCode = defaultUSDcConfig.dcCode,
                )
            },
        )

        val safetyStockConfigurations = SafetyStockConfigurations(emptyList())
        // When
        val skuSafetyStock =
            runBlocking {
                safetyStockBuffersFormulaService.calculateSafetyStocks(
                    setOf(defaultUSDcConfig),
                    safetyStockConfigurations,
                )
            }

        // then
        assertEquals(3, skuSafetyStock.size)
        safetyStockParamsByWeek.forEach { (week, params) ->
            val safetyStock = skuSafetyStock.first {
                it.toKey() == createSafetyStockKey(defaultUSDcConfig.dcCode, week.dcWeek, ukSkuId)
            }
            assertEquals(
                safetyStockConfigurations.getConfiguration(defaultUSDcConfig.dcCode, ukSkuId, week).riskMultiplier,
                safetyStock.configuration.riskMultiplier,
            )
            assertEquals(
                safetyStockConfigurations.getConfiguration(defaultUSDcConfig.dcCode, ukSkuId, week).skuRiskRating,
                safetyStock.configuration.skuRiskRating,
            )
            assertEquals(params.first, safetyStock.configuration.bufferPercentage)
            assertEquals(
                params.first.multiply(params.second.toBigDecimal()).setScale(0, RoundingMode.HALF_UP).toLong(),
                safetyStock.value,
            )
        }
    }

    @Test
    fun `fetch safety stock value use default percentage`() {
        // given

        val currentWeek =
            ProductionWeek(defaultUSDcConfig.getLatestProductionStart(), defaultUSDcConfig.productionStart)
        val ukSkuId = defaultUSSafetyStockSku.first

        val safetyStockConfigurations =
            runBlocking { safetyStockConfigurationRepository.fetchSafetyStockConfigurations(setOf(defaultUSDcConfig)) }

        insertSkuSpecifications(mapOf(defaultUSSafetyStockSku))

        val demands = mapOf(
            currentWeek to 5044L,
            currentWeek.plusWeeks(1) to 1456L,
            currentWeek.plusWeeks(2) to 654L,
        )

        insertDemand(
            demands.map { (week, demand) ->
                createDemand(
                    demand,
                    week.dcWeek.getStartDateInDcWeek(defaultUSDcConfig.productionStart, defaultUSDcConfig.zoneId),
                    ukSkuId,
                    dcCode = defaultUSDcConfig.dcCode,
                )
            },
        )

        // When
        val skuSafetyStock =
            runBlocking {
                safetyStockBuffersFormulaService.calculateSafetyStocks(
                    setOf(defaultUSDcConfig),
                    safetyStockConfigurations,
                )
            }

        // then
        assertEquals(3, skuSafetyStock.size)
        demands.forEach { (week, demand) ->
            val safetyStock = skuSafetyStock.first {
                it.toKey() == createSafetyStockKey(defaultUSDcConfig.dcCode, week.dcWeek, ukSkuId)
            }
            assertEquals(
                SafetyStockBuffer.DEFAULT_BUFFER_PERCENTAGE.multiply(demand.toBigDecimal())
                    .setScale(0, RoundingMode.HALF_UP).toLong(),
                safetyStock.value,
            )
        }
    }

    @Test
    fun `fetch safety stock value should return empty results when empty dc codes requested`() {
        // When
        val skuSafetyStock = runBlocking {
            safetyStockBuffersFormulaService.calculateSafetyStocks(emptySet(), SafetyStockConfigurations(emptyList()))
        }

        // then
        assertEquals(0, skuSafetyStock.size)
    }
}
