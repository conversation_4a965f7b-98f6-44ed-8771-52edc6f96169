package com.hellofresh.cif.safetyStock.repo

import com.hellofresh.cif.safety.stock.job.schema.Tables
import com.hellofresh.cif.safetyStock.service.TestPrepare
import com.hellofresh.cif.safetystock.model.SafetyStockBuffer
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class SafetyStockBufferRepositoryTest : TestPrepare() {

    @Test
    fun `Verify that the upsert method is correct`() {
        val safetyStockBuffer = mutableListOf<SafetyStockBuffer>()
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        safetyStockBuffer.add(randomSafetyStockBuffer(skuId1, "NJ", "2025-W08", 0.02.toBigDecimal()))
        safetyStockBuffer.add(randomSafetyStockBuffer(skuId1, "NJ", "2025-W10", 0.20.toBigDecimal()))
        safetyStockBuffer.add(randomSafetyStockBuffer(skuId2, "NT", "2025-W10", 0.20.toBigDecimal()))

        runBlocking {
            safetyStockBufferRepo.upsertSafetyStockBuffer(
                safetyStockBuffer
            )
        }

        val records = runBlocking {
            dsl.selectFrom(Tables.SAFETY_STOCK_BUFFER).fetch()
        }

        assertEquals(3, records.size)
        val safetyStock = safetyStockBuffer.filter { it.skuId == skuId1 }
        with(safetyStock.first()) {
            assertEquals(skuId, skuId1)
            assertEquals(dcCode, "NJ")
            assertEquals(buffer, "0.02".toBigDecimal())
        }

        with(safetyStock.last()) {
            assertEquals(skuId, skuId1)
            assertEquals(dcCode, "NJ")
            assertEquals(buffer, 0.20.toBigDecimal())
        }

        // As record already exists, test for upsert buffer 0.30 for 2025-W10
        runBlocking {
            safetyStockBufferRepo.upsertSafetyStockBuffer(
                listOf(randomSafetyStockBuffer(skuId2, "NT", "2025-W10", 0.30.toBigDecimal()))
            )
        }

        val records2 = runBlocking {
            dsl.selectFrom(Tables.SAFETY_STOCK_BUFFER).fetch()
        }

        val record = records2.filter { it.skuId == skuId2 }.first()
        assertEquals(record.skuId, skuId2)
        assertEquals(record.dcCode, "NT")
        assertEquals(record.buffer, 0.30.toBigDecimal())
    }

    private fun randomSafetyStockBuffer(skuId: UUID, dc: String, week: String, buffer: BigDecimal) =
        SafetyStockBuffer(
            skuId = skuId,
            dcCode = dc,
            week = week,
            buffer = buffer,
        )
}
