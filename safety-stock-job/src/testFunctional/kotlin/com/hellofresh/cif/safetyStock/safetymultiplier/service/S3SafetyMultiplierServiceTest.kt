package com.hellofresh.cif.safetyStock.safetymultiplier.service

import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_MULTIPLIER
import com.hellofresh.cif.safetyStock.service.TestPrepare
import io.mockk.coEvery
import java.io.ByteArrayInputStream
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

private const val BUCKET_NAME = "hf-bi-dwh-uploader"

class S3SafetyMultiplierServiceTest : TestPrepare() {
    @Test
    fun `Should import safety stock multiplier data to safety stock multiplier table`() {
        val key = "ip_safety_stock/safety_stock_multipliers/DACH/VE/safety_stock_multiplier.csv"
        val fileContentInByteArray = readFileContent(key)
        coEvery {
            s3Importer.fetchObjectContent(BUCKET_NAME, key)
        } returns ByteArrayInputStream(fileContentInByteArray)

        insertSkuSpecification(
            listOf(
                TestSkuItem("PRO-00-90459-5", "DACH"),
                TestSkuItem("PRO-00-90562-6", "DACH"),
                TestSkuItem("PRO-00-90562-7", "DACH"),
                TestSkuItem("PRO-00-90558-8", "DACH"),
                TestSkuItem("PRO-00-50357-9", "DACH"),
                TestSkuItem("PRO-00-116337-5", "DACH"),
            ),
        )

        runBlocking { s3SafetyMultiplierService.process(S3File(BUCKET_NAME, key)) }

        val safetyStockMultiplierRecord = dsl.selectFrom(SAFETY_STOCK_MULTIPLIER).fetch()

        assertEquals(6, safetyStockMultiplierRecord.size)
    }

    @ParameterizedTest(name = "{index} => {1}")
    @MethodSource("provideInputsForInvalidSafetyStockMultiplier")
    fun `should not process the safety stock multiplier`(key: String) {
        val fileContentInByteArray = readFileContent(key)
        coEvery {
            s3Importer.fetchObjectContent(BUCKET_NAME, key)
        } returns ByteArrayInputStream(fileContentInByteArray)

        runBlocking { s3SafetyMultiplierService.process(S3File(BUCKET_NAME, key)) }

        val safetyStockMultiplierRecord = dsl.selectFrom(SAFETY_STOCK_MULTIPLIER).fetch()

        assertEquals(0, safetyStockMultiplierRecord.size)
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun provideInputsForInvalidSafetyStockMultiplier(): Stream<Arguments> = Stream.of(
            Arguments.of(
                "ip_safety_stock/safety_stock_multipliers/DACH/VE/safety_stock_multiplier_wrong_headers.csv",
                "should not process the safety stock multiplier if the file has wrong headers",
            ),
            Arguments.of(
                "ip_safety_stock/safety_stock_multipliers/DACH/VE/safety_stock_multiplier_not_a_csv_format.csv",
                "should not process the safety stock multiplier if the file is not in CSV format",
            ),
            Arguments.of(
                "ip_safety_stock/safety_stock_multipliers/DACH/VE/safety_stock_multiplier_with_negative_values.csv",
                "should not process the safety stock multiplier if the file has negative value",
            ),
            Arguments.of(
                "ip_safety_stock/safety_stock_multipliers/DACH/VE/safety_stock_multiplier_with_invalid_number_of_columns.csv",
                "should not process the safety stock multiplier if the file has invalid number of columns",
            ),
            Arguments.of(
                "ip_safety_stock/safety_stock_multipliers/DACH/VE/safety_stock_multiplier_with_blank_cell.csv",
                "should not process the safety stock multiplier if the file has blank cell",
            ),
        )
    }
}
