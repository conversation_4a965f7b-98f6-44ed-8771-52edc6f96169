package com.hellofresh.cif.safetyStock.repo

import com.hellofresh.cif.safety.stock.job.schema.Tables
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockImportRecord
import com.hellofresh.cif.safetyStock.service.TestPrepare
import com.hellofresh.safetystock.safetymultiplier.service.SafetyStockImport
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import random

class SafetyStockImportRepositoryTest : TestPrepare() {

    @Test
    fun `should persist safety stock import successfully`() {
        val safetyStockImport1 = SafetyStockImport.Companion.random()
        val safetyStockImport2 = SafetyStockImport.Companion.random()

        runBlocking { safetyStockImportRepository.upsertSafetyStocks(listOf(safetyStockImport1, safetyStockImport2)) }

        assertSafetyStockImport(listOf(safetyStockImport1, safetyStockImport2))
    }

    @ParameterizedTest
    @CsvSource(
        "safetyStock",
    )
    fun `should upsert the safety stock import fields`(fieldName: String) {
        val safetyStockImport = SafetyStockImport.Companion.random()

        runBlocking { safetyStockImportRepository.upsertSafetyStocks(listOf(safetyStockImport)) }

        val record = assertSafetyStockImport(safetyStockImport)

        val updatedSafetyStock = safetyStockImport.copy()

        // Updates copy field
        var randomSafetyStock = SafetyStockImport.Companion.random()
        val originalField = updatedSafetyStock::class.java.getDeclaredField(fieldName)
        originalField.isAccessible = true
        val originalFieldValue = originalField.get(updatedSafetyStock)
        var randomField = randomSafetyStock::class.java.getDeclaredField(fieldName)
        randomField.isAccessible = true
        while (originalFieldValue == randomField.get(randomSafetyStock)) {
            randomSafetyStock = SafetyStockImport.Companion.random()
            randomField = randomSafetyStock::class.java.getDeclaredField(fieldName)
            randomField.isAccessible = true
        }

        originalField.set(updatedSafetyStock, randomField.get(randomSafetyStock))
        originalField.isAccessible = false

        assertNotEquals(safetyStockImport, updatedSafetyStock)

        runBlocking { safetyStockImportRepository.upsertSafetyStocks(listOf(updatedSafetyStock)) }

        val updatedRecord = assertSafetyStockImport(updatedSafetyStock)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertTrue(record.updatedAt.isBefore(updatedRecord.updatedAt))
    }

    @Test
    fun `should fetch safety stock import from week and dcs`() {
        val safetyStockImport1 = SafetyStockImport.Companion.random()
            .copy(week = "2025-W15")
        val safetyStockImport2 = SafetyStockImport.Companion.random()
            .copy(week = "2025-W20")
        val safetyStockImport3 = SafetyStockImport.Companion.random()
            .copy(dcCode = "D2", week = "2025-W16")

        val records = listOf(safetyStockImport1, safetyStockImport2, safetyStockImport3)
        dsl.batchInsert(records.map { toSafetyStockImportRecord(it) }).execute()

        val safetyStockImports =
            runBlocking {
                safetyStockImportRepository.fetchSafetyStockImports(
                    "2025-W16",
                    setOf(safetyStockImport2.dcCode, safetyStockImport3.dcCode),
                )
            }

        assertEquals(2, safetyStockImports.size)
        assertEquals(safetyStockImport2, safetyStockImports.first { it.dcCode == safetyStockImport2.dcCode })
        assertEquals(safetyStockImport3, safetyStockImports.first { it.dcCode == safetyStockImport3.dcCode })
    }

    @Test
    fun `should fetch safety stock import from week and dcs for specific skus`() {
        val safetyStockImport1 = SafetyStockImport.Companion.random()
            .copy(week = "2025-W15")
        val safetyStockImport2 = SafetyStockImport.Companion.random()
            .copy(week = "2025-W15")
        val safetyStockImport3 = SafetyStockImport.Companion.random()
            .copy(week = "2025-W15")

        val records = listOf(safetyStockImport1, safetyStockImport2, safetyStockImport3)
        dsl.batchInsert(records.map { toSafetyStockImportRecord(it) }).execute()

        val safetyStockImports =
            runBlocking {
                safetyStockImportRepository.fetchSafetyStockImports(
                    "2025-W15",
                    setOf(safetyStockImport2.dcCode, safetyStockImport3.dcCode),
                    setOf(safetyStockImport2.skuId, safetyStockImport3.skuId),
                )
            }

        assertEquals(2, safetyStockImports.size)
        assertEquals(safetyStockImport2, safetyStockImports.first { it.skuId == safetyStockImport2.skuId })
        assertEquals(safetyStockImport3, safetyStockImports.first { it.skuId == safetyStockImport3.skuId })
    }

    private fun toSafetyStockImportRecord(safetyStockImport: SafetyStockImport) =
        SafetyStockImportRecord().apply {
            this.dcCode = safetyStockImport.dcCode
            this.skuId = safetyStockImport.skuId
            this.week = safetyStockImport.week
            this.safetyStock = safetyStockImport.safetyStock
        }

    private fun assertSafetyStockImport(safetyStockImport: SafetyStockImport) =
        assertSafetyStockImport(listOf(safetyStockImport)).first()

    private fun assertSafetyStockImport(safetyStockImports: List<SafetyStockImport>): List<SafetyStockImportRecord> {
        val records = dsl.selectFrom(Tables.SAFETY_STOCK_IMPORT).fetch()
        assertEquals(safetyStockImports.size, records.size)
        safetyStockImports.forEach { safetyStock ->
            val record = records.first { it.skuId == safetyStock.skuId }
            assertSafetyStockImport(safetyStock, record)
        }
        return records
    }

    private fun assertSafetyStockImport(
        safetyStockImport: SafetyStockImport,
        record: SafetyStockImportRecord
    ) {
        assertEquals(safetyStockImport.dcCode, record.dcCode)
        assertEquals(safetyStockImport.skuId, record.skuId)
        assertEquals(safetyStockImport.week, record.week)
        assertEquals(safetyStockImport.safetyStock, record.safetyStock)
    }
}
