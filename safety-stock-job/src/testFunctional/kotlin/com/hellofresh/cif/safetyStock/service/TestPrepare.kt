package com.hellofresh.cif.safetyStock.service

import InfraPreparation
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCKS
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_BUFFER
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_IMPORT
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_MULTIPLIER
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockBufferRecord
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.reader.ParquetFileReader
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.cif.safetystocklib.schema.tables.SafetyStockConf.SAFETY_STOCK_CONF
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.DemandRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierCulinarySkuRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierSkuPricingRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierSkuRecord
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.repo.SupplierRepositoryImpl
import com.hellofresh.cif.sqrlib.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF
import com.hellofresh.safetystock.repo.SafetyStockBufferRepositoryImpl
import com.hellofresh.safetystock.repo.SafetyStockDemandRepository
import com.hellofresh.safetystock.repo.SafetyStockImportRepository
import com.hellofresh.safetystock.repo.SafetyStockRepository
import com.hellofresh.safetystock.repo.SafetyStockRepositoryImpl
import com.hellofresh.safetystock.repo.buffer.SafetyStockBufferRepository
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepository
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepositoryImpl
import com.hellofresh.safetystock.safetymultiplier.service.S3SafetyMultiplierService
import com.hellofresh.safetystock.safetymultiplier.service.S3USSafetyStockBufferService
import com.hellofresh.sku.models.LeadTime
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.SupplierSkuDetail
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.mockk
import java.io.File
import java.math.BigDecimal
import java.nio.file.Files
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach

open class TestPrepare {

    val minimumAllowedMLOR = 46
    val leadTime = 14
    val stdDevLeadTimeFactor = 0.2
    val defaultSafetyStockStrategy = "ALGORITHM_FORECASTVARIANCE"

    private val defaultMarket = "MRKT"
    internal val defaultDcCode = "DC"
    internal val defaultUSDcCode = "NJ"
    internal val defaultSku = UUID.randomUUID() to
        SkuSpecification(
            category = "PHF",
            skuCode = "PHF-00-00000-0",
            name = "s sku",
            coolingType = "aaa",
            packaging = "bbb",
            acceptableCodeLife = 2,
            market = "dach",
        )

    internal val defaultSafetyStockSku = defaultSku.first to defaultSku.second

    internal val defaultDcConfig = DistributionCenterConfiguration(
        dcCode = defaultDcCode,
        productionStart = LocalDate.now().dayOfWeek,
        cleardown = LocalDate.now().dayOfWeek,
        market = defaultMarket,
        zoneId = ZoneOffset.UTC,
        enabled = true,
        wmsType = WmsSystem.WMS_SYSTEM_FCMS,
    )

    internal val defaultUSDcConfig = defaultDcConfig.copy(market = "US", dcCode = defaultUSDcCode)

    @BeforeEach
    fun beforeEach() {
        persistDcConfig()
        dcConfigService.fetchOnDemand()
    }

    @AfterEach
    fun tearDown() {
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        refreshSkuView()
        dsl.deleteFrom(Tables.DEMAND).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU).execute()
        dsl.deleteFrom(Tables.SUPPLIER_CULINARY_SKU).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU_PRICING).execute()
        dsl.deleteFrom(Tables.SUPPLIER).execute()
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
        dsl.deleteFrom(SAFETY_STOCKS).execute()
        dsl.deleteFrom(SAFETY_STOCK_MULTIPLIER).execute()
        dsl.deleteFrom(SAFETY_STOCK_CONF).execute()
        dsl.deleteFrom(SAFETY_STOCK_BUFFER).execute()
        dsl.deleteFrom(SAFETY_STOCK_IMPORT).execute()
        dsl.deleteFrom(SUPPLY_QUANTITY_RECOMMENDATION_CONF).execute()
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    fun persistDcConfig(
        dcCode: String = "VE",
        market: String = "DACH",
        zoneId: ZoneId = ZoneId.of("Europe/Berlin"),
        productionStartDay: DayOfWeek = MONDAY
    ) {
        val dcConfig = DcConfigRecord(
            dcCode, market, productionStartDay.name, "FRIDAY", zoneId.id,
            true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null,
            LocalTime.now(), emptyArray()
        )
        dsl.batchInsert(dcConfig).execute()
    }

    fun insertSkuSpecifications(skuSpecifications: Map<UUID, SkuSpecification>) {
        skuSpecifications.forEach { (skuId, skuSpec) ->
            dsl.insertInto(
                Tables.SKU_SPECIFICATION,
                Tables.SKU_SPECIFICATION.ID,
                Tables.SKU_SPECIFICATION.PARENT_ID,
                Tables.SKU_SPECIFICATION.CATEGORY,
                Tables.SKU_SPECIFICATION.CODE,
                Tables.SKU_SPECIFICATION.NAME,
                Tables.SKU_SPECIFICATION.COOLING_TYPE,
                Tables.SKU_SPECIFICATION.PACKAGING,
                Tables.SKU_SPECIFICATION.ACCEPTABLE_CODE_LIFE,
                Tables.SKU_SPECIFICATION.MARKET,
            )
                .values(
                    skuId,
                    skuSpec.parentId,
                    skuSpec.category,
                    skuSpec.skuCode,
                    skuSpec.name,
                    skuSpec.coolingType,
                    skuSpec.packaging,
                    skuSpec.acceptableCodeLife,
                    skuSpec.market,
                )
                .execute()
        }.also {
            refreshSkuView()
        }
    }

    fun insertSkuSpecification(skuCodes: List<TestSkuItem>) {
        skuCodes.forEach { skuItem ->
            dsl.insertInto(
                Tables.SKU_SPECIFICATION,
                Tables.SKU_SPECIFICATION.ID,
                Tables.SKU_SPECIFICATION.PARENT_ID,
                Tables.SKU_SPECIFICATION.CATEGORY,
                Tables.SKU_SPECIFICATION.CODE,
                Tables.SKU_SPECIFICATION.NAME,
                Tables.SKU_SPECIFICATION.COOLING_TYPE,
                Tables.SKU_SPECIFICATION.PACKAGING,
                Tables.SKU_SPECIFICATION.ACCEPTABLE_CODE_LIFE,
                Tables.SKU_SPECIFICATION.MARKET,
            )
                .values(
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    "PRO",
                    skuItem.skuCode,
                    "${skuItem.skuCode}-name",
                    "coolingType",
                    "packaging",
                    0,
                    skuItem.market,
                )
                .execute()
        }.also {
            refreshSkuView()
        }
    }

    fun createSafetyStockKey(
        dcCode: String,
        dcWeek: DcWeek,
        skuId: UUID,
    ) = SafetyStockKey(
        dcCode = dcCode,
        dcWeek = dcWeek,
        skuId = skuId,
    )

    data class TestSkuItem(
        val skuCode: String,
        val market: String
    )

    internal fun insertSupplierDetails(
        skuId: UUID,
        mlorAndLeadTime: LeadTimeAndMlor,
        market: String = defaultMarket.lowercase()
    ) =
        insertSupplierDetails(skuId, listOf(mlorAndLeadTime), market)

    internal fun insertSupplierDetails(
        skuId: UUID,
        supplierDetails: List<LeadTimeAndMlor>,
        market: String = defaultMarket.lowercase()
    ) =

        supplierDetails.map { (leadTime, mlor) ->
            val supplierCulinarySkuRecord = SupplierCulinarySkuRecord().apply {
                this.id = UUID.randomUUID()
                this.supplierId = UUID.randomUUID()
                this.culinarySkuId = UUID.randomUUID()
                this.market = market
                this.status = "active"
            }

            val supplierSkuRecord =
                SupplierSkuRecord().apply {
                    this.skuId = skuId
                    this.mlorDays = mlor
                    this.supplierSkuId = supplierCulinarySkuRecord.id
                    this.status = "active"
                }

            val supplierRecord = SupplierRecord().apply {
                this.id = UUID.randomUUID()
                this.name = "Supplier 1"
                this.parentId = supplierCulinarySkuRecord.supplierId
            }

            val supplierSkuPricingRecord =
                SupplierSkuPricingRecord().apply {
                    this.enabled = true
                    this.id = UUID.randomUUID()
                    this.supplierSkuId = supplierCulinarySkuRecord.id
                    this.startDate = LocalDate.now().minusWeeks(2)
                    this.endDate = LocalDate.now().plusWeeks(2)
                    this.leadTime = leadTime
                }

            dsl.batchInsert(
                supplierCulinarySkuRecord,
                supplierRecord,
                supplierSkuRecord,
                supplierSkuPricingRecord,
            ).execute()
            SupplierSkuDetail(
                supplierId = supplierRecord.id,
                supplierName = supplierRecord.name,
                mlor = supplierSkuRecord.mlorDays,
                leadTimes = listOf(
                    LeadTime(
                        supplierSkuPricingRecord.leadTime,
                        supplierSkuPricingRecord.startDate,
                        supplierSkuPricingRecord.endDate,
                    ),
                ),
            )
        }

    internal fun insertDefaultDemand() {
        insertDemand(
            listOf(
                createDemand(20023, defaultDcConfig.getLatestProductionStart()),
                createDemand(6453, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
                createDemand(9372, defaultDcConfig.getLatestProductionStart().plusWeeks(2)),
            ),
        )
    }

    internal fun createDemand(
        qty: Long,
        date: LocalDate = LocalDate.now(ZoneOffset.UTC),
        skuId: UUID = defaultSafetyStockSku.first,
        dcCode: String = defaultDcCode
    ) =
        DemandRecord().apply {
            this.skuId = skuId
            this.dcCode = dcCode
            this.date = date
            this.quantity = qty.toBigDecimal()
            this.recordTimestamp_ = LocalDateTime.now(ZoneOffset.UTC)
        }

    internal fun insertDemand(demands: List<DemandRecord>) {
        dsl.batchInsert(demands).execute()
    }

    protected fun readFileContent(fileName: String): ByteArray =
        with(this::class.java.classLoader) {
            File(getResource(fileName)!!.toURI())
        }.let {
            Files.readAllBytes(it.toPath())
        }

    fun insertSafetyStockBuffer(dcCode: String, week: String, skuId: UUID, buffer: BigDecimal) {
        dsl.batchInsert(
            SafetyStockBufferRecord().apply {
                this.dcCode = dcCode
                this.week = week
                this.skuId = skuId
                this.buffer = buffer
            },
        ).execute()
    }

    companion object {
        private val dataSource = InfraPreparation.getMigratedDataSource()
        lateinit var dsl: MetricsDSLContext
        lateinit var dcConfigRepository: DcRepository
        lateinit var safetyStockRepository: SafetyStockRepository
        lateinit var supplierRepository: SupplierRepositoryImpl
        lateinit var safetyStockConfigurationRepository: SafetyStockConfigurationRepository
        lateinit var safetyStockMultiplierRepository: SafetyStockMultiplierRepository
        lateinit var safetyStockBufferRepository: SafetyStockBufferRepository
        lateinit var safetyStockDemandRepository: SafetyStockDemandRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var s3Importer: S3Importer
        lateinit var s3SafetyMultiplierService: S3SafetyMultiplierService
        lateinit var s3UsSafetyStockBufferService: S3USSafetyStockBufferService
        lateinit var safetyStockBufferRepo: SafetyStockBufferRepositoryImpl
        lateinit var safetyStockImportRepository: SafetyStockImportRepository
        lateinit var skuSpecificationsRepo: SkuSpecificationRepositoryImpl
        private lateinit var skuInputDataRepositoryImpl: SkuInputDataRepositoryImpl

        @BeforeAll
        @JvmStatic
        fun setUp() {
            val usDcConfig = DistributionCenterConfiguration(
                dcCode = "NJ",
                productionStart = LocalDate.now().dayOfWeek,
                cleardown = LocalDate.now().dayOfWeek,
                market = "US",
                zoneId = ZoneOffset.UTC,
                enabled = true,
                wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            )

            val defaultConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(
                defaultConfiguration,
            ).withMetrics(SimpleMeterRegistry())
            dcConfigRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(
                SimpleMeterRegistry(),
                repo = {
                    listOf(
                        usDcConfig,
                        usDcConfig.copy(dcCode = "VE", market = "DACH"),
                    )
                },
            )
            safetyStockRepository = SafetyStockRepositoryImpl(dsl, dsl)
            safetyStockConfigurationRepository = SafetyStockConfigurationRepository(dsl)
            safetyStockMultiplierRepository = SafetyStockMultiplierRepositoryImpl(dsl, dsl)
            safetyStockBufferRepository = SafetyStockBufferRepository(dsl)
            safetyStockDemandRepository = SafetyStockDemandRepository(dsl)
            safetyStockBufferRepo = SafetyStockBufferRepositoryImpl(dsl)
            safetyStockImportRepository = SafetyStockImportRepository(dsl, dsl)
            skuSpecificationsRepo = SkuSpecificationRepositoryImpl(dsl)
            supplierRepository = SupplierRepositoryImpl(dsl)
            s3Importer = mockk<S3Importer>()
            skuInputDataRepositoryImpl = SkuInputDataRepositoryImpl(dsl, dcConfigService)
            s3SafetyMultiplierService = S3SafetyMultiplierService(
                s3Importer,
                safetyStockMultiplierRepository, skuInputDataRepositoryImpl
            )
            s3UsSafetyStockBufferService = S3USSafetyStockBufferService(
                s3Importer,
                ParquetFileReader(),
                dcConfigService,
                SkuSpecificationService(SimpleMeterRegistry(), skuSpecificationsRepo),
                safetyStockBufferRepo
            )
        }
    }
}

private typealias LeadTimeAndMlor = Pair<Int, Int>
