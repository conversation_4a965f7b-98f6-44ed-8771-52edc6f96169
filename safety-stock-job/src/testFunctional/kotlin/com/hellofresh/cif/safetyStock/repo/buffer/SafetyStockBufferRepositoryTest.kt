package com.hellofresh.cif.safetyStock.repo.buffer

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockBufferRecord
import com.hellofresh.cif.safetyStock.service.TestPrepare
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class SafetyStockBufferRepositoryTest : TestPrepare() {

    @Test
    fun `safety stock buffers are fetched from given week`() {
        val distributionCenterConfiguration1 = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = DayOfWeek.TUESDAY,
                zoneId = ZoneOffset.UTC,
            )
        val distributionCenterConfiguration2 = DistributionCenterConfiguration.Companion.default().copy(
            dcCode = "DC",
            productionStart = DayOfWeek.WEDNESDAY,
            zoneId = ZoneOffset.UTC,
        )

        val today = LocalDate.now(ZoneOffset.UTC)
        val skuId1 = UUID.randomUUID()
        val sku1Buffer1 =
            insertBuffer(
                distributionCenterConfiguration1,
                today,
                skuId1,
                BigDecimal.ONE,
            )
        val sku1Buffer2 =
            insertBuffer(
                distributionCenterConfiguration1,
                today.plusWeeks(1),
                skuId1,
                BigDecimal.valueOf(Random.Default.nextDouble()),
            )

        val skuId2 = UUID.randomUUID()
        val sku2Buffer1 =
            insertBuffer(
                distributionCenterConfiguration2,
                distributionCenterConfiguration2.getLatestProductionStart(),
                skuId2,
                BigDecimal.valueOf(Random.Default.nextDouble()),
            )

        insertBuffer(
            DistributionCenterConfiguration.Companion.default("XX"),
            today,
            skuId1,
            BigDecimal.ONE,
        )

        val safetyStockBuffers =
            runBlocking {
                safetyStockBufferRepository.fetchSafetyStockBuffersFromWeek(
                    listOf(
                        DcWeek(today, distributionCenterConfiguration1.productionStart).value,
                        DcWeek(today, distributionCenterConfiguration2.productionStart).value,
                    ).min(),
                    setOf(distributionCenterConfiguration1, distributionCenterConfiguration2),
                )
            }

        with(
            safetyStockBuffers.getBuffer(
                sku1Buffer1.dcCode,
                sku1Buffer1.skuId,
                ProductionWeek(
                    sku1Buffer1.week,
                    distributionCenterConfiguration1.productionStart,
                    distributionCenterConfiguration1.zoneId,
                ),
            ),
        ) {
            assertEquals(sku1Buffer1.buffer, bufferPercentage)
        }

        with(
            safetyStockBuffers.getBuffer(
                sku1Buffer2.dcCode,
                sku1Buffer2.skuId,
                ProductionWeek(
                    sku1Buffer2.week,
                    distributionCenterConfiguration1.productionStart,
                    distributionCenterConfiguration1.zoneId,
                ),
            ),
        ) {
            assertEquals(sku1Buffer2.buffer, bufferPercentage)
        }

        with(
            safetyStockBuffers.getBuffer(
                sku2Buffer1.dcCode,
                sku2Buffer1.skuId,
                ProductionWeek(
                    sku2Buffer1.week,
                    distributionCenterConfiguration2.productionStart,
                    distributionCenterConfiguration2.zoneId,
                ),
            ),
        ) {
            assertEquals(sku2Buffer1.buffer, bufferPercentage)
        }
    }

    @Test
    fun `future safety stock buffers are fetched when there is no past weeks configurations`() {
        val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = DayOfWeek.TUESDAY,
                zoneId = ZoneOffset.UTC,
            )

        val today = LocalDate.now(ZoneOffset.UTC)
        val skuId = UUID.randomUUID()
        val sku1Buffer =
            insertBuffer(
                distributionCenterConfiguration,
                today.plusWeeks(3),
                skuId,
                BigDecimal(32343),
            )

        val safetyStockBuffers =
            runBlocking {
                safetyStockBufferRepository.fetchSafetyStockBuffersFromWeek(
                    DcWeek(today, distributionCenterConfiguration.productionStart).value,
                    setOf(distributionCenterConfiguration),
                )
            }

        with(
            safetyStockBuffers.getBuffer(
                sku1Buffer.dcCode,
                sku1Buffer.skuId,
                ProductionWeek(
                    sku1Buffer.week,
                    distributionCenterConfiguration.productionStart,
                    distributionCenterConfiguration.zoneId,
                ),
            ),
        ) {
            assertEquals(sku1Buffer.buffer, bufferPercentage)
        }
    }

    fun insertBuffer(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        date: LocalDate,
        skuId: UUID,
        bufferPercentage: BigDecimal,
    ) = insertBuffer(
        distributionCenterConfiguration.dcCode,
        distributionCenterConfiguration.productionStart,
        date,
        skuId,
        bufferPercentage,
    )

    fun insertBuffer(
        dcCode: String,
        productionStart: DayOfWeek,
        date: LocalDate,
        skuId: UUID,
        bufferPercentage: BigDecimal,
    ) =
        SafetyStockBufferRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.week = DcWeek(date, productionStart).toString()
            this.buffer = bufferPercentage
        }.also {
            dsl.batchInsert(it).execute()
        }
}
