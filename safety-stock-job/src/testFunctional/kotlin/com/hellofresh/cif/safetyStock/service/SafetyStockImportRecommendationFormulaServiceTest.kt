package com.hellofresh.cif.safetyStock.service

import com.hellofresh.cif.distributionCenter.models.DcConfigWeekRange
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockImportRecord
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.safetystock.toConfiguration
import com.hellofresh.cif.sqr.SQRConfiguration
import com.hellofresh.cif.sqr.SQRConfigurations
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.safetystock.service.SafetyStockImportRecommendationFormulaService
import com.hellofresh.safetystock.service.SafetyStockRiskFormulaService
import io.mockk.coEvery
import io.mockk.mockk
import java.util.Random
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SafetyStockImportRecommendationFormulaServiceTest : TestPrepare() {

    private val safetyStockMultiplierService = mockk<SafetyStockRiskFormulaService>()
    private val supplyQuantityRecommendationConfigRepository = mockk<SupplyQuantityRecommendationConfigRepository>()

    enum class SOURCE {
        SAFETY_STOCK_IMPORTS,
        RISK_MULTIPLIER
    }

    @ParameterizedTest
    @CsvSource(
        "true, SAFETY_STOCK_IMPORTS",
        "false, RISK_MULTIPLIER",
    )
    fun `calculates safety stock for skus with recommendation`(
        recommendation: Boolean,
        expectedSource: SOURCE
    ) {
        val skuId = UUID.randomUUID()

        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        val dcConfigs = setOf(defaultDcConfig)

        val safetyStockConfigurations = SafetyStockConfigurations(emptyList())

        val weekRange = defaultDcConfig.getCurrentWeek().let { it..it.plusWeeks(10) }

        val safetyStockRiskMultipliers = weekRange.map { week ->
            SafetyStock(
                dcCode = defaultDcCode,
                skuId = skuId,
                week = week.weekString,
                value = Random().nextLong(),
                configuration = safetyStockConfigurations.getConfiguration(
                    defaultDcCode,
                    skuId,
                    week
                ).toConfiguration(),
                strategy = defaultSafetyStockStrategy,
            )
        }

        coEvery {
            safetyStockMultiplierService.calculateSafetyStocks(dcConfigs, safetyStockConfigurations)
        } returns safetyStockRiskMultipliers

        coEvery {
            supplyQuantityRecommendationConfigRepository.fetchSupplyQuantityRecommendationConfigurations(
                weekRange.start.weekString,
                dcConfigs.toList(),
            )
        } returns
            SQRConfigurations(
                listOf(
                    SQRConfiguration(
                        dcCode = defaultDcCode,
                        skuId = skuId,
                        productionWeek = weekRange.first(),
                        recommendationEnabled = recommendation,
                        multiWeekEnabled = false,
                    ),
                ),
            )

        val safetyStockImports = insertSafetyStockImports(weekRange, skuId)

        val safetyStocks = runBlocking {
            SafetyStockImportRecommendationFormulaService(
                safetyStockImportRepository,
                supplyQuantityRecommendationConfigRepository,
                safetyStockMultiplierService,
            ).calculateSafetyStocks(dcConfigs, safetyStockConfigurations)
        }

        assertEquals(weekRange.count(), safetyStocks.size)

        when (expectedSource) {
            SOURCE.SAFETY_STOCK_IMPORTS -> {
                safetyStockImports.forEach { import ->
                    val safetyStock = safetyStocks.first {
                        it.dcCode == import.dcCode && it.skuId == import.skuId && it.week == import.week
                    }
                    assertEquals(
                        import.safetyStock,
                        safetyStock.value,
                    )
                    assertEquals(
                        safetyStockConfigurations.getConfiguration(
                            safetyStock.dcCode,
                            safetyStock.skuId,
                            ProductionWeek(safetyStock.week, defaultDcConfig.productionStart, defaultDcConfig.zoneId),
                        ).toConfiguration(),
                        safetyStock.configuration,
                    )
                }
            }

            SOURCE.RISK_MULTIPLIER -> {
                safetyStockRiskMultipliers.forEach { multiplier ->
                    assertEquals(
                        multiplier,
                        safetyStocks.first { it.dcCode == multiplier.dcCode && it.skuId == multiplier.skuId && it.week == multiplier.week },
                    )
                }
            }
        }
    }

    private fun insertSafetyStockImports(weekRange: DcConfigWeekRange, skuId: UUID?) =
        weekRange.mapIndexed { index, paramWeek ->
            SafetyStockImportRecord().apply {
                dcCode = defaultDcCode
                this.skuId = skuId
                week = paramWeek.weekString
                safetyStock = Random().nextLong()
            }
        }.also {
            dsl.batchInsert(it).execute()
        }
}
