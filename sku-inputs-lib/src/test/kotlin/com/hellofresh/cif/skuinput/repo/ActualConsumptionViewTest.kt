package com.hellofresh.cif.skuinput.repo

import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import com.hellofresh.cif.sku_inputs_lib.schema.enums.Uom
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.random.RandomGenerator
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ActualConsumptionViewTest : TestPrepare() {

    @Test
    fun `should fetch actual consumption by PK`() {
        // given
        val qty: Short = 11
        val market = defaultSku.second.market
        val skuCode = defaultSku.second.skuCode
        insertSkuSpecificationsWithSafetyStock(mapOf(defaultSku))
        persistPick2LightForToday(skuCode, market, qty)

        // when
        dsl.query("refresh materialized view ${Tables.ACTUAL_CONSUMPTION_VIEW.name}").execute()
        val resultAi = dsl.selectFrom(Tables.ACTUAL_CONSUMPTION_VIEW)
            .where(Tables.ACTUAL_CONSUMPTION_VIEW.SKU_ID.eq(defaultSku.first)).toList()

        // then
        assertEquals(1, resultAi.size)
        assertEquals(qty.toLong(), resultAi.first()[Tables.ACTUAL_CONSUMPTION_VIEW.QUANTITY].toLong())
    }

    @Test
    fun `should sum actual consumption quantities for the same sku, dc and date`() {
        // given
        val market = defaultSku.second.market
        val skuCode = defaultSku.second.skuCode
        insertSkuSpecificationsWithSafetyStock(mapOf(defaultSku))
        // insert first p2l entry for skuCode, market, today
        val qty1: Short = 2
        persistPick2LightForToday(skuCode, market, qty1)
        // insert second p2l entry for skuCode, market, today
        val qty2: Short = 5
        persistPick2LightForToday(skuCode, market, qty2)
        val qty3 = BigDecimal(6)
        persistActualConsumptionYf(qty3)

        // when
        dsl.query("refresh materialized view ${Tables.ACTUAL_CONSUMPTION_VIEW.name}").execute()
        val resultAi = dsl.selectFrom(Tables.ACTUAL_CONSUMPTION_VIEW)
            .where(Tables.ACTUAL_CONSUMPTION_VIEW.SKU_ID.eq(defaultSku.first)).toList()
            .sorted()

        assertEquals(2, resultAi.size)
        assertEquals((qty1 + qty2).toLong(), resultAi[1][Tables.ACTUAL_CONSUMPTION_VIEW.QUANTITY].toLong())
        assertEquals((qty3.toShort()).toLong(), resultAi[0][Tables.ACTUAL_CONSUMPTION_VIEW.QUANTITY].toLong())
    }

    private fun persistPick2LightForToday(skuCode: String, market: String, qty: Short) {
        with(Tables.PICK_2_LIGHT) {
            dsl.insertInto(this).columns(
                KAFKA_MESSAGE_KEY,
                CREATED_AT,
                QUANTITY,
                MARKET,
                DC_CODE,
                CSKU_CODE,
            ).values(
                RandomGenerator.getDefault().nextLong().toString(),
                OffsetDateTime.now(UTC),
                qty,
                market,
                "VE",
                skuCode,
            ).execute()
        }
    }

    private fun persistActualConsumptionYf(qty: BigDecimal) {
        with(Tables.ACTUAL_CONSUMPTION) {
            dsl.insertInto(this).columns(
                DC_CODE,
                SKU_ID,
                DATE,
                QUANTITY,
                UOM
            ).values(
                "VE",
                defaultSku.first,
                LocalDate.now().minusDays(1),
                qty,
                Uom.UOM_UNIT
            ).execute()
        }
    }
}
