package com.hellofresh.cif.skuinput.repo

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.enums.Uom
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.DemandRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SkuSpecificationYfRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierCulinarySkuRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierSkuPricingRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierSkuRecord
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl.Companion.toSkuUOM
import com.hellofresh.sku.models.SkuSpecification
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows

class SkuInputDataRepositoryTest {
    private val skuId = UUID.randomUUID()
    private val dcCode = "VE"
    private val skuSpecRecord = SkuSpecificationRecord().apply {
        id = skuId
        parentId = UUID.randomUUID()
        name = "Name"
        code = "SPI-0000"
        category = "SPI"
        acceptableCodeLife = 0
        coolingType = ""
        packaging = ""
        market = "dach"
        uom = Uom.UOM_LBS
    }
    private val dcConfig = DcConfigRecord(
        dcCode, "dach", DayOfWeek.TUESDAY.name, DayOfWeek.TUESDAY.name, "UTC", true,
        LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null,
        LocalTime.now(), emptyArray()
    )

    private val demandQty = BigDecimal(7L)
    private val demandRecord = DemandRecord(
        skuId,
        dcCode,
        LocalDate.now(),
        LocalDateTime.now(),
        LocalDateTime.now(),
        LocalDateTime.now(),
        null,
        demandQty,
        null,
        null,
        null,
        Uom.UOM_UNIT,
    )
    private val supplierId = UUID.randomUUID()
    private val supplierParentId = UUID.randomUUID()
    private val culinraySkuId = UUID.randomUUID()
    private val supplierSkuId = UUID.randomUUID()

    private val supplierRecord = SupplierRecord(
        supplierId,
        supplierParentId,
        "Name 1",
    )
    private val supplierCulinarySkuRecord = SupplierCulinarySkuRecord(
        supplierSkuId,
        supplierParentId,
        culinraySkuId,
        "dach",
        "active",
        OffsetDateTime.now(),
        OffsetDateTime.now(),
    )

    private val supplierSkuRecord = SupplierSkuRecord(
        supplierSkuId,
        skuId,
        10,
        OffsetDateTime.now(),
        OffsetDateTime.now(),
        "active",
    )

    private val supplierSkuPricingRecord = SupplierSkuPricingRecord(
        UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(), OffsetDateTime.now(), LocalDate.now(),
        LocalDate.now().plusMonths(10),
    )

    @BeforeEach
    fun initData() {
        dsl.batchInsert(skuSpecRecord, dcConfig).execute()
        refreshSkuView()
    }

    @AfterEach
    fun tearDown() {
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        dsl.deleteFrom(Tables.SKU_SPECIFICATION_YF).execute()
        refreshSkuView()
        dsl.deleteFrom(Tables.DEMAND).execute()
        dsl.deleteFrom(Tables.SUPPLIER).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU_PRICING).execute()
        dsl.deleteFrom(Tables.SUPPLIER_CULINARY_SKU).execute()
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    @Test
    fun `should fetch yf sku data from sku specification view`() {
        val skuSpecRecordYf = SkuSpecificationYfRecord().apply {
            id = UUID.randomUUID()
            parentId = UUID.randomUUID()
            name = "NameYf"
            code = "SPI-0000-yf"
            category = "AMB"
            acceptableCodeLife = 5
            coolingType = "C1"
            packaging = "P2"
            market = dcConfig.market
            uom = Uom.UOM_KG
        }
        dsl.batchInsert(
            skuSpecRecordYf,
        ).execute()
        refreshSkuView()

        // when
        runBlocking {
            val result = skuInputDataRepository.fetchSkuInputData(dcConfig.dcCode, skuSpecRecordYf.id)
            assertDcConfig(result)
            assertSkuSpecification(skuSpecRecordYf, result)
        }
    }

    @Test
    fun `should fetch sku data for a given sku`() {
        dsl.batchInsert(
            demandRecord,
        ).execute()
        dsl.query("refresh materialized view ${Tables.ACTUAL_CONSUMPTION_VIEW.name}").execute()

        // when
        runBlocking {
            val result = skuInputDataRepository.fetchSkuInputData(dcCode, skuId)
            assertDcConfig(result)
            assertSkuSpecification(skuSpecRecord, result)
        }
    }

    @Test
    fun `should fetch sku data for all sku by dc`() {
        val skuId2 = UUID.randomUUID()
        val skuSpecRecord2 = SkuSpecificationRecord().apply {
            id = skuId2
            parentId = UUID.randomUUID()
            name = "Name"
            code = "SPI-0002"
            category = "SPI"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            market = "dach"
            uom = Uom.UOM_LBS
        }

        dsl.batchInsert(demandRecord).execute()
        dsl.batchInsert(
            skuSpecRecord2,
            DemandRecord(
                skuId2,
                dcCode,
                LocalDate.now(),
                LocalDateTime.now(),
                LocalDateTime.now(),
                LocalDateTime.now(),
                null,
                demandQty,
                null,
                null,
                null,
                Uom.UOM_UNIT,
            ),
            // TODO: fix in CIF-2155
            // demandRecord.into(DemandRecord()).setSkuId(skuId2),
        ).execute()
        dsl.query("refresh materialized view ${Tables.ACTUAL_CONSUMPTION_VIEW.name}").execute()
        refreshSkuView()

        // when
        runBlocking {
            val result = skuInputDataRepository.fetchByDcs(setOf(dcCode))
            val expectedSkuIds = setOf(skuId, skuId2)
            assertEquals(expectedSkuIds, result.skuSpecification.keys)

            assertDcConfig(result)
            assertSkuSpecification(skuSpecRecord, result)
            assertSkuSpecification(skuSpecRecord2, result)
        }
    }

    @Test
    fun `should throw exception if requested sku is not found`() {
        runBlocking {
            assertThrows<IllegalArgumentException> {
                skuInputDataRepository.fetchSkuInputData(dcCode, UUID.randomUUID())
            }
        }
    }

    @Test
    fun `should throw exception if requested dcCode is not found`() {
        runBlocking {
            assertThrows<IllegalArgumentException> {
                skuInputDataRepository.fetchSkuInputData("XX", skuId)
            }
        }
    }

    @Test
    fun `returns requested sku ids`() {
        val skuId2 = UUID.randomUUID()
        val skuId3 = UUID.randomUUID()
        val skuSpecRecord2 = skuSpecRecord.into(Tables.SKU_SPECIFICATION)
            .apply {
                id = skuId2
                market = "EU"
            }
        val skuSpecRecord3 = skuSpecRecord.into(Tables.SKU_SPECIFICATION)
            .apply {
                id = skuId3
                market = "DE"
            }

        dsl.batchInsert(skuSpecRecord2, skuSpecRecord3).execute()
        refreshSkuView()

        // when
        runBlocking {
            val expectedSkuIds = setOf(skuId, skuId3)
            val result = skuInputDataRepository.fetchSkus(expectedSkuIds)

            assertEquals(expectedSkuIds, result.keys)

            assertSkuSpecification(skuSpecRecord, skuId, result[skuId]!!)
            assertSkuSpecification(skuSpecRecord3, skuId3, result[skuId3]!!)
        }
    }

    @Test
    fun `returns requested sku by market`() {
        val skuId2 = UUID.randomUUID()
        val skuId3 = UUID.randomUUID()
        val skuSpecRecord2 = skuSpecRecord.into(Tables.SKU_SPECIFICATION)
            .apply {
                id = skuId2
                market = "EU"
            }
        val skuSpecRecord3 = skuSpecRecord.into(Tables.SKU_SPECIFICATION)
            .apply {
                id = skuId3
                market = "DE"
            }

        dsl.batchInsert(skuSpecRecord2, skuSpecRecord3).execute()
        refreshSkuView()

        // when
        runBlocking {
            val expectedSkuIds = setOf(skuId, skuId2)
            val result = skuInputDataRepository.fetchSkus(expectedSkuIds)

            assertEquals(expectedSkuIds, result.keys)

            assertSkuSpecification(skuSpecRecord, skuId, result[skuId]!!)
            assertSkuSpecification(skuSpecRecord2, skuId2, result[skuId2]!!)
        }
    }

    @Test
    fun `returns sku lookup map by dcs`() {
        val skuId2 = UUID.randomUUID()
        val skuSpecRecord2 = skuSpecRecord.into(Tables.SKU_SPECIFICATION)
            .apply {
                id = skuId2
                market = "eu"
            }
        val dcConfig2 = dcConfig.into(Tables.DC_CONFIG)
            .apply {
                dcCode = "EU"
                market = "eu"
            }
        dsl.batchInsert(skuSpecRecord2, dcConfig2).execute()
        refreshSkuView()
        dcConfigService.fetchOnDemand()

        // when
        runBlocking {
            val result = skuInputDataRepository.fetchSkuLookUp(setOf(dcConfig.dcCode, dcConfig2.dcCode))

            assertEquals(2, result.size)

            with(result[DcSkuCodeKey(dcConfig.dcCode, skuSpecRecord.code)]!!) {
                assertSkuSpecification(skuSpecRecord, this.first, this.second)
            }
            with(result[DcSkuCodeKey(dcConfig2.dcCode, skuSpecRecord2.code)]!!) {
                assertSkuSpecification(skuSpecRecord2, this.first, this.second)
            }
        }
    }

    @Test
    fun `returns requested supplier sku by market`() {
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord,
            supplierSkuPricingRecord,
        ).execute()
        runBlocking {
            val result = skuInputDataRepository.fetchByDcs(setOf(dcCode))
            assertEquals(1, result.supplierSkuDetails.size)
            val supplierSku = result.supplierSkuDetails[skuId]?.first()
            assertEquals(supplierId, supplierSku?.supplierId)
            assertEquals(supplierSkuRecord.mlorDays, supplierSku?.mlor)
            assertEquals(10, supplierSku?.leadTimes?.first()?.leadTime)
        }
    }

    @Test
    fun `returns requested supplier sku with unique lead time`() {
        val supplierSkuPricingRecordOne = SupplierSkuPricingRecord(
            UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(), OffsetDateTime.now(), LocalDate.now(),
            LocalDate.now().plusMonths(10),
        )
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord,
            supplierSkuPricingRecord,
            supplierSkuPricingRecordOne,
        ).execute()
        runBlocking {
            val result = skuInputDataRepository.fetchByDcs(setOf(dcCode))
            assertEquals(1, result.supplierSkuDetails.size)
            val supplierSku = result.supplierSkuDetails[skuId]?.first()
            assertEquals(supplierId, supplierSku?.supplierId)
            assertEquals(supplierSkuRecord.mlorDays, supplierSku?.mlor)
            assertEquals(10, supplierSku?.leadTimes?.first()?.leadTime)
        }
    }

    private fun assertDcConfig(result: SkuInputData) {
        assertEquals(dcConfig.cleardown, result.dcConfig[dcConfig.dcCode]?.cleardown?.name)
        assertEquals(dcConfig.productionStart, result.dcConfig[dcConfig.dcCode]?.productionStart?.name)
        assertEquals(dcConfig.hasCleardown, result.dcConfig[dcConfig.dcCode]?.hasCleardown)
        assertEquals(dcConfig.enabled, result.dcConfig[dcConfig.dcCode]?.enabled)
        assertEquals(dcConfig.market, result.dcConfig[dcConfig.dcCode]?.market)
        assertEquals(dcConfig.zoneId, result.dcConfig[dcConfig.dcCode]?.zoneId?.id)
    }

    private fun assertSkuSpecification(skuSpecRecord: SkuSpecificationRecord, result: SkuInputData) {
        assertSkuSpecification(skuSpecRecord, skuSpecRecord.id, result.skuSpecification[skuSpecRecord.id]!!)
    }

    private fun assertSkuSpecification(
        skuSpecRecord: SkuSpecificationRecord,
        skuId: UUID,
        skuSpecification: SkuSpecification
    ) {
        assertEquals(skuSpecRecord.id, skuId)
        with(skuSpecification) {
            assertEquals(skuSpecRecord.name, name)
            assertEquals(skuSpecRecord.code, skuCode)
            assertEquals(skuSpecRecord.category, category)
            assertEquals(skuSpecRecord.parentId, parentId)
            assertEquals(skuSpecRecord.packaging, packaging)
            assertEquals(skuSpecRecord.acceptableCodeLife, acceptableCodeLife)
            assertEquals(skuSpecRecord.coolingType, coolingType)
            assertEquals(skuSpecRecord.market, market)
            assertEquals(skuSpecRecord.uom.toSkuUOM(), uom)
        }
    }

    private fun assertSkuSpecification(
        skuSpecRecordYf: SkuSpecificationYfRecord,
        result: SkuInputData
    ) {
        with(result.skuSpecification[skuSpecRecordYf.id]!!) {
            assertEquals(skuSpecRecordYf.name, name)
            assertEquals(skuSpecRecordYf.code, skuCode)
            assertEquals(skuSpecRecordYf.category, category)
            assertEquals(skuSpecRecordYf.parentId, parentId)
            assertEquals(skuSpecRecordYf.packaging, packaging)
            assertEquals(skuSpecRecordYf.acceptableCodeLife, acceptableCodeLife)
            assertEquals(skuSpecRecordYf.coolingType, coolingType)
            assertEquals(skuSpecRecordYf.market, market)
            assertEquals(skuSpecRecordYf.uom.toSkuUOM(), uom)
        }
    }

    companion object {
        lateinit var skuInputDataRepository: SkuInputDataRepository
        lateinit var dcConfigRepository: DcRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcConfigRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
            skuInputDataRepository = SkuInputDataRepositoryImpl(dsl, dcConfigService)
        }
    }
}
