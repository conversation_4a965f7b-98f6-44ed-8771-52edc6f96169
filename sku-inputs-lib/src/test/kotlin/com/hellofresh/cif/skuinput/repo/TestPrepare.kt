package com.hellofresh.cif.skuinput.repo

import InfraPreparation
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.DemandRecord
import com.hellofresh.sku.models.LeadTime
import com.hellofresh.sku.models.SupplierSkuDetail
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach

open class TestPrepare {

    val MINIMUM_ALLOWED_MLOR = 46
    val LEAD_TIME = 14
    val STD_DEV_LEAD_TIME_FACTOR = 0.2

    internal val defaultMarket = "MRKT"
    internal val defaultDcCode = "DC"

    internal val defaultSku = UUID.randomUUID() to
        SkuSpecificationWithSafetyStock(
            category = "PHF",
            skuCode = "PHF-00-00000-0",
            name = "s sku",
            coolingType = "aaa",
            packaging = "bbb",
            acceptableCodeLife = 2,
            market = "dach",
        )

    internal val defaultSafetyStockSku = defaultSku.first to defaultSku.second

    internal val defaultDcConfig = DistributionCenterConfiguration(
        dcCode = defaultDcCode,
        productionStart = LocalDate.now().dayOfWeek,
        cleardown = LocalDate.now().dayOfWeek,
        market = defaultMarket,
        zoneId = ZoneOffset.UTC,
        enabled = true,
        wmsType = WmsSystem.WMS_SYSTEM_FCMS,
    )

    @BeforeEach
    fun beforeEach() {
        persistDcConfig()
        dcConfigService.fetchOnDemand()
    }

    @AfterEach
    fun tearDown() {
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        dsl.deleteFrom(Tables.PICK_2_LIGHT).execute()
        dsl.deleteFrom(Tables.DEMAND).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU).execute()
        dsl.deleteFrom(Tables.SUPPLIER_CULINARY_SKU).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU_PRICING).execute()
        dsl.deleteFrom(Tables.SUPPLIER).execute()
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
    }

    fun persistDcConfig(
        dcCode: String = "VE",
        market: String = "DACH",
        zoneId: ZoneId = ZoneId.of("Europe/Berlin"),
        productionStartDay: DayOfWeek = MONDAY
    ) {
        val dcConfig = DcConfigRecord(
            dcCode, market, productionStartDay.name, "FRIDAY", zoneId.id,
            true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
            emptyArray()
        )
        dsl.batchInsert(dcConfig).execute()
    }

    fun insertSkuSpecificationsWithSafetyStock(skuSpecifications: Map<UUID, SkuSpecificationWithSafetyStock>) {
        skuSpecifications.forEach { (skuId, skuSpec) ->
            dsl.insertInto(
                Tables.SKU_SPECIFICATION,
                Tables.SKU_SPECIFICATION.ID,
                Tables.SKU_SPECIFICATION.PARENT_ID,
                Tables.SKU_SPECIFICATION.CATEGORY,
                Tables.SKU_SPECIFICATION.CODE,
                Tables.SKU_SPECIFICATION.NAME,
                Tables.SKU_SPECIFICATION.COOLING_TYPE,
                Tables.SKU_SPECIFICATION.PACKAGING,
                Tables.SKU_SPECIFICATION.ACCEPTABLE_CODE_LIFE,
                Tables.SKU_SPECIFICATION.MARKET,
            )
                .values(
                    skuId,
                    skuSpec.parentId,
                    skuSpec.category,
                    skuSpec.skuCode,
                    skuSpec.name,
                    skuSpec.coolingType,
                    skuSpec.packaging,
                    skuSpec.acceptableCodeLife,
                    skuSpec.market,
                )
                .execute()
        }
    }

    internal fun createSupplierDetails(leadTime: Int, mlor: Int) =
        listOf(
            SupplierSkuDetail(
                supplierId = UUID.randomUUID(),
                supplierName = "Supplier 1",
                mlor = mlor,
                leadTimes = listOf(LeadTime(leadTime, LocalDate.now().minusWeeks(2), LocalDate.now().plusWeeks(2))),
            ),
        )

    internal fun insertDefaultDemand() {
        insertDemand(
            listOf(
                createDemand(20023, defaultDcConfig.getLatestProductionStart()),
                createDemand(6453, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
                createDemand(9372, defaultDcConfig.getLatestProductionStart().plusWeeks(2)),
            ),
        )
    }

    internal fun createDemand(
        qty: Long,
        date: LocalDate = LocalDate.now(ZoneOffset.UTC),
        skuId: UUID = defaultSafetyStockSku.first,
        dcCode: String = defaultDcCode
    ) =
        DemandRecord().apply {
            this.skuId = skuId
            this.dcCode = dcCode
            this.date = date
            this.quantity = qty.toBigDecimal()
            this.recordTimestamp_ = LocalDateTime.now(ZoneOffset.UTC)
        }

    internal fun insertDemand(demands: List<DemandRecord>) {
        dsl.batchInsert(demands).execute()
    }

    companion object {
        val dataSource = InfraPreparation.getMigratedDataSource()
        lateinit var dsl: MetricsDSLContext
        lateinit var skuInputDataRepository: SkuInputDataRepository
        lateinit var dcConfigRepository: DcRepository
        lateinit var dcConfigService: DcConfigService

        @BeforeAll
        @JvmStatic
        fun setUp() {
            val defaultConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(
                defaultConfiguration,
            ).withMetrics(SimpleMeterRegistry())
            dcConfigRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
            skuInputDataRepository = SkuInputDataRepositoryImpl(dsl, dcConfigService)
        }
    }
}

data class SkuSpecificationWithSafetyStock(
    val coolingType: String,
    val name: String,
    val packaging: String,
    val skuCode: String,
    val category: String,
    val parentId: UUID? = null,
    val acceptableCodeLife: Int,
    val market: String,
)
