plugins {
    id("com.hellofresh.cif.common-conventions")
    alias(libs.plugins.jooq)
    alias(libs.plugins.gradle.docker.compose)
}

description = "DB library to query sku data like inventory, PO etc"
group = "$group.${project.name}"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "sku_specification|sku_specification_yf|sku_specification_view|uom" +
                            "|dc_config|pick_2_light" +
                            "|actual_consumption_view|demand|supplier_sku|supplier_culinary_sku" +
                            "|supplier|supplier_details_view|supplier_sku_pricing|actual_consumption"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(libs.coroutines.core)
    implementation(projects.distributionCenterLib)
    implementation(projects.lib.db)
    implementation(libs.apache.commons.math)
    api(projects.calculatorRule)
    api(projects.distributionCenterModels)
    api(projects.skuModels)

    testImplementation(libs.mockk)
    testImplementation(libs.ktor.test) {
        exclude("junit", "junit")
        exclude("ch.qos.logback", "logback-classic")
    }
    testImplementation(projects.libTests)

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
