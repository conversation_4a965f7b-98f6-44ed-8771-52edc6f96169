plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
    hellofresh.`test-integration`
}

description = "Service for listening to file notifications from SQS and processing the corresponding files from S3."
group = "$group.fileConsumer"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "stock_update|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}
dependencies {
    jooqGenerator(libs.postgresql.driver)
    implementation(libs.ktor.core)
    implementation(projects.lib.sqs)
    implementation(projects.lib.s3)
    implementation(projects.lib.fileConsumer)
    implementation(projects.lib.fileUpload)
    implementation(projects.lib.stockUpdate)
    implementation(projects.skuInputsLib)
    implementation(projects.skuSpecificationLib)
    implementation(projects.distributionCenterLib)
    implementation(projects.inventory.inventoryLib)

    implementation(libs.aws.java.sdk.s3)

    testImplementation(libs.mockk)
    testImplementation(projects.libTests)
}
