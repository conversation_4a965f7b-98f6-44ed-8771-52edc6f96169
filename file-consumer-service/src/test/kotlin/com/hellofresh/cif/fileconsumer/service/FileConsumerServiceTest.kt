package com.hellofresh.cif.fileconsumer.service

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.fileconsumer.service.service.StockUpdateFileProcessorService
import com.hellofresh.cif.fileconsumer.service.service.StockUpdateService
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.com.cif.business.fileupload.repository.FileUploadRepositoryImpl
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.mockk
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FileConsumerServiceTest {
    private lateinit var metricsDSLContext: MetricsDSLContext
    private lateinit var dcConfigService: DcConfigService
    private lateinit var s3Importer: S3Importer
    private lateinit var stockUpdateService: StockUpdateService
    private lateinit var fileUploadRepositoryImpl: FileUploadRepositoryImpl
    private lateinit var stockUpdateFileProcessorService: StockUpdateFileProcessorService

    @BeforeEach
    fun setup() {
        metricsDSLContext = DSL.using(
            DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            },
        ).withMetrics(SimpleMeterRegistry())

        s3Importer = mockk()
        stockUpdateService = mockk()
        fileUploadRepositoryImpl = mockk()

        val dcConfig = DistributionCenterConfiguration(
            dcCode = "BV",
            productionStart = LocalDate.now().dayOfWeek,
            cleardown = LocalDate.now().dayOfWeek,
            market = "UK",
            zoneId = ZoneOffset.UTC,
            enabled = true,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
        )

        dcConfigService = DcConfigService(
            SimpleMeterRegistry(), repo = {
                listOf(dcConfig)
            }
        )

        stockUpdateFileProcessorService = StockUpdateFileProcessorService(
            metricsDSLContext = metricsDSLContext,
            dcConfigService = dcConfigService,
            s3Importer = s3Importer,
            stockUpdateService = stockUpdateService,
            fileUploadRepositoryImpl = fileUploadRepositoryImpl,
        )
    }

    @Test
    fun `should process file from S3`() {
        // Given
        val bucket = "test-bucket"
        val key = "test-file.csv"
        val fileContent = "test,data\n1,2".toByteArray()
//        val s3File = S3File(bucket, key)

        coEvery {
            s3Importer.fetchObjectContent(bucket, key)
        } returns ByteArrayInputStream(fileContent)

        // When WIP

        // Then WIP
    }

    companion object {
        private val dataSource = InfraPreparation.getMigratedDataSource()
    }
}
