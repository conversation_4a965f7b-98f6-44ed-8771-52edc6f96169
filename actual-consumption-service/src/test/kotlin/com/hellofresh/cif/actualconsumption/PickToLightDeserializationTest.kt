package com.hellofresh.cif.actualconsumption

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.actualconsumption.model.Pick2Light
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

private const val PICK2LIGHT_FILE = "pick-2-light-data.json"

class PickToLightDeserializationTest {

    private val objectMapper = ObjectMapper().findAndRegisterModules()

    @Test
    fun `should be able to deserialize pick-2-light topic data`() {
        val pick2Light = objectMapper.readValue(
            getPick2LightFileData(),
            Pick2Light::class.java,
        )
        assertEquals("Milano", pick2Light.city)
        assertEquals(10, pick2Light.data.pickRow.quantity)
        assertEquals("DAI-10-10020-4", pick2Light.data.pickRow.cskuCode)
        assertEquals("2022-10-02T16:23:38.965", pick2Light.createdAt.toString())
    }

    private fun getPick2LightFileData() = this::class.java.classLoader.getResourceAsStream(
        PICK2LIGHT_FILE
    )?.readAllBytes()?.let {
        String(it, Charsets.UTF_8)
    }
}
