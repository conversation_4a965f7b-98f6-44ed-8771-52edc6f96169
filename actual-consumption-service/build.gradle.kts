plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
    hellofresh.`test-integration`
    alias(libs.plugins.gradle.docker.compose)
}

description = "Dumps Pick-2-Light information from Kafka topics to the Pick-2-Light Database"
group = "$group.actualConsumption"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "pick_2_light"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.lib)
    implementation(projects.distributionCenterLib)
    api(projects.lib.db)

    testRuntimeOnly(projects.inventoryDb)
    testImplementation(libs.mockk)
    testImplementation(libs.kafka.kafka213)
    testImplementation(testFixtures(projects.distributionCenterModels))

    testIntegrationImplementation(libs.jooq.core)
    testIntegrationImplementation(projects.lib)
    testIntegrationImplementation(projects.libTests)
    testIntegrationImplementation(libs.coroutines.core)

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
