package com.hellofresh.demand.models

import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.DemandType as ProtoDemandType

object DemandTypeMapper {
    fun mapDemandType(demandType: ProtoDemandType): DemandType =
        when (demandType) {
            ProtoDemandType.DEMAND_TYPE_FUMIGATED -> DemandType.FUMIGATED
            ProtoDemandType.DEMAND_TYPE_REGULAR -> DemandType.REGULAR
            ProtoDemandType.DEMAND_TYPE_UNSPECIFIED -> DemandType.UNSPECIFIED
            ProtoDemandType.UNRECOGNIZED -> DemandType.UNRECOGNIZED
        }
}
