package com.hellofresh.demand.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming

const val UNKNOWN_BRAND = "UNKNOWN"
const val HELLOFRESH_BRAND = "HF"
const val EVERYPLATE_BRAND = "EP"

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class ConsumptionDetails(
    val recipeBreakdowns: List<RecipeBreakdown>,
    val prekitting: Prekittings? = null,
    val substitutions: Substitutions? = null,
    val crossDockings: CrossDockings? = null,
) {
    companion object {
        val empty = ConsumptionDetails(
            recipeBreakdowns = listOf(),
            prekitting = null,
            substitutions = null,
            crossDockings = null,
        )
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class Prekittings(
    val `in`: List<Prekitting>,
    val out: List<Prekitting>
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class Prekitting(
    val qty: Long,
    val demandType: DemandType? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class Substitution(
    val brand: String,
    val recipeIndex: String?,
    val qty: Long,
    val demandType: DemandType? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class Substitutions(
    val `in`: List<Substitution>,
    val out: List<Substitution>
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class RecipeBreakdown(
    val brand: String,
    val recipeIndex: String,
    val qty: Long,
    val demandType: DemandType? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class CrossDockings(
    val `in`: List<CrossDocking> = emptyList(),
    val out: List<CrossDocking> = emptyList()
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonInclude(Include.NON_NULL)
data class CrossDocking(
    val dcCode: String,
    val action: CrossDockingAction,
    val quantity: Long
)

enum class CrossDockingAction {
    UNKNOWN,
    REPLACE,
    DUPLICATE
}

enum class DemandType(val value: String) {
    FUMIGATED("Fumigated"),
    REGULAR("Regular"),
    UNSPECIFIED("Unspecified"),
    UNRECOGNIZED("Unrecognized")
}
