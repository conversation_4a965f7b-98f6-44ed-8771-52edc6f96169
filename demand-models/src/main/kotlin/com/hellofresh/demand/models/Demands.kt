package com.hellofresh.demand.models

import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID

data class Demands(
    val demandList: List<Demand>
) {
    private val demandsMap: Map<DemandKey, Demand> = demandList.associateBy(
        keySelector = { DemandKey(it.skuId, it.dcCode, it.date) },
    )

    fun getDemand(skuId: UUID, dcCode: String, date: LocalDate, zoneId: ZoneId) = demandsMap[
        DemandKey(
            skuId = skuId,
            dcCode = dcCode,
            date = date,
        ),
    ]?.let {
        val forecastedQty = it.forecastedQty
        val actualDemand = if (date < LocalDate.now(zoneId) && it.actualConsumption.usable) {
            it.actualConsumption.actualConsumptionQty
        } else {
            forecastedQty
        }
        DemandValue(forecastedQty, it.actualConsumptionQty, actualDemand)
    }
}

data class Demand(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val forecastedQty: SkuQuantity,
    val actualConsumption: ActualConsumption = ActualConsumption(
        SkuQuantity.fromLong(0L, forecastedQty.unitOfMeasure),
        false,
    ),
) {

    val actualConsumptionQty: SkuQuantity
        get() = actualConsumption.actualConsumptionQty
}

data class ActualConsumption(
    val actualConsumptionQty: SkuQuantity,
    val usable: Boolean = true
)

data class DemandKey(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate
)

data class DemandValue(
    val forecastedQty: SkuQuantity,
    val actualConsumptionQty: SkuQuantity,
    val actualDemand: SkuQuantity
)
