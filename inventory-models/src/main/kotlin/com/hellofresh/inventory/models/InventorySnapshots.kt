package com.hellofresh.inventory.models

import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.inventorySnapshotInbounds
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventoryLookup
import java.time.LocalDate
import java.util.UUID

data class InventorySnapshots(
    val inventoryList: List<InventorySnapshot>,
    val liveInventoryList: List<LiveInventorySnapshot>,
    val cleardownData: List<CleardownData>,
    val inventoryActivities: List<InventoryActivity> = emptyList(),
) {

    val cleardownInventorySnapshotInbounds = cleardownData
        .associateBy({ it.dcCode }) { inventorySnapshotInbounds(it, inventoryActivities) }

    private val inventorySnapshotMap: Map<InventorySnapshotKey, SkuInventoryLookup> =
        inventoryList.flatMap { snapshot ->
            snapshot.skus.map {
                InventorySnapshotKey(
                    it.skuId,
                    snapshot.dcCode,
                    snapshot.snapshotTime.toLocalDate(),
                ) to SkuInventoryLookup(snapshot, it.inventory, false)
            }
        }.toMap()

    private val liveInventorySnapshotMap: Map<InventorySnapshotKey, SkuLiveInventoryLookup> =
        liveInventoryList.flatMap { snapshot ->
            snapshot.skus.map {
                InventorySnapshotKey(it.skuId, snapshot.dcCode, snapshot.snapshotTime.toLocalDate()) to
                    SkuLiveInventoryLookup(snapshot, it, false)
            }
        }.toMap()

    private val cleardownInventorySnapshotMap: Map<CleardownInventorySnapshotKey, SkuInventoryLookup> =
        cleardownData.flatMap { cleardownInventory ->
            cleardownInventory.snapshot?.skus?.map {
                CleardownInventorySnapshotKey(
                    it.skuId,
                    cleardownInventory.snapshot.dcCode,
                ) to SkuInventoryLookup(cleardownInventory.snapshot, it.inventory, true)
            }
                ?: emptyList()
        }.toMap()

    private val dcCleardown: Map<String, CleardownData> = cleardownData.associateBy { it.dcCode }

    fun getAvailableSkuDcs() =
        inventoryList.flatMap { snapshot -> snapshot.skus.map { it.skuId to snapshot.dcCode }.toSet() } +
            liveInventoryList.flatMap { snapshot -> snapshot.skus.map { it.skuId to snapshot.dcCode }.toSet() } +
            cleardownData.flatMap { cleardownSnapshot ->
                cleardownSnapshot.snapshot?.skus?.map { it.skuId to cleardownSnapshot.dcCode }?.toSet() ?: emptySet()
            }

    fun getLatestCleardown(dcCode: String) = dcCleardown[dcCode]
    fun getLatestCleardown(skuId: UUID, dcCode: String) = cleardownInventorySnapshotMap[
        CleardownInventorySnapshotKey(
            skuId = skuId,
            dcCode = dcCode,
        ),
    ]

    fun getInventory(skuId: UUID, dcCode: String, date: LocalDate) = inventorySnapshotMap[
        InventorySnapshotKey(
            skuId = skuId,
            dcCode = dcCode,
            date = date,
        ),
    ]

    fun getLiveInventory(skuId: UUID, dcCode: String, date: LocalDate) = liveInventorySnapshotMap[
        InventorySnapshotKey(
            skuId = skuId,
            dcCode = dcCode,
            date = date,
        ),
    ]
}

private data class InventorySnapshotKey(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate
)

private data class CleardownInventorySnapshotKey(
    val skuId: UUID,
    val dcCode: String,
)
