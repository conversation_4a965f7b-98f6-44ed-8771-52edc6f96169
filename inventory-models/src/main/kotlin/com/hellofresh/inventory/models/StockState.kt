package com.hellofresh.inventory.models

import org.apache.logging.log4j.kotlin.Logging

enum class StockState {
    STOCK_STATE_UNSPECIFIED,
    STOCK_STATE_ACTIVE,
    STOCK_STATE_ON_HOLD,
    STOCK_STATE_INCOMING,
    STOCK_STATE_LOST,
    STOCK_STATE_OUTSIDE,
    STOCK_STATE_GONE,
    STOCK_STATE_BOOKED_FOR_OUTBOUND,
    STOCK_STATE_TOTAL,
    UNKNOWN;

    companion object : Logging {

        fun parse(value: String) =
            runCatching {
                StockState.valueOf(value)
            }.onFailure { logger.error("StockState value is not valid for $value") }
                .getOrDefault(UNKNOWN)
    }
}
