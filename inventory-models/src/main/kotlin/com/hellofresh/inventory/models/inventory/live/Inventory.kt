package com.hellofresh.inventory.models.inventory.live

import com.hellofresh.inventory.models.Inventory
import java.time.LocalDateTime
import java.util.UUID

data class LiveInventorySnapshot(
    val dcCode: String,
    val snapshotId: UUID,
    val snapshotTime: LocalDateTime,
    val skus: List<SkuLiveInventory>
) {
    companion object
}

data class SkuLiveInventory(
    val skuId: UUID,
    val inventory: List<Inventory>,
    val cleardownTime: LocalDateTime? = null
) {
    companion object
}

data class SkuLiveInventoryLookup(
    private val liveInventorySnapshot: LiveInventorySnapshot,
    val skuLiveInventory: SkuLiveInventory,
    val cleardownInventory: Boolean
) {
    val inventoryTime: LocalDateTime
        get() = liveInventorySnapshot.snapshotTime
}
