package com.hellofresh.inventory.models

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import java.time.LocalTime

object InventoryCleardown {

    fun createInventorySnapshotsWithScheduledCleardown(
        dcConfigs: Map<String, DistributionCenterConfiguration>,
        inventoryList: List<InventorySnapshot>,
        liveInventoryList: List<LiveInventorySnapshot>
    ) =
        InventorySnapshots(
            inventoryList,
            liveInventoryList,
            dcConfigs.mapNotNull { (dcCode, dcConfig) ->
                if (dcConfig.hasCleardown) {
                    val cleardownDate = dcConfig.getLatestCleardown()
                    val inventoryDate = cleardownDate.minusDays(1)
                    val inventorySnapshot = inventoryList.firstOrNull {
                        it.dcCode == dcCode && it.snapshotTime.toLocalDate() == inventoryDate
                    }
                    CleardownData(
                        dcCode = dcCode,
                        cleardownTime = cleardownDate.atTime(LocalTime.MIDNIGHT),
                        cleardownMode = SCHEDULED,
                        snapshot = inventorySnapshot
                    )
                } else {
                    null
                }
            },
        )
}
