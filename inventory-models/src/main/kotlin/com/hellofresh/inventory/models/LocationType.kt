package com.hellofresh.inventory.models

import org.apache.logging.log4j.kotlin.Logging

enum class LocationType {
    LOCATION_TYPE_UNSPECIFIED,
    LOCATION_TYPE_STAGING,
    LOCATION_TYPE_PRODUCTION,
    LOCATION_TYPE_STORAGE,
    LOCATION_TYPE_QUARANTINE,
    LOCATION_TYPE_DONATIONS,
    LOCATION_TYPE_WASTE,
    LOCATION_TYPE_INBOUND,
    LOCATION_TYPE_OUTBOUND_INTERNAL_TRANSFER,
    LOCATION_TYPE_EXTERNAL_STORAGE,
    LOCATION_TYPE_RECEIVING_ERROR,
    LOCATION_TYPE_SUPERMARKET,
    LOCATION_TYPE_LOST,
    UNKNOWN;

    fun isStaging() = when (this) {
        LOCATION_TYPE_STAGING,
        LOCATION_TYPE_PRODUCTION,
        LOCATION_TYPE_SUPERMARKET -> true

        else -> false
    }

    fun isStorage() = !isStaging()

    fun isUsable() =
        when (this) {
            LOCATION_TYPE_STAGING,
            LOCATION_TYPE_PRODUCTION,
            LOCATION_TYPE_SUPERMARKET,
            LOCATION_TYPE_INBOUND,
            LOCATION_TYPE_STORAGE,
            LOCATION_TYPE_OUTBOUND_INTERNAL_TRANSFER,
            LOCATION_TYPE_UNSPECIFIED -> true

            else -> false
        }

    companion object : Logging {

        fun parse(value: String) =
            runCatching {
                LocationType.valueOf(value)
            }.onFailure { logger.error("LocationType value is not valid for $value") }
                .getOrDefault(UNKNOWN)
    }
}
