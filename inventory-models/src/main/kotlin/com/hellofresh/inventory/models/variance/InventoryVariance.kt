package com.hellofresh.inventory.models.variance

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.time.LocalDate
import java.util.UUID

data class InventoryVariance(
    val skuId: UUID,
    val dcCode: String,
    val dcWeek: String,
    val cleardownVariance: SkuQuantity,
    val liveVariance: SkuQuantity,
    val dailyInventoryVarianceData: List<DailyInventoryVarianceData>
) {
    val uom: SkuUOM
        get() {
            checkUOM()
            return cleardownVariance.unitOfMeasure
        }

    fun checkUOM() {
        dailyInventoryVarianceData.forEach { it.checkUOM() }
        require(cleardownVariance.unitOfMeasure == liveVariance.unitOfMeasure) {
            "Cleardown: ${cleardownVariance.unitOfMeasure} and live: ${liveVariance.unitOfMeasure} variance UOM must be the same for skuId: $skuId"
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class DailyInventoryVarianceData(
    val date: LocalDate,
    val inventoryQty: SkuQuantity?,
    val cleardownClosingStock: SkuQuantity,
    val liveClosingStock: SkuQuantity
) {
    fun checkUOM() = require(
        liveClosingStock.unitOfMeasure == cleardownClosingStock.unitOfMeasure &&
            inventoryQty?.let { it.unitOfMeasure == cleardownClosingStock.unitOfMeasure } ?: true,
    ) {
        "Cleardown:${cleardownClosingStock.unitOfMeasure} and live variance:${inventoryQty?.unitOfMeasure} UOM must be the same"
    }
}
