package com.hellofresh.inventory.models

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class SkuInventory(
    val skuId: UUID,
    val inventory: List<Inventory>
)

data class InventorySnapshot(
    val dcCode: String,
    val snapshotId: UUID,
    val snapshotTime: LocalDateTime,
    val skus: List<SkuInventory>
) {

    companion object
}

data class CleardownData(
    val dcCode: String,
    val cleardownTime: LocalDateTime,
    val cleardownMode: CleardownMode,
    val snapshot: InventorySnapshot?
) {
    val inboundRange = InboundRange(cleardownTime)
}

/**
 * The range of time that the inbound should be considered before cleadown.
 * It is needed as not all inbound is cleared down in some DCs.
 */
data class InboundRange(
    override val endInclusive: LocalDateTime
) : ClosedRange<LocalDateTime> {
    override val start: LocalDateTime
        get() = endInclusive.minusDays(1).with(LocalTime.MIDNIGHT)

    fun dates(): List<LocalDate> = generateSequence(start.toLocalDate()) { it.plusDays(1) }
        .takeWhile { it <= endInclusive.toLocalDate() }
        .toList()
}

data class SkuInventoryLookup(
    private val inventorySnapshot: InventorySnapshot,
    val inventory: List<Inventory>,
    val cleardownInventory: Boolean
) {
    val inventoryTime: LocalDateTime
        get() = inventorySnapshot.snapshotTime
}
