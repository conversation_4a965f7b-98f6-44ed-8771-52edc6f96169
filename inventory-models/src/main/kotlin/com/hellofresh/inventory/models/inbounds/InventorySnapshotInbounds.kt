package com.hellofresh.inventory.models.inbounds

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_FCMS
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_WMS_LITE
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.InventoryActivity
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.InboundSnapshotState.INBOUNDED
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.InboundSnapshotState.NON_INBOUNDED
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.InboundSnapshotState.UNKNOWN
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

private const val INBOUND_RANGE_HOURS = 24L

class InventorySnapshotInbounds private constructor(
    val dcCode: String,
    val inboundsRange: InboundRange,
    inventoryActivities: List<InventoryActivity>
) {

    private val activitiesBySku: Map<ActivitySkuPoKey, List<InventoryActivity>> =
        inventoryActivities
            .asSequence()
            .filter { inboundsRange.contains(it.activityTime.toLocalDateTime()) }
            .filter { it.dcCode == dcCode }
            .filter { it.poNumber != null }
            .groupBy { ActivitySkuPoKey(it.skuId, it.poNumber!!) }
            .mapValues { (_, activities) -> activities.sortedBy { it.activityTime } }

    constructor(inventorySnapshot: InventorySnapshot, inventoryActivities: List<InventoryActivity>) :
        this(inventorySnapshot.dcCode, InboundRange(inventorySnapshot.snapshotTime), inventoryActivities)

    fun isPurchaseOrderInbounded(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        skuId: UUID,
        poNumber: String
    ): InboundSnapshotState {
        require(
            distributionCenterConfiguration.dcCode == dcCode,
        ) { "Dc Configuration Details must match dc snapshot being evaluated for inbounds" }
        val activitiesMatching = activitiesBySku[ActivitySkuPoKey(skuId, poNumber)] ?: emptyList()

        // temporary fix
        if (distributionCenterConfiguration.market.uppercase() == "BENELUXFR") {
            return result(null)
        }

        return result(
            when (distributionCenterConfiguration.wmsType) {
                WMS_SYSTEM_FCMS -> isFcmsDeliveryInbounded(activitiesMatching)

                WMS_SYSTEM_WMS_LITE -> isWMSLDeliveryInbounded(activitiesMatching)
                else -> null
            },
        )
    }

    private fun isFcmsDeliveryInbounded(inventoryActivities: List<InventoryActivity>) =
        inventoryActivities
            .any {
                it.typeId == InventoryAdjustmentTypeId.USP.name ||
                    it.typeId == InventoryAdjustmentTypeId.REJ.name
            }

    private fun isWMSLDeliveryInbounded(inventoryActivities: List<InventoryActivity>): Boolean =
        inventoryActivities.any {
            it.typeId == InventoryAdjustmentTypeId.RCV.name
        } &&
            inventoryActivities.any { it is InventoryMovement }

    companion object {

        enum class InboundSnapshotState {
            INBOUNDED,
            NON_INBOUNDED,
            UNKNOWN
        }

        fun result(inbounded: Boolean?): InboundSnapshotState =
            inbounded?.let {
                when (inbounded) {
                    true -> INBOUNDED
                    false -> NON_INBOUNDED
                }
            } ?: UNKNOWN

        internal fun inventorySnapshotInbounds(cleardownData: CleardownData, inventoryActivities: List<InventoryActivity>) =
            InventorySnapshotInbounds(
                cleardownData.dcCode,
                calculateInboundTimeRange(cleardownData),
                inventoryActivities,
            )

        fun calculateInboundTimeRange(cleardownData: CleardownData): InboundRange =
            calculateInboundTimeRange(cleardownData.cleardownTime, cleardownData.snapshot?.snapshotTime)

        fun calculateInboundTimeRange(cleardownTime: LocalDateTime, snapshotTime: LocalDateTime?): InboundRange =
            InboundRange(listOfNotNull(cleardownTime, snapshotTime).min())
    }
}

private data class ActivitySkuPoKey(val skuId: UUID, val poNumber: String)

/**
 * The range of time that the inbound should be considered before cleadown.
 * It is needed as not all inbound is cleared down in some DCs.
 */
data class InboundRange(
    override val endInclusive: LocalDateTime
) : ClosedRange<LocalDateTime> {
    override val start: LocalDateTime
        get() = endInclusive.minusHours(INBOUND_RANGE_HOURS)

    fun dates(): List<LocalDate> = generateSequence(start.toLocalDate()) { it.plusDays(1) }
        .takeWhile { it <= endInclusive.toLocalDate() }
        .toList()
}
