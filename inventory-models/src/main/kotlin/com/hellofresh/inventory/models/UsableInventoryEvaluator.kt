package com.hellofresh.inventory.models

import com.github.benmanes.caffeine.cache.Caffeine
import com.hellofresh.cif.featureflags.Context.CATEGORY
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.NoAclUnusableInventory
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_DONATIONS
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_EXTERNAL_STORAGE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_LOST
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_OUTBOUND_INTERNAL_TRANSFER
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_RECEIVING_ERROR
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_WASTE
import com.hellofresh.sku.models.SkuSpecification
import java.time.Duration
import java.time.LocalDate
import org.apache.logging.log4j.kotlin.Logging

const val SHORT_SHELF_LIFE_UNUSABLE_REASON = "Short Shelf Life"

@SuppressWarnings("LongParameterList")
class UsableInventoryEvaluator(
    statsigFeatureFlagClient: StatsigFeatureFlagClient
) {

    // Flag at sku level are constantly evaluated
    // To avoid repetitive evaluations we can cache flag value during some time safely
    private val flagEvaluationCache = Caffeine.newBuilder()
        .expireAfterWrite(Duration.ofMinutes(1))
        .build<DisabledAclInventoryKey, Boolean> {
            statsigFeatureFlagClient.isEnabledFor(
                NoAclUnusableInventory(setOf(ContextData(DC, it.dc), ContextData(CATEGORY, it.skuCategory))),
            )
        }

    fun isUsable(
        dc: String,
        date: LocalDate,
        locationType: LocationType,
        expiryDate: LocalDate?,
        acl: Int,
        skuCategory: String
    ) =
        if (!locationType.isUsable()) {
            unusable(
                when (locationType) {
                    LOCATION_TYPE_QUARANTINE -> "Quarantine"
                    LOCATION_TYPE_DONATIONS -> "Charity"
                    LOCATION_TYPE_WASTE -> "Waste"
                    LOCATION_TYPE_OUTBOUND_INTERNAL_TRANSFER -> "DC Transfer"
                    LOCATION_TYPE_EXTERNAL_STORAGE -> "External Storage"
                    LOCATION_TYPE_RECEIVING_ERROR -> "Receiving Error"
                    LOCATION_TYPE_LOST -> "Lost"
                    else -> "Unknown"
                },
            )
        } else if (isExpired(dc, date, expiryDate, acl, skuCategory)) {
            unusable(SHORT_SHELF_LIFE_UNUSABLE_REASON)
        } else {
            usable
        }

    fun isExpired(
        dc: String,
        date: LocalDate,
        expiryDate: LocalDate?,
        acl: Int,
        skuCategory: String
    ) =
        expiryDate?.let {
            if (flagEvaluationCache[DisabledAclInventoryKey(dc, skuCategory)]) {
                expiryDate < date
            } else {
                expiryDate.minusDays(SkuSpecification.daysBeforeExpiry(acl)) < date
            }
        } ?: false

    fun isUsable(
        dc: String,
        date: LocalDate,
        inventory: Inventory,
        acl: Int,
        skuCategory: String
    ) = isUsable(dc, date, inventory.location.type, inventory.expiryDate, acl, skuCategory)

    companion object : Logging {

        private val usable = UsabilityDetails(true, null)
        private fun unusable(reason: String) = UsabilityDetails(false, reason)
    }
}

private data class DisabledAclInventoryKey(val dc: String, val skuCategory: String)

data class UsabilityDetails(val usable: Boolean, val unusableReason: String?)
