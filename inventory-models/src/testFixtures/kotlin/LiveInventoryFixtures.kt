package com.hellofresh.inventory.models.inventory.live

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.random
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random

fun LiveInventorySnapshot.Companion.default() = LiveInventorySnapshot(
    dcCode = "dc-code-fixture",
    snapshotTime = LocalDateTime.now(UTC),
    snapshotId = UUID.randomUUID(),
    skus = listOf(
        SkuLiveInventory(
            skuId = UUID.randomUUID(),
            inventory = listOf(
                Inventory(
                    qty = SkuQuantity.fromBigDecimal(BigDecimal(100)),
                    expiryDate = LocalDate.now(UTC).plusDays(10),
                    location = Location(
                        id = "location-id",
                        type = LOCATION_TYPE_STAGING,
                        transportModuleId = "transportModuleId",
                    ),
                    poReference = "2024701483_E01",
                ),
            ),
        ),
    ),
)

fun LiveInventorySnapshot.Companion.random() = with(Random(LocalDateTime.now().nano)) {
    LiveInventorySnapshot(
        dcCode = UUID.randomUUID().toString(),
        snapshotId = UUID.randomUUID(),
        snapshotTime = LocalDateTime.now(UTC),
        skus = listOf(
            SkuLiveInventory(
                skuId = UUID.randomUUID(),
                inventory = listOf(Inventory.random()),
            ),
        ),
    )
}
