package com.hellofresh.inventory.models

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.random.Random

fun Inventory.Companion.random(staging: Boolean? = null) = with(Random(LocalDateTime.now().nano)) {
    val stagingType = staging ?: nextBoolean()
    Inventory(
        qty = SkuQuantity.fromBigDecimal(BigDecimal(Random.nextDouble(0.0, 1000.0))),
        expiryDate = LocalDate.now().plusDays(10 + nextLong(100)),
        location = Location("", if (stagingType) LOCATION_TYPE_STAGING else LOCATION_TYPE_STORAGE, null),
        poReference = UUID.randomUUID().toString(),
    )
}
