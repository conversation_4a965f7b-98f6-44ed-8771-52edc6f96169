package com.hellofresh.cif.outputdemandforecast

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distribution_center_lib.schema.Tables
import com.hellofresh.cif.distribution_center_lib.schema.tables.records.DcConfigRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.unmockkAll
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach

open class TestPrepare {

    @BeforeEach
    fun setup() {
        val dcs = listOf(
            mapOf("VE" to mapOf("market" to "DE", "enabled" to false)),
            mapOf("BV" to mapOf("market" to "DE", "enabled" to true)),
            mapOf("NJ" to mapOf("market" to "US", "enabled" to false)),
        )

        val expected = dcs.map { dcMap ->
            val (dcCode1, dcConfig1) = dcMap.entries.first()
            val market1 = dcConfig1["market"] as String
            val enabled1 = dcConfig1["enabled"] as Boolean

            DcConfigRecord().apply {
                dcCode = dcCode1
                cleardown = MONDAY.name
                productionStart = FRIDAY.name
                market = market1
                zoneId = UTC.id
                hasCleardown = true
                enabled = enabled1
                createdAt = LocalDateTime.now(UTC)
                updatedAt = LocalDateTime.now(UTC)
                recordTimestamp_ = LocalDateTime.now(UTC)
            }
        }
        metricsDSLContext.batchInsert(expected).execute()
    }

    @AfterEach
    fun tearDown() {
        metricsDSLContext.deleteFrom(Tables.DC_CONFIG).execute()
        unmockkAll()
    }

    companion object {
        private val dataSource = getMigratedDataSource()
        private val meterRegistry = SimpleMeterRegistry()
        private val dbConfiguration = DefaultConfiguration()
            .apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
        val metricsDSLContext = DSL.using(dbConfiguration).withMetrics(meterRegistry)
    }
}
