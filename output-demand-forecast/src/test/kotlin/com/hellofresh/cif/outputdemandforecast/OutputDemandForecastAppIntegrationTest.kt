package com.hellofresh.cif.outputdemandforecast

import InfraPreparation.createKafkaConsumer
import InfraPreparation.createKafkaProducer
import InfraPreparation.startKafkaAndCreateTopics
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.calculator.models.inventoryCalculationsTopic
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.StopPublishingDemandsForClients
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.serde
import com.hellofresh.cif.outputdemandforecast.service.DEMAND_FORECAST_TOPIC_NAME
import com.hellofresh.cif.outputdemandforecast.service.OutputDemandForecastService
import com.hellofresh.cif.outputdemandforecast.service.OutputDemandForecastService.Companion.convertToUOM
import com.hellofresh.cif.outputdemandforecast.service.OutputDemandForecastService.Companion.toProtoDecimal
import com.hellofresh.dateUtil.models.toProtoDate
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Duration
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.shaded.org.awaitility.Awaitility
import random

class OutputDemandForecastAppIntegrationTest : TestPrepare() {

    @Test
    fun `should consume from calculation topic and process records`() {
        // given
        val skuId = UUID.randomUUID()

        val key = CskuInventoryForecastKey(skuId, UUID.randomUUID().toString(), LocalDate.now(UTC))
        val value = CskuInventoryForecastVal.Companion.random()
        val record = ProducerRecord(calculationsTopicName, key, value)
        // when
        calculationTopicProducer.send(record)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                OutputDemandForecastApp(
                    meterRegistry,
                    pollConfig,
                    consumerConfig,
                    producerConfig,
                    statsigFeatureFlagClient,
                    dcConfigService = dcConfigService1
                ).runApp()
            }

            val records = mutableListOf<ConsumerRecord<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    val consumerRecords = calculationOutputTopicConsumer.poll(Duration.ofMillis(100))
                    records.addAll(consumerRecords)
                    records.count() == 1
                }
            assertKey(key, records.first().key())
            assertValue(value, records.first().value())

            app.cancel()
        }
    }

    @Test
    fun `should consume from calculation topic and should NOT publish records for disabled dcs`() {
        // given
        statsigFeatureFlagClient.fixtures = setOf(
            StopPublishingDemandsForClients(
                setOf(
                    ContextData(MARKET, "US"),
                    ContextData(DC, "VE"),
                ),
            ),
        )
        val skuId = UUID.randomUUID()

        val disabledKey = CskuInventoryForecastKey(skuId, "VE", LocalDate.now(UTC))
        val disabledValue = CskuInventoryForecastVal.Companion.random()
        val disabledKey2 = CskuInventoryForecastKey(skuId, "NJ", LocalDate.now(UTC))
        val disabledValue2 = CskuInventoryForecastVal.Companion.random()
        val enabledKey = CskuInventoryForecastKey(skuId, "BV", LocalDate.now(UTC))
        val enabledValue = CskuInventoryForecastVal.Companion.random()

        val record1 = ProducerRecord(calculationsTopicName, disabledKey, disabledValue)
        val record2 = ProducerRecord(calculationsTopicName, enabledKey, enabledValue)
        val record3 = ProducerRecord(calculationsTopicName, disabledKey2, disabledValue2)

        // when
        calculationTopicProducer.send(record1)
        calculationTopicProducer.send(record2)
        calculationTopicProducer.send(record3)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                OutputDemandForecastApp(
                    meterRegistry,
                    pollConfig,
                    consumerConfig,
                    producerConfig,
                    statsigFeatureFlagClient,
                    dcConfigService = dcConfigService1
                ).runApp()
            }

            val records = mutableListOf<ConsumerRecord<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    val consumerRecords = calculationOutputTopicConsumer.poll(Duration.ofMillis(100))
                    records.addAll(consumerRecords)
                    records.count() == 1
                }
            assertKey(enabledKey, records.first().key())
            assertValue(enabledValue, records.first().value())

            app.cancel()
        }
    }

    @Test
    fun `calculation from same sku id are sent to same partition`() {
        // given
        val skuId = UUID.randomUUID()

        repeat(100) {
            val key = CskuInventoryForecastKey(skuId, UUID.randomUUID().toString(), LocalDate.now(UTC))
            val value = CskuInventoryForecastVal.Companion.random()
            calculationTopicProducer.send(ProducerRecord(calculationsTopicName, key, value))
        }
        calculationTopicProducer.flush()

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                OutputDemandForecastApp(
                    meterRegistry,
                    pollConfig,
                    consumerConfig,
                    producerConfig,
                    statsigFeatureFlagClient = statsigFeatureFlagClient,
                    dcConfigService = dcConfigService1,
                    OutputDemandForecastService(
                        meterRegistry,
                        OutputDemandForecastApp.createKafkaProducer(producerConfig),
                        0,
                        statsigFeatureFlagClient = statsigFeatureFlagClient,
                        dcConfigService = dcConfigService1,
                    ),
                ).runApp()
            }

            val records = mutableListOf<ConsumerRecord<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(30))
                .until {
                    val consumerRecords = calculationOutputTopicConsumer.poll(Duration.ofMillis(100))
                    records.addAll(consumerRecords)
                    records.count() == 100
                }
            assertEquals(100, records.count())
            val firstPartition = records.first().partition()
            records.forEach {
                assertEquals(skuId.toString(), it.key().skuId)
                assertEquals(firstPartition, it.partition())
            }
            assertEquals(1, records.map { it.partition() }.distinct().count())

            app.cancel()
        }
    }

    companion object {
        private lateinit var calculationOutputTopicConsumer:
            KafkaConsumer<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>
        private lateinit var calculationTopicProducer: KafkaProducer<CskuInventoryForecastKey, CskuInventoryForecastVal>
        private lateinit var calculationTopicConsumer: KafkaConsumer<CskuInventoryForecastKey, CskuInventoryForecastVal>
        private lateinit var consumerConfig: Map<String, String>
        private lateinit var producerConfig: Map<String, String>
        private lateinit var pollConfig: PollConfig

        private lateinit var kafka: KafkaContainer
        private lateinit var statsigFeatureFlagClient: StatsigTestFeatureFlagClient

        private val calculationsTopicName = inventoryCalculationsTopic.name
        private val meterRegistry = SimpleMeterRegistry()
        private var dcConfigService1: DcConfigService = DcConfigService(
            meterRegistry,
            DcRepositoryImpl(metricsDSLContext)
        )

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = startKafkaAndCreateTopics(
                listOf(calculationsTopicName),
            )

            calculationOutputTopicConsumer = createKafkaConsumer(
                "randomTest",
                DEMAND_FORECAST_TOPIC_NAME, serde<SkuInventoryDemandForecastKey>().deserializer(), serde<SkuInventoryDemandForecastVal>().deserializer(),
            )
            calculationTopicProducer = createKafkaProducer(
                kafka,
                serde<CskuInventoryForecastKey>().serializer(),
                serde<CskuInventoryForecastVal>().serializer(),
            )
            calculationTopicConsumer = createKafkaConsumer(
                "random", calculationsTopicName,
                serde<CskuInventoryForecastKey>().deserializer(), serde<CskuInventoryForecastVal>().deserializer(),
            )
            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.output-demand-forecast-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            producerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                )
            pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )

            statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
        }

        fun assertKey(sourceKey: CskuInventoryForecastKey, outputKey: SkuInventoryDemandForecastKey) {
            assertEquals(sourceKey.cskuId.toString(), outputKey.skuId)
            assertEquals(sourceKey.dcCode, outputKey.distributionCenterBobCode)
            assertEquals(sourceKey.date.toProtoDate(), outputKey.date)
        }

        fun assertValue(sourceValue: CskuInventoryForecastVal, outputValue: SkuInventoryDemandForecastVal) {
            assertEquals(sourceValue.demanded.toProtoDecimal(), outputValue.forecastedDemandedQty)
            assertEquals(sourceValue.dailyNeeds.toProtoDecimal(), outputValue.forecastedNeededQty)
            assertEquals(
                sourceValue.productionWeekStartStock.toProtoDecimal(),
                outputValue.productionWeekStartStock,
            )
            with(DcWeek(sourceValue.productionWeek)) {
                assertEquals(week, outputValue.productionWeek.week)
                assertEquals(year, outputValue.productionWeek.year)
            }
            assertEquals(sourceValue.uom.convertToUOM(), outputValue.unit)
        }
    }
}
