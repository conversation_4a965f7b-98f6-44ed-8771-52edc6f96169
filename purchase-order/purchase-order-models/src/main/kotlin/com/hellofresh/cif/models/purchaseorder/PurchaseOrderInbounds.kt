package com.hellofresh.cif.models.purchaseorder

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuQuantity.Companion.ZERO
import com.hellofresh.cif.models.sum
import com.hellofresh.cif.models.sumOf
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.UUID

data class Inbounds(
    val pos: List<PoInbounds>
) {

    val expectedPoNumbers: Set<String>
        get() = pos.mapNotNull { it.expectedPoNumber }.toSet()

    val actualPoNumbers: Set<String>
        get() = pos.flatMap { d -> d.deliveries.map { it.sourcePo.number } }.toSet()

    val actualQuantity: SkuQuantity
        get() = pos.sumOf { it.actualQuantity }

    val remainingExpectedQuantity: SkuQuantity
        get() = pos.sumOf { it.remainingExpectedQuantity ?: ZERO }

    val poQuantity: SkuQuantity
        get() = pos.mapNotNull { it.poQuantity }.sum()
}

data class PoInbounds(
    val sourcePo: PurchaseOrder,
    val sourcePoSku: PurchaseOrderSku,
    val expectedPurchaseOrderSku: PurchaseOrderSku?,
    val remainingExpectedQuantity: SkuQuantity?,
    val deliveries: List<DeliveryPoInbound>,
) {

    val expectedPoNumber: String?
        get() = expectedPurchaseOrderSku?.let { sourcePo.number }

    val expectedExpiryDate: LocalDate?
        get() = expectedPurchaseOrderSku?.expiryDate

    val actualQuantity: SkuQuantity = deliveries.sumOf { it.actualQuantity }

    val poQuantity: SkuQuantity?
        get() = expectedPurchaseOrderSku?.expectedQuantity

    companion object
}

data class DeliveryPoInbound(
    val sourcePo: PurchaseOrder,
    val deliveryTime: LocalDateTime,
    val actualQuantity: SkuQuantity,
    val actualExpiryDate: LocalDate?
) {
    companion object
}

data class PurchaseOrderInbounds(val pos: List<PurchaseOrder>) {

    private val purchaseOrdersBySkuDc: Map<SkuPoKey, List<SkuPoValue>> by lazy {
        pos.asSequence()
            .filter { po -> po.poStatus.isUsable() }
            .flatMap { po ->
                po.purchaseOrderSkus.map { skuPo -> SkuPoKey(skuPo.skuId, po.dcCode) to SkuPoValue(po, skuPo) }
            }.groupBy({ it.first }, { it.second })
    }

    fun inbounds(skuId: UUID, dcCode: String, date: LocalDate): Inbounds =
        inbounds(skuId, dcCode, listOf(date))

    /**
     * Returns the inbounds for a given SKU and DC with deliveries on the given dates.
     */
    @SuppressWarnings("MagicNumber", "LongMethod")
    fun inbounds(skuId: UUID, dcCode: String, dates: List<LocalDate>): Inbounds {
        val skuPos = purchaseOrdersBySkuDc[SkuPoKey(skuId, dcCode)] ?: emptyList()
        return skuPos
            .map { (po, purchaseOrderSku) ->
                var expectedPurchaseOrderSku: PurchaseOrderSku? = null
                var remainingExpectedQuantity: SkuQuantity? = null

                val deliveries = purchaseOrderSku.deliveries.filter {
                    it.deliveryTime.toLocalDate() in dates
                }.let {
                    it.map { delivery ->
                        DeliveryPoInbound(
                            po,
                            delivery.deliveryTime.toLocalDateTime(),
                            delivery.quantity,
                            delivery.expiryDate,
                        )
                    }
                }

                if (po.expectedDeliveryTimeslot != null && po.expectedDeliveryTimeslot.expectedDeliveryDate in dates) {
                    expectedPurchaseOrderSku = purchaseOrderSku

                    if (purchaseOrderSku.deliveries.hasPendingDeliveries() &&
                        isPoStillExpected(po.expectedDeliveryTimeslot.startTime)
                    ) {
                        remainingExpectedQuantity = SkuQuantity.max(
                            ZERO,
                            (purchaseOrderSku.expectedQuantity ?: ZERO).minus(purchaseOrderSku.deliveries.sumOf { it.quantity }),
                        )
                    }
                }

                Inbounds(
                    pos = if (expectedPurchaseOrderSku != null || deliveries.isNotEmpty()) {
                        listOf(
                            PoInbounds(
                                po,
                                purchaseOrderSku,
                                expectedPurchaseOrderSku,
                                remainingExpectedQuantity,
                                deliveries,
                            ),
                        )
                    } else {
                        emptyList()
                    },
                )
            }.fold(
                Inbounds(listOf()),
            ) { acc, cur ->
                Inbounds(
                    acc.pos + cur.pos,
                )
            }
    }

    fun purchaseOrdersCutOffQuantity(poInbounds: List<PoInbounds>, poCutoffTime: LocalTime): SkuQuantity {
        val pos = purchaseOrdersCutOff(poInbounds, poCutoffTime)
        return purchaseOrdersCutOffQuantity(pos)
    }

    fun purchaseOrdersCutOffQuantity(poInbounds: List<PoInbounds>): SkuQuantity =
        poInbounds.mapNotNull {
            if (it.actualQuantity != ZERO) {
                it.actualQuantity
            } else {
                it.expectedPurchaseOrderSku?.expectedQuantity
            }
        }.sum()

    fun purchaseOrdersCutOff(poInbounds: List<PoInbounds>, poCutoffTime: LocalTime): List<PoInbounds> = poInbounds
        .filter {
            val slot = it.sourcePo.expectedDeliveryTimeslot ?: return@filter false
            slot.startTime.toLocalTime().isBefore(poCutoffTime) && (
                poCutoffTime.isBefore(slot.endTime.toLocalTime()) ||
                    slot.endTime.toLocalTime().isBefore(poCutoffTime)
                )
        }

    private fun List<DeliveryInfo>.hasPendingDeliveries() =
        this.isEmpty() || this.any { DeliveryInfoStatus.OPEN == it.state }

    private fun isPoStillExpected(startTime: ZonedDateTime) =
        LocalDateTime.now(startTime.zone).isBefore(
            startTime.toLocalDateTime().with(EXPECTED_LOCAL_TIME_LIMIT),
        )

    private data class SkuPoKey(val skuId: UUID, val dcCode: String)
    private data class SkuPoValue(val po: PurchaseOrder, val skuPo: PurchaseOrderSku)

    companion object {
        val EXPECTED_LOCAL_TIME_LIMIT: LocalTime = LocalTime.of(23, 30)
    }
}
