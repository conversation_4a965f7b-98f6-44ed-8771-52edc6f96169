package com.hellofresh.cif.models.purchaseorder

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.OPEN
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PoStatus.PLANNED
import com.hellofresh.cif.models.purchaseorder.PoStatus.SENT
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class PurchaseOrderStatusTest {

    private val skuId = UUID.randomUUID()

    @Test
    fun `a PO should have status DELIVERY_OPEN if at least one delivery is OPEN and the PO delivery date is not elapsed`() {
        // given
        val po = Po(LocalDate.now().plusDays(1), OPEN, skuId).create()

        // then
        assertEquals(PurchaseOrderStatus.DELIVERY_OPEN, po.status(skuId))
    }

    @Test
    fun `a PO should have status OVERDUE if at least one delivery is OPEN and the PO delivery date had elapsed`() {
        // given
        val po = Po(LocalDate.now().minusDays(1), OPEN, skuId).create()

        // then
        assertEquals(PurchaseOrderStatus.OVERDUE, po.status(skuId))
    }

    @Test
    fun `a PO should have status DELIVERED if all deliveries are CLOSED and the PO delivery date had elapsed`() {
        // given
        val po = Po(LocalDate.now().minusDays(1), CLOSED, skuId).create()

        // then
        assertEquals(PurchaseOrderStatus.DELIVERED, po.status(skuId))
    }

    @Test
    fun `a PO should have status NOT_RECEIVED if there is no delivery and the PO delivery date had elapsed`() {
        // given
        val po = Po(LocalDate.now().minusDays(1), null, skuId).create()

        // then
        assertEquals(PurchaseOrderStatus.OVERDUE, po.status(skuId))
    }

    @Test
    fun `a PO should have status ASN_RECIEVED if there is no delivery and the PO delivery date has not elapsed`() {
        // given
        val po = Po(LocalDate.now().plusDays(1), null, skuId)
            .withSupplier(newSupplier())
            .withAsns(listOf(newAsn()))
            .create()

        // then
        assertEquals(PurchaseOrderStatus.ASN_RECEIVED, po.status(skuId))
    }

    @Test
    fun `the status for a sku in a PO having multiple skus should reflect that sku only`() {
        // given
        val skuWithClosedDelivery = PurchaseOrderSku(
            UUID.randomUUID(),
            SkuQuantity.fromLong(11),
            listOf(DeliveryInfo(UUID.randomUUID().toString(), ZonedDateTime.now(UTC), CLOSED, SkuQuantity.fromLong(9))),
        )
        val otherPoSkuOpenDelivery = PurchaseOrderSku(
            UUID.randomUUID(),
            SkuQuantity.fromLong(11),
            listOf(DeliveryInfo(UUID.randomUUID().toString(), ZonedDateTime.now(UTC), OPEN, SkuQuantity.fromLong(11))),
        )
        val po = Po(LocalDate.now().minusDays(1), CLOSED, skuId)
            .create()
            .copy(purchaseOrderSkus = listOf(skuWithClosedDelivery, otherPoSkuOpenDelivery))

        // then
        assertEquals(PurchaseOrderStatus.DELIVERED, po.status(skuWithClosedDelivery.skuId))
        assertEquals(PurchaseOrderStatus.OVERDUE, po.status(otherPoSkuOpenDelivery.skuId))
    }

    @ParameterizedTest
    @EnumSource(PoStatus::class)
    fun `a PO should return its status if is still expected and does not have asn or grn info`(poStatus: PoStatus) {
        // given
        val poSku = PurchaseOrderSku(
            UUID.randomUUID(),
            SkuQuantity.fromLong(11),
            emptyList(),
        )
        val po = Po(LocalDate.now().plusDays(1), null, skuId)
            .withStatus(poStatus)
            .create()
            .copy(purchaseOrderSkus = listOf(poSku))

        // then
        assertEquals(
            when (poStatus) {
                PLANNED -> PurchaseOrderStatus.PLANNED
                SENT -> PurchaseOrderStatus.SENT
                APPROVED -> PurchaseOrderStatus.APPROVED
            },
            po.status(poSku.skuId),
        )
    }

    @Test
    fun `a planned PO should still be planned even if it has grns or asns`() {
        // given
        val poSku = PurchaseOrderSku(
            UUID.randomUUID(),
            SkuQuantity.fromLong(11),
            listOf(DeliveryInfo(UUID.randomUUID().toString(), ZonedDateTime.now(UTC), OPEN, SkuQuantity.fromLong(11))),
        )

        val po = Po(LocalDate.now().plusDays(1), null, skuId)
            .withStatus(PLANNED)
            .withSupplier(newSupplier())
            .withAsns(listOf(newAsn()))
            .create()
            .copy(purchaseOrderSkus = listOf(poSku))

        // then
        assertEquals(PurchaseOrderStatus.PLANNED, po.status(poSku.skuId))
    }

    private inner class Po(
        val expectedDeliveryDate: LocalDate,
        val deliveryInfoStatus: DeliveryInfoStatus?,
        val skuId: UUID
    ) {

        var supplier: Supplier? = null
        var asns: List<Asn> = emptyList()
        var poStatus: PoStatus = SENT

        fun withSupplier(supplier: Supplier) = apply {
            this.supplier = supplier
        }

        fun withAsns(asns: List<Asn>) = apply {
            this.asns = asns
        }

        fun withStatus(poStatus: PoStatus) = apply {
            this.poStatus = poStatus
        }

        fun create() = PurchaseOrder(
            "POR", "POR1", null, "VE",
            expectedDeliveryTimeslot = expectedDeliveryDate.let {
                TimeRange(
                    it.atStartOfDay(UTC),
                    it.atStartOfDay(UTC).plusMinutes(1),
                )
            },
            supplier = supplier,
            listOf(
                PurchaseOrderSku(
                    skuId,
                    SkuQuantity.fromLong(1),
                    deliveryInfoStatus?.let {
                        listOf(DeliveryInfo(UUID.randomUUID().toString(), ZonedDateTime.now(), deliveryInfoStatus, SkuQuantity.fromLong(1)))
                    }
                        ?: emptyList(),
                ),
            ),
            asns = asns,
            poStatus = poStatus,
        )
    }

    private fun newSupplier() = Supplier(UUID.randomUUID(), "aSupplier")
    private fun newAsn() =
        Asn(
            "anId",
            UUID.randomUUID(),
            ZonedDateTime.now(),
            SkuQuantity.fromLong(1),
        )
}
