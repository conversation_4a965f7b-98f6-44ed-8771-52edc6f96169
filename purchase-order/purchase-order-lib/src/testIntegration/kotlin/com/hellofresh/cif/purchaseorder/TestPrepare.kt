package com.hellofresh.cif.purchaseorder

import InfraPreparation
import InfraPreparation.getDataSourceConfig
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context.CATEGORY
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.DisableExpiryUnusable
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.purchase_order_lib.schema.Tables.ADVANCED_SHIPPING_NOTICE
import com.hellofresh.cif.purchase_order_lib.schema.Tables.ADVANCED_SHIPPING_NOTICE_SKU
import com.hellofresh.cif.purchase_order_lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.purchase_order_lib.schema.Tables.GOODS_RECEIVED_NOTE
import com.hellofresh.cif.purchase_order_lib.schema.Tables.PURCHASE_ORDER
import com.hellofresh.cif.purchase_order_lib.schema.Tables.PURCHASE_ORDER_SKU
import com.hellofresh.cif.purchase_order_lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.purchase_order_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.purchase_order_lib.schema.Tables.SUPPLIER
import com.hellofresh.cif.purchase_order_lib.schema.Tables.SUPPLIER_CULINARY_SKU
import com.hellofresh.cif.purchase_order_lib.schema.Tables.SUPPLIER_SKU
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeRecord
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeSkuRecord
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.SkuSpecificationRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach

private const val DEFAULT_CATEGORY = "PHF"
private const val DEFAULT_DC = "IE"

open class TestPrepare {
    lateinit var dsl: MetricsDSLContext
    lateinit var purchaseOrderRepository: PurchaseOrderRepository
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
        setOf(
            DisableExpiryUnusable(setOf(ContextData(DC, DEFAULT_DC), ContextData(CATEGORY, DEFAULT_CATEGORY))),
        ),
    )

    @AfterEach
    fun tearDown() {
        dsl.deleteFrom(PURCHASE_ORDER_SKU).execute()
        dsl.deleteFrom(ADVANCED_SHIPPING_NOTICE_SKU).execute()
        dsl.deleteFrom(ADVANCED_SHIPPING_NOTICE).execute()
        dsl.deleteFrom(PURCHASE_ORDER).execute()
        dsl.deleteFrom(GOODS_RECEIVED_NOTE).execute()
        dsl.deleteFrom(SUPPLIER_SKU).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        refreshSkuView()
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    @BeforeEach
    fun setUp() {
        with(getDataSourceConfig()) {
            System.setProperty("HF_INVENTORY_DB_HOST", hostName)
            System.setProperty("HF_INVENTORY_READONLY_DB_USERNAME", userName)
            System.setProperty("HF_INVENTORY_READONLY_DB_PASSWORD", password)
        }
        val defaultConfiguration = DefaultConfiguration()
            .apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
        dsl = DSL.using(
            defaultConfiguration,
        ).withMetrics(SimpleMeterRegistry())

        purchaseOrderRepository = PurchaseOrderRepositoryImpl(
            dsl,
            DcConfigService(
                SimpleMeterRegistry(),
            ),
            statsigFeatureFlagClient,
        )
        persistDcConfig()
    }

    fun persistGoodsReceivedNotes(goodsReceivedNotes: Set<GoodsReceivedNoteSku>) {
        goodsReceivedNotes
            .forEach { grnRecord ->
                with(GOODS_RECEIVED_NOTE) {
                    dsl.insertInto(this).columns(
                        SKU_ID, DC_CODE, DELIVERY_ID, DELIVERY_TIME, QUANTITY, DELIVERY_STATUS, PO_REF, PO_NUMBER, EXPIRY_DATE,
                    ).values(
                        grnRecord.skuId,
                        grnRecord.dcCode,
                        grnRecord.deliveryId,
                        grnRecord.deliveryDate,
                        grnRecord.quantity.toBigDecimal(),
                        grnRecord.grnStatus.name,
                        grnRecord.poRef,
                        grnRecord.poNumber,
                        grnRecord.expiryDate,
                    ).execute()
                }
            }
        refreshView()
    }

    fun persistSkuSpecification(skuId: UUID, market: String, category: String) {
        dsl.executeInsert(
            SkuSpecificationRecord().apply {
                id = skuId
                name = UUID.randomUUID().toString()
                code = UUID.randomUUID().toString()
                this.market = market
                this.category = category
                acceptableCodeLife = 0
                coolingType = ""
                packaging = ""
                uom = Uom.UOM_LBS
            },
        )
        refreshSkuView()
    }

    fun persistSupplierSkus(supplierSkuId: UUID, skuId: UUID, mlor: Int, status: String) =
        with(SUPPLIER_SKU) {
            dsl.insertInto(this).columns(
                SUPPLIER_SKU_ID,
                SKU_ID,
                MLOR_DAYS,
                STATUS,
                CREATED_AT,
            ).values(
                supplierSkuId,
                skuId,
                mlor,
                status,
                OffsetDateTime.now(UTC),
            ).execute()
        }

    fun persistSupplierCulinarySku(id: UUID, culinarySkuId: UUID, supplierId: UUID, market: String, status: String) =
        with(SUPPLIER_CULINARY_SKU) {
            dsl.insertInto(this).columns(
                ID,
                SUPPLIER_ID,
                CULINARY_SKU_ID,
                MARKET,
                STATUS,
            ).values(
                id,
                supplierId,
                culinarySkuId,
                market,
                status,
            ).execute()
        }

    fun persistSupplier(id: UUID, name: String, parentId: UUID) =
        with(SUPPLIER) {
            dsl.insertInto(this).columns(
                ID,
                NAME,
                PARENT_ID,
            ).values(
                id,
                name,
                parentId,
            ).execute()
        }

    @Suppress("LongParameterList")
    fun createGrn(
        dcCode: String = "VE",
        poNumber: String = "2305DH046273",
        poRef: String = "2305DH046273_01",
        skuId: UUID,
        deliveryId: String = UUID.randomUUID().toString(),
        deliveryDate: OffsetDateTime = OffsetDateTime.now(UTC),
        quantity: Long = 9,
        deliveryInfoStatus: DeliveryInfoStatus,
        expiryDate: LocalDate? = null
    ) = GoodsReceivedNoteSku(
        dcCode = dcCode,
        poNumber = poNumber,
        poRef = poRef,
        skuId = skuId,
        deliveryId = deliveryId,
        deliveryDate = deliveryDate,
        quantity = quantity,
        expiryDate = expiryDate,
        grnStatus = deliveryInfoStatus,
    )

    fun persistPurchaseOrderAndRefreshPoInfoViewWithoutSupplier(
        purchaseOrderRecords: List<PurchaseOrderRecord>
    ) {
        purchaseOrderRecords.forEach {
            persistPurchaseOrder(it)
            persistPurchaseOrderSku(it)
        }
        refreshView()
    }

    fun persistPurchaseOrderAndRefreshPoInfoView(
        purchaseOrderRecord: PurchaseOrderRecord
    ) = persistPurchaseOrderAndRefreshPoInfoView(listOf(purchaseOrderRecord))

    fun persistPurchaseOrderAndRefreshPoInfoView(
        purchaseOrderRecords: List<PurchaseOrderRecord>
    ) {
        purchaseOrderRecords.forEach {
            persistPurchaseOrder(it)
            persistPurchaseOrderSku(it)
            dsl.query("insert into supplier(id, name) values(?,?)", it.supplierId, it.supplierName).execute()
        }
        refreshView()
    }

    fun persistPurchaseOrderAsn(
        advancedShippingNoticeRecord: AdvancedShippingNoticeRecord,
        advancedShippingNoticeSkuRecords: List<AdvancedShippingNoticeSkuRecord>,
    ) {
        with(advancedShippingNoticeRecord) {
            dsl.query(
                "insert into advanced_shipping_notice(asn_id, po_number, po_id, dc_code, " +
                    "planned_delivery_time,supplier_id, created_at, updated_at) " +
                    "values(?,?,?,?,(?::timestamp),?,(?::timestamp),(?::timestamp))",
                asnId, poNumber, poId, dcCode,
                plannedDeliveryTime, supplierId, OffsetDateTime.now(ZoneId.of("UTC")),
                OffsetDateTime.now(ZoneId.of("UTC")),
            ).execute()
        }
        advancedShippingNoticeSkuRecords.forEach { asnSku ->
            dsl.query(
                "insert into advanced_shipping_notice_sku(asn_id, sku_id, shipped_quantity,created_at, updated_at)" +
                    "values(?,?,?,(?::timestamp),(?::timestamp))",
                asnSku.asnId,
                asnSku.skuId,
                asnSku.shippedQuantity,
                OffsetDateTime.now(ZoneId.of("UTC")),
                OffsetDateTime.now(ZoneId.of("UTC")),
            ).execute()
        }
    }

    private fun persistPurchaseOrderSku(it: PurchaseOrderRecord) {
        it.skus.forEach { sku ->
            dsl.query(
                "insert into purchase_order_sku(po_number, sku_id, quantity) values(?,?,?)",
                it.poNumber,
                sku.skuId,
                sku.quantity,
                sku.quantity,
            ).execute()
        }
    }

    private fun persistPurchaseOrder(it: PurchaseOrderRecord) {
        dsl.query(
            "insert into purchase_order(po_ref, po_id, po_number, dc_code, " +
                "expected_arrival_start_time,expected_arrival_end_time, supplier_id, supplier_name) " +
                "values(?,?,?,?,(?::timestamptz),(?::timestamptz),?,?)",
            it.poRef,
            it.poId,
            it.poNumber,
            it.dcCode,
            it.expectedDateStartTime,
            it.expectedDateEndTime,
            it.supplierId,
            it.poSupplierName,
        ).execute()
    }

    fun persistDcConfig(dcCode: String = "VE", market: String = "DACH", zoneId: String = "Europe/Berlin") {
        val dcConfig = DcConfigRecord(
            dcCode, market, "MONDAY", "FRIDAY", zoneId,
            true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
            emptyArray()
        )
        dsl.batchInsert(dcConfig).execute()
    }

    private fun refreshView() = dsl.query("refresh materialized view PURCHASE_ORDERS_VIEW").execute()

    companion object {
        val dataSource = InfraPreparation.getMigratedDataSource(nestedFolderCount = 2)
    }
}

@Suppress("LongParameterList")
fun PurchaseOrder.assertPurchaseOrder(
    grn: GoodsReceivedNoteSku?,
    po: PurchaseOrderRecord? = null,
    mlorDays: Int? = null,
    expectedSupplierName: String? = po?.supplierName
) {
    Assertions.assertEquals(po?.poNumber ?: grn?.poNumber, this.number)
    Assertions.assertEquals(po?.poRef ?: grn?.poRef, this.poReference)
    Assertions.assertEquals(
        grn?.deliveryId,
        this.purchaseOrderSkus.flatMap { it.deliveries }.map { it.id }.firstOrNull(),
    )
    Assertions.assertEquals(poId, this.poId)
    Assertions.assertEquals(po?.dcCode ?: grn?.dcCode, this.dcCode)
    Assertions.assertEquals(po?.skus?.firstOrNull()?.skuId ?: grn?.skuId, this.purchaseOrderSkus.first().skuId)
    Assertions.assertEquals(
        po?.expectedDateStartTime?.toLocalDate(),
        this.expectedDeliveryTimeslot?.expectedDeliveryDate,
    )
    Assertions.assertEquals(expectedSupplierName, this.supplier?.name)

    Assertions.assertEquals(
        if (po?.skus?.firstOrNull()?.quantity == null) 0 else po.skus.first().quantity,
        this.purchaseOrderSkus.firstOrNull()?.expectedQuantity?.getValue()?.toLong()
    )

    Assertions.assertEquals(
        mlorDays?.run { po?.expectedDateStartTime?.toLocalDate()?.plusDays(mlorDays.toLong() - 1L) },
        this.purchaseOrderSkus.first().expiryDate,
    )

    grn?.also {
        Assertions.assertEquals(
            it.deliveryDate.toLocalDate(),
            this.purchaseOrderSkus.first().deliveries.first().deliveryTime.toLocalDate(),
        )
        Assertions.assertEquals(
            mlorDays?.run { it.deliveryDate.plusDays(mlorDays.toLong() - 1L) }?.toLocalDate(),
            this.purchaseOrderSkus.first().deliveries.first().expiryDate,
        )
        Assertions.assertEquals(
            it.quantity,
            this.purchaseOrderSkus.first().deliveries.first().quantity.getValue().toLong()
        )
        Assertions.assertEquals(it.grnStatus.name, this.purchaseOrderSkus.first().deliveries.first().state.name)
    }
}

data class GoodsReceivedNoteSku(
    val dcCode: String,
    val poNumber: String,
    val poRef: String,
    val skuId: UUID,
    val deliveryId: String,
    val deliveryDate: OffsetDateTime,
    val quantity: Long,
    val expiryDate: LocalDate?,
    val grnStatus: DeliveryInfoStatus
)

data class PurchaseOrderRecord(
    val poRef: String,
    val poId: UUID = UUID.randomUUID(),
    val poNumber: String = UUID.randomUUID().toString(),
    val dcCode: String,
    val expectedDateStartTime: OffsetDateTime = OffsetDateTime.now(UTC),
    val expectedDateEndTime: OffsetDateTime = OffsetDateTime.now(UTC).plusHours(2),
    val supplierId: UUID = UUID.randomUUID(),
    val supplierName: String = UUID.randomUUID().toString(),
    val poSupplierName: String? = null,
    val skus: List<PurchaseOrderSkuRecord>
) {
    constructor(
        poRef: String,
        poId: UUID = UUID.randomUUID(),
        poNumber: String = UUID.randomUUID().toString(),
        dcCode: String,
        expectedDateStartTime: OffsetDateTime = OffsetDateTime.now(UTC),
        expectedDateEndTime: OffsetDateTime = OffsetDateTime.now(UTC).plusHours(2),
        supplierId: UUID = UUID.randomUUID(),
        supplierName: String = UUID.randomUUID().toString(),
        skuId: UUID,
        quantity: Long,
        poSupplierName: String? = null,
    ) :
        this(
            poRef,
            poId,
            poNumber,
            dcCode,
            expectedDateStartTime,
            expectedDateEndTime,
            supplierId,
            supplierName,
            poSupplierName,
            listOf(PurchaseOrderSkuRecord(skuId, quantity)),
        )
}

data class PurchaseOrderSkuRecord(
    val skuId: UUID,
    val quantity: Long,
)
