package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeRecord
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeSkuRecord
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class PurchaseOrderInboundVarianceRepositoryImplTest : TestPrepare() {
    private val dcCode = "VE"
    private val skuId = UUID.randomUUID()
    private val expectedArrivalStartTime = OffsetDateTime.now(UTC)
    private val expectedArrivalEndTime = expectedArrivalStartTime.plusHours(2)
    private val poRef = "2305DH046273_01"
    private val poId = UUID.randomUUID()
    private val poNumber = UUID.randomUUID().toString()
    private val poQuantity = 12345L
    private val asnId: String = "ASN12345"
    private val supplierId = UUID.randomUUID()
    private val pastDate1 = OffsetDateTime.now(UTC).withHour(5).withMinute(59).minusDays(1)
    private val pastDate2 = OffsetDateTime.now(UTC).withHour(5).withMinute(59).minusDays(20)
    private val pastDate3NotInRange = OffsetDateTime.now(UTC).withHour(23).withMinute(59).minusDays(22)
    private val defaultPurchaseOrder = PurchaseOrderRecord(
        poRef, poId, poNumber, dcCode, expectedArrivalStartTime,
        expectedArrivalEndTime,
        UUID.randomUUID(),
        UUID.randomUUID().toString(),
        skuId,
        poQuantity,
    )

    private val defaultPurchaseOrderAsn = AdvancedShippingNoticeRecord(
        asnId,
        poNumber,
        poId,
        dcCode,
        expectedArrivalStartTime.minusDays(1),
        supplierId,
        OffsetDateTime.now(ZoneId.of("UTC")),
        OffsetDateTime.now(ZoneId.of("UTC")),
    )

    private val defaultPurchaseOrderAsnSku = AdvancedShippingNoticeSkuRecord(
        asnId,
        skuId,
        poQuantity.toBigDecimal(),
        OffsetDateTime.now(ZoneId.of("UTC")),
        OffsetDateTime.now(ZoneId.of("UTC")),
        Uom.UOM_UNIT,
    )

    @Test
    fun `PoService returns list of purchase orders for the given dc and dc week dates to calculate inbound variance`() {
        persistSkuSpecification(skuId, "test", "TST")
        val grnPastDate1 = createGrn(
            deliveryDate = pastDate1,
            dcCode = dcCode,
            poNumber = poNumber,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        val poPastDate1 =
            defaultPurchaseOrder.copy(expectedDateStartTime = pastDate1)

        val poPastDate2 = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poNumber = "PO2",
            poRef = "PO2_2",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate2,
        )
        val grnPastDate2 = grnPastDate1.copy(
            deliveryDate = pastDate2,
            poNumber = poPastDate2.poNumber,
            poRef = poPastDate2.poRef
        )

        val poPastDate3NotInRange = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poNumber = "PO3",
            poRef = "PO3_3",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate3NotInRange,
        )
        val defaultPurchaseOrderAsnSku1 = createAsnSkuRecord(
            10000L,
            UUID.randomUUID(),
        )
        val defaultPurchaseOrderAsnSku2 = createAsnSkuRecord(
            20000L,
            UUID.randomUUID(),
        )
        val grnPastDate3NotInRange =
            grnPastDate1.copy(
                deliveryDate = pastDate3NotInRange,
                poNumber = poPastDate3NotInRange.poNumber,
                poRef = poPastDate3NotInRange.poRef,
            )

        persistPurchaseOrderAndRefreshPoInfoView(listOf(poPastDate1, poPastDate2, poPastDate3NotInRange))
        persistGoodsReceivedNotes(setOf(grnPastDate1, grnPastDate2, grnPastDate3NotInRange))
        persistPurchaseOrderAsn(
            defaultPurchaseOrderAsn,
            listOf(
                defaultPurchaseOrderAsnSku,
                defaultPurchaseOrderAsnSku1,
                defaultPurchaseOrderAsnSku2,
            ),
        )

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPoVariance(
                setOf(dcCode),
                setOf(
                    DateRange(pastDate1.toLocalDate(), pastDate1.toLocalDate()),
                ),
            )
        }

        // then
        assertEquals(1, purchaseOrders.size)
        val purchaseOrderForInboundVariance = purchaseOrders.first()
        assertEquals(poRef, purchaseOrderForInboundVariance.poReference)
        assertEquals(
            poQuantity,
            purchaseOrderForInboundVariance.poSkuVariances.first().expectedQuantity?.getValue()?.toLong()
        )
        assertEquals(
            9L,
            purchaseOrderForInboundVariance.poSkuVariances.first().deliveredQuantity?.getValue()?.toLong()
        )
        assertEquals(3, purchaseOrderForInboundVariance.asns.size)
        assertEquals(defaultPurchaseOrderAsnSku.asnId, purchaseOrderForInboundVariance.asns.first().id)
    }

    @Test
    fun `PoService returns list of purchase orders for the given dc and dc week dates with null poSkuVariance quanity`() {
        persistSkuSpecification(skuId, "test", "TST")
        val grnPastDate1 = createGrn(
            deliveryDate = pastDate1,
            dcCode = dcCode,
            poNumber = "AN-001",
            poRef = poRef,
            skuId = skuId,
            quantity = 0,
            deliveryInfoStatus = CLOSED,
        )
        val poPastDate1 = PurchaseOrderRecord(
            poRef,
            poId,
            poNumber,
            dcCode,
            expectedArrivalStartTime,
            expectedArrivalEndTime,
            UUID.randomUUID(),
            UUID.randomUUID().toString(),
            skuId,
            0,
        )

        val defaultOrderAsnSku1 = AdvancedShippingNoticeSkuRecord(
            asnId,
            UUID.randomUUID(),
            0.toBigDecimal(),
            OffsetDateTime.now(ZoneId.of("UTC")),
            OffsetDateTime.now(ZoneId.of("UTC")),
            Uom.UOM_UNIT,
        )

        persistPurchaseOrderAndRefreshPoInfoView(listOf(poPastDate1))
        persistGoodsReceivedNotes(setOf(grnPastDate1))
        persistPurchaseOrderAsn(
            defaultPurchaseOrderAsn,
            listOf(
                defaultOrderAsnSku1,
            ),
        )

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPoVariance(
                setOf(dcCode),
                setOf(
                    DateRange(pastDate1.toLocalDate(), pastDate1.toLocalDate()),
                ),
            )
        }

        // then
        val purchaseOrderForInboundVariance = purchaseOrders.first()
        assertEquals(
            null,
            purchaseOrderForInboundVariance.poSkuVariances.first().expectedQuantity
        )
    }

    private fun createAsnSkuRecord(shippedQuantity: Long, skuId: UUID) = AdvancedShippingNoticeSkuRecord(
        asnId,
        skuId,
        shippedQuantity.toBigDecimal(),
        OffsetDateTime.now(UTC),
        OffsetDateTime.now(ZoneId.of("UTC")),
        Uom.UOM_UNIT,
    )
}
