package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PoStatus.PLANNED
import com.hellofresh.cif.models.purchaseorder.PoStatus.SENT
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus.Accepted
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus.Planned
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus.Sent

internal fun PurchaseOrderStatus?.toPoStatus() =
    this?.let { status ->
        when (status) {
            Planned -> PLANNED
            Sent -> SENT
            Accepted -> APPROVED
        }
    } ?: SENT
