package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.PoVariance
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import java.util.UUID

interface PurchaseOrderRepository {
    /** Returning the purchase orders without asns even though asns exist, will have to refactor it. **/
    suspend fun findPurchaseOrders(dcCodes: Set<String>, dateRange: DateRange): List<PurchaseOrder>

    /** Returning the purchase orders without asns even though asns exist, will have to refactor it. **/
    suspend fun findPurchaseOrders(poNrs: Set<String>): List<PurchaseOrder>
    suspend fun findPurchaseOrdersWithAsns(skuId: UUID, dcCodes: Set<String>, dateRanges: Set<DateRange>):
        List<PurchaseOrder>

    suspend fun findPoVariance(dcCodes: Set<String>, dateRanges: Set<DateRange>): List<PoVariance>
}
