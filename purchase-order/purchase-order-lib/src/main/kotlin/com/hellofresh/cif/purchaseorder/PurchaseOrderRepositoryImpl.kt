package com.hellofresh.cif.purchaseorder

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.purchase_order_lib.schema.Tables.ADVANCED_SHIPPING_NOTICE
import com.hellofresh.cif.purchase_order_lib.schema.Tables.ADVANCED_SHIPPING_NOTICE_SKU
import com.hellofresh.cif.purchase_order_lib.schema.tables.PurchaseOrdersView.PURCHASE_ORDERS_VIEW
import com.hellofresh.cif.purchase_order_lib.schema.tables.SkuSpecificationView.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeRecord
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeSkuRecord
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import java.util.concurrent.Executors
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Table
import org.jooq.impl.DSL.exists
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.inline
import org.jooq.impl.DSL.name
import org.jooq.impl.DSL.select

class PurchaseOrderRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
) : PurchaseOrderRepository {

    private val purchaseOrdersInfoByDcDuration = "purchase-order-by-dc"
    private val purchaseOrdersByPoRefs = "purchase-order-by-porefs"
    private val purchaseOrdersByDateRange = "purchase-order-by-date-range"
    private val purchaseOrdersByDateRangeForInboundVariance = "purchase-order-by-date-range-for-inbound-variance"
    private val asnsByPoIds = "asns-by-po-ids"
    private val coroutineDispatcher = Executors.newFixedThreadPool(
        1,
        ThreadFactoryBuilder().setNameFormat("producer-thread-%d").build(),
    ).asCoroutineDispatcher()

    private fun MetricsDSLContext.selectPurchaseOrderViewAndSkuSpecification() = select(
        PURCHASE_ORDERS_VIEW.PO_ID,
        PURCHASE_ORDERS_VIEW.PO_NUMBER,
        PURCHASE_ORDERS_VIEW.PO_REF,
        PURCHASE_ORDERS_VIEW.PO_SKU_ID,
        PURCHASE_ORDERS_VIEW.PO_DC_CODE,
        PURCHASE_ORDERS_VIEW.PO_SUPPLIER_ID,
        PURCHASE_ORDERS_VIEW.PO_SUPPLIER_NAME,
        PURCHASE_ORDERS_VIEW.PO_QUANTITY,
        PURCHASE_ORDERS_VIEW.PO_UOM,
        PURCHASE_ORDERS_VIEW.PO_EXPECTED_ARRIVAL_START_TIME,
        PURCHASE_ORDERS_VIEW.PO_EXPECTED_ARRIVAL_END_TIME,
        PURCHASE_ORDERS_VIEW.PO_MLOR,
        PURCHASE_ORDERS_VIEW.PO_DC_LOCAL_EXPECTED_ARRIVAL_START_TIME,
        PURCHASE_ORDERS_VIEW.PO_STATUS,
        PURCHASE_ORDERS_VIEW.GRN_SKU_ID,
        PURCHASE_ORDERS_VIEW.GRN_PO_NUMBER,
        PURCHASE_ORDERS_VIEW.GRN_PO_REF,
        PURCHASE_ORDERS_VIEW.GRN_DC_CODE,
        PURCHASE_ORDERS_VIEW.GRN_QUANTITY,
        PURCHASE_ORDERS_VIEW.GRN_UOM,
        PURCHASE_ORDERS_VIEW.GRN_EXPIRY_DATE,
        PURCHASE_ORDERS_VIEW.GRN_MLOR,
        PURCHASE_ORDERS_VIEW.GRN_DC_LOCAL_DELIVERY_TIME,
        PURCHASE_ORDERS_VIEW.GRN_DELIVERY_ID,
        PURCHASE_ORDERS_VIEW.GRN_DELIVERY_TIME,
        PURCHASE_ORDERS_VIEW.GRN_DELIVERY_STATUS,
        SKU_SPECIFICATION_VIEW.ID,
        SKU_SPECIFICATION_VIEW.NAME,
        SKU_SPECIFICATION_VIEW.CODE,
        SKU_SPECIFICATION_VIEW.MARKET,
        SKU_SPECIFICATION_VIEW.CATEGORY,
    )

    @Suppress("LongMethod")
    override suspend fun findPurchaseOrders(dcCodes: Set<String>, dateRange: DateRange): List<PurchaseOrder> = run {
        logger.info("Fetching Purchase Order data")
        metricsDSLContext.withTagName(purchaseOrdersInfoByDcDuration)
            .selectPurchaseOrderViewAndSkuSpecification()
            .from(PURCHASE_ORDERS_VIEW)
            .leftJoin(SKU_SPECIFICATION_VIEW).on(
                PURCHASE_ORDERS_VIEW.PO_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)
                    .or(PURCHASE_ORDERS_VIEW.GRN_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)),
            )
            .where(
                PURCHASE_ORDERS_VIEW.PO_DC_LOCAL_EXPECTED_ARRIVAL_START_TIME.between(
                    atStartOfDayInLocalTime(dateRange.fromDate),
                    atEndOfDayInLocalTime(dateRange.toDate),
                )
                    .or(
                        PURCHASE_ORDERS_VIEW.GRN_DC_LOCAL_DELIVERY_TIME.between(
                            atStartOfDayInLocalTime(dateRange.fromDate),
                            atEndOfDayInLocalTime(dateRange.toDate),
                        ),
                    ),
            )
            .and(PURCHASE_ORDERS_VIEW.PO_DC_CODE.`in`(dcCodes).or(PURCHASE_ORDERS_VIEW.GRN_DC_CODE.`in`(dcCodes)))
            .fetchAsync()
            .thenApply {
                PurchaseOrderRecordBuilder.buildPurchaseOrders(
                    records = it,
                    asnRecords = emptyList(),
                    dcConfigService = dcConfigService,
                    statsigFeatureFlagClient = statsigFeatureFlagClient,
                )
            }
            .await()
    }

    override suspend fun findPurchaseOrders(poNrs: Set<String>): List<PurchaseOrder> =
        metricsDSLContext.withTagName(purchaseOrdersByPoRefs)
            .selectPurchaseOrderViewAndSkuSpecification()
            .from(PURCHASE_ORDERS_VIEW)
            .leftJoin(SKU_SPECIFICATION_VIEW).on(
                PURCHASE_ORDERS_VIEW.PO_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)
                    .or(PURCHASE_ORDERS_VIEW.GRN_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)),
            )
            .where(
                PURCHASE_ORDERS_VIEW.PO_NUMBER.`in`(poNrs),
            ).fetchAsync()
            .thenApply {
                PurchaseOrderRecordBuilder.buildPurchaseOrders(
                    records = it,
                    asnRecords = emptyList(),
                    dcConfigService = dcConfigService,
                    statsigFeatureFlagClient = statsigFeatureFlagClient,
                )
            }
            .await()

    @Suppress("LongMethod")
    override suspend fun findPurchaseOrdersWithAsns(
        skuId: UUID,
        dcCodes: Set<String>,
        dateRanges: Set<DateRange>
    ): List<PurchaseOrder> =
        run {
            val poTable = PURCHASE_ORDERS_VIEW.`as`("pos")
            val dateRangeConditions = getDateRangeConditions(dateRanges, poTable)
            metricsDSLContext.withTagName(purchaseOrdersByDateRange)
                .selectPurchaseOrderViewAndSkuSpecification()
                .from(PURCHASE_ORDERS_VIEW)
                .leftJoin(SKU_SPECIFICATION_VIEW).on(
                    PURCHASE_ORDERS_VIEW.PO_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)
                        .or(PURCHASE_ORDERS_VIEW.GRN_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)),
                )
                .where(
                    exists(
                        select(inline(1))
                            .from(poTable)
                            .where(dateRangeConditions.reduce { acc, c -> acc.or(c) })
                            .and(
                                field(
                                    name("pos", PURCHASE_ORDERS_VIEW.PO_REF.name),
                                    PURCHASE_ORDERS_VIEW.PO_REF.dataType,
                                ).eq(PURCHASE_ORDERS_VIEW.PO_REF)
                                    .or(
                                        field(
                                            name("pos", PURCHASE_ORDERS_VIEW.GRN_PO_REF.name),
                                            PURCHASE_ORDERS_VIEW.GRN_PO_REF.dataType,
                                        ).eq(PURCHASE_ORDERS_VIEW.GRN_PO_REF),
                                    ),
                            )
                            .and(
                                field(
                                    name("pos", PURCHASE_ORDERS_VIEW.PO_SKU_ID.name),
                                    PURCHASE_ORDERS_VIEW.PO_SKU_ID.dataType,
                                ).eq(skuId)
                                    .or(
                                        field(
                                            name("pos", PURCHASE_ORDERS_VIEW.GRN_SKU_ID.name),
                                            PURCHASE_ORDERS_VIEW.GRN_SKU_ID.dataType,
                                        ).eq(skuId),
                                    ),
                            )
                            .and(
                                field(
                                    name("pos", PURCHASE_ORDERS_VIEW.PO_DC_CODE.name),
                                    PURCHASE_ORDERS_VIEW.PO_DC_CODE.dataType,
                                )
                                    .`in`(dcCodes)
                                    .or(
                                        field(
                                            name("pos", PURCHASE_ORDERS_VIEW.GRN_DC_CODE.name),
                                            PURCHASE_ORDERS_VIEW.GRN_DC_CODE.dataType,
                                        ).`in`(dcCodes),
                                    ),
                            ),
                    ),
                )
                .fetchAsync()
                .thenApply { result ->
                    val poIds = result.mapNotNull { it[PURCHASE_ORDERS_VIEW.PO_ID] }.toSet()
                    fetchAsns(poIds)
                        .thenApply { records ->
                            PurchaseOrderRecordBuilder.buildPurchaseOrders(
                                records = result,
                                asnRecords = records.map {
                                    JoinedAsnRecord(
                                        it.into(ADVANCED_SHIPPING_NOTICE),
                                        it.into(ADVANCED_SHIPPING_NOTICE_SKU),
                                    )
                                },
                                dcConfigService = dcConfigService,
                                statsigFeatureFlagClient = statsigFeatureFlagClient,
                            )
                        }
                }.await()
        }.await()

    override suspend fun findPoVariance(dcCodes: Set<String>, dateRanges: Set<DateRange>) =
        withContext(coroutineDispatcher) {
            logger.info("Fetching Purchase Order data for inbound variance")
            val purchaseOrderRecords =
                metricsDSLContext.withTagName(purchaseOrdersByDateRangeForInboundVariance).select(
                    PURCHASE_ORDERS_VIEW.PO_ID,
                    PURCHASE_ORDERS_VIEW.PO_REF,
                    PURCHASE_ORDERS_VIEW.PO_SKU_ID,
                    PURCHASE_ORDERS_VIEW.GRN_SKU_ID,
                    PURCHASE_ORDERS_VIEW.GRN_PO_REF,
                    PURCHASE_ORDERS_VIEW.PO_DC_CODE,
                    PURCHASE_ORDERS_VIEW.GRN_DC_CODE,
                    PURCHASE_ORDERS_VIEW.PO_SUPPLIER_ID,
                    PURCHASE_ORDERS_VIEW.PO_SUPPLIER_NAME,
                    PURCHASE_ORDERS_VIEW.PO_QUANTITY,
                    PURCHASE_ORDERS_VIEW.PO_UOM,
                    PURCHASE_ORDERS_VIEW.GRN_QUANTITY,
                    PURCHASE_ORDERS_VIEW.GRN_UOM,
                    SKU_SPECIFICATION_VIEW.ID,
                    SKU_SPECIFICATION_VIEW.NAME,
                    SKU_SPECIFICATION_VIEW.CODE,
                    SKU_SPECIFICATION_VIEW.CATEGORY,
                )
                    .from(PURCHASE_ORDERS_VIEW).leftJoin(SKU_SPECIFICATION_VIEW)
                    .on(
                        PURCHASE_ORDERS_VIEW.PO_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)
                            .or(PURCHASE_ORDERS_VIEW.GRN_SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)),
                    )
                    .where(
                        (getDateRangeConditions(dateRanges, PURCHASE_ORDERS_VIEW).reduce { acc, c -> acc.or(c) })
                            .and(
                                field(name(PURCHASE_ORDERS_VIEW.PO_REF.name), PURCHASE_ORDERS_VIEW.PO_REF.dataType)
                                    .eq(PURCHASE_ORDERS_VIEW.PO_REF)
                                    .or(
                                        field(
                                            name(PURCHASE_ORDERS_VIEW.GRN_PO_REF.name),
                                            PURCHASE_ORDERS_VIEW.GRN_PO_REF.dataType,
                                        ).eq(PURCHASE_ORDERS_VIEW.GRN_PO_REF),
                                    ),
                            ),
                    ).and(
                        PURCHASE_ORDERS_VIEW.PO_DC_CODE.`in`(dcCodes)
                            .or(PURCHASE_ORDERS_VIEW.GRN_DC_CODE.`in`(dcCodes)),
                    )
                    .fetchAsync().await()

            val purchaseOrderIds = purchaseOrderRecords.mapNotNull { it[PURCHASE_ORDERS_VIEW.PO_ID] }.toSet()

            val records = fetchAsns(purchaseOrderIds).await()

            val poVarianceList = PurchaseOrderRecordBuilder.buildPoForInboundVariance(
                purchaseOrderRecords,
                records.map {
                    JoinedAsnRecord(
                        it.into(ADVANCED_SHIPPING_NOTICE),
                        it.into(ADVANCED_SHIPPING_NOTICE_SKU),
                    )
                },
                dcConfigService,
            )
            poVarianceList
        }

    private fun fetchAsns(poIds: Set<UUID>) =
        metricsDSLContext.withTagName(asnsByPoIds)
            .select(
                ADVANCED_SHIPPING_NOTICE.ASN_ID,
                ADVANCED_SHIPPING_NOTICE.PO_ID,
                ADVANCED_SHIPPING_NOTICE.DC_CODE,
                ADVANCED_SHIPPING_NOTICE.PLANNED_DELIVERY_TIME,
                ADVANCED_SHIPPING_NOTICE_SKU.SKU_ID,
                ADVANCED_SHIPPING_NOTICE_SKU.UOM,
                ADVANCED_SHIPPING_NOTICE_SKU.SHIPPED_QUANTITY,
            )
            .from(ADVANCED_SHIPPING_NOTICE)
            .join(ADVANCED_SHIPPING_NOTICE_SKU)
            .on(ADVANCED_SHIPPING_NOTICE.ASN_ID.eq(ADVANCED_SHIPPING_NOTICE_SKU.ASN_ID))
            .where(ADVANCED_SHIPPING_NOTICE.PO_ID.`in`(poIds))
            .fetchAsync()

    private fun getDateRangeConditions(dateRanges: Set<DateRange>, table: Table<*>) = dateRanges.map {
        val fromDateTimeStartOfDay = atStartOfDayInLocalTime(it.fromDate)
        val toDateTimeEndOfDay = atEndOfDayInLocalTime(it.toDate)
        (
            field(
                name(table.name, PURCHASE_ORDERS_VIEW.PO_DC_LOCAL_EXPECTED_ARRIVAL_START_TIME.name),
                PURCHASE_ORDERS_VIEW.PO_DC_LOCAL_EXPECTED_ARRIVAL_START_TIME.dataType
            )
                .ge(fromDateTimeStartOfDay)
                .and(
                    field(
                        name(table.name, PURCHASE_ORDERS_VIEW.PO_DC_LOCAL_EXPECTED_ARRIVAL_START_TIME.name),
                        PURCHASE_ORDERS_VIEW.PO_DC_LOCAL_EXPECTED_ARRIVAL_START_TIME.dataType
                    )
                        .le(toDateTimeEndOfDay)
                )
            ).or(
            field(
                name(table.name, PURCHASE_ORDERS_VIEW.GRN_DC_LOCAL_DELIVERY_TIME.name),
                PURCHASE_ORDERS_VIEW.GRN_DC_LOCAL_DELIVERY_TIME.dataType
            )
                .ge(fromDateTimeStartOfDay)
                .and(
                    field(
                        name(table.name, PURCHASE_ORDERS_VIEW.GRN_DC_LOCAL_DELIVERY_TIME.name),
                        PURCHASE_ORDERS_VIEW.GRN_DC_LOCAL_DELIVERY_TIME.dataType
                    )
                        .le(toDateTimeEndOfDay)
                )
        )
    }

    private fun atStartOfDayInLocalTime(date: LocalDate) =
        date.atStartOfDay()

    private fun atEndOfDayInLocalTime(date: LocalDate) =
        date.atTime(LocalTime.MAX)

    companion object : Logging
}

internal data class JoinedAsnRecord(
    val asnRecord: AdvancedShippingNoticeRecord,
    val asnSkuRecord: AdvancedShippingNoticeSkuRecord
)
