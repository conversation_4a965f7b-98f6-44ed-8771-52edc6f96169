package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context.CATEGORY
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.DisableExpiryUnusable
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.memoize
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.Asn
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus
import com.hellofresh.cif.models.purchaseorder.PoSkuVariance
import com.hellofresh.cif.models.purchaseorder.PoVariance
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.Supplier
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom
import com.hellofresh.cif.purchase_order_lib.schema.tables.PurchaseOrdersView.PURCHASE_ORDERS_VIEW
import com.hellofresh.cif.purchase_order_lib.schema.tables.SkuSpecificationView.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.PurchaseOrdersViewRecord
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.SkuSpecificationRecord
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Record
import org.jooq.Record17
import org.jooq.Result

object PurchaseOrderRecordBuilder : Logging {

    @Suppress("LongMethod")
    internal fun buildPurchaseOrders(
        records: Result<Record>,
        asnRecords: List<JoinedAsnRecord>, // Join of ASN and ASNSku
        dcConfigService: DcConfigService,
        statsigFeatureFlagClient: StatsigFeatureFlagClient,
    ): List<PurchaseOrder> {
        val purchaseOrderRecords = records.into(PURCHASE_ORDERS_VIEW).mapNotNull {
            it.into(PurchaseOrdersViewRecord::class.java)
        }
        val purchaseOrderInfoMap = purchaseOrderRecords.groupBy {
            PurchaseOrderKey(it.poNumber ?: it.grnPoNumber, it.poDcCode ?: it.grnDcCode)
        }

        val skuSpecificationMap: Map<UUID, SkuSpecificationRecord> = records.into(SKU_SPECIFICATION_VIEW)
            .mapNotNull { it.into(SkuSpecificationRecord::class.java) }
            .filter { it.id != null }
            .associateBy { it.id }
        val asnMap = asnRecords.groupBy { (asn, _) -> AsnKey(asn.poId, asn.dcCode) }

        val unusableRulesEnabledFlag = isUnusableRulesEnabled(statsigFeatureFlagClient)

        return purchaseOrderInfoMap.entries.mapNotNull { entry ->
            try {
                val poView = entry.value.first()
                val localDcZoneId = dcConfigService.dcConfigurations[entry.key.dcCode]?.zoneId ?: ZoneOffset.UTC

                val expectedTimeslot = poView.poExpectedArrivalStartTime?.let {
                    TimeRange(
                        poView.poExpectedArrivalStartTime.atZoneSameInstant(localDcZoneId),
                        poView.poExpectedArrivalEndTime.atZoneSameInstant(localDcZoneId),
                    )
                }
                val expectedDeliveryDate = expectedTimeslot?.expectedDeliveryDate
                val supplier = poView.poSupplierId?.let { id ->
                    poView.poSupplierName?.let { name ->
                        Supplier(
                            id,
                            name
                        )
                    }
                }

                val params = PurchaseOrderSkusParams(
                    purchaseOrderInfoRecords = entry.value,
                    expectedDate = expectedDeliveryDate,
                    localDcZoneId = localDcZoneId,
                    skuSpecificationMap = skuSpecificationMap,
                    dcCode = entry.key.dcCode,
                )
                val purchaseOrderSkus = buildPurchaseOrderSkus(
                    params = params,
                    unusableRulesEnabledFlag = unusableRulesEnabledFlag,
                )
                val advancedShippingNotices = poView.poId?.let {
                    buildAdvancedShippingNotices(asnMap[AsnKey(poView.poId, entry.key.dcCode)], localDcZoneId)
                } ?: emptyList()

                PurchaseOrder(
                    entry.key.poNumber,
                    poView.poRef ?: poView.grnPoRef, poView.poId, entry.key.dcCode, expectedTimeslot, supplier,
                    purchaseOrderSkus, advancedShippingNotices, poView.poStatus.toPoStatus(),
                )
            } catch (e: IllegalArgumentException) {
                logger.error(
                    "Cannot build the purchase order for ${entry.key.poNumber}, ${entry.key.dcCode}, " +
                        "error = ${e.message}",
                )
                null
            }
        }
    }

    internal fun buildPoForInboundVariance(
        purchaseOrderRecords:
        List<Record17<UUID, String, UUID, UUID, String, String, String, UUID, String, BigDecimal, Uom, BigDecimal, Uom, UUID, String, String, String>>,
        asnRecords: List<JoinedAsnRecord>, // Join of ASN and ASNSku
        dcConfigService: DcConfigService
    ): List<PoVariance> {
        val purchaseOrderInfoMap = purchaseOrderRecords.groupBy {
            PurchaseOrderKey(
                it[PURCHASE_ORDERS_VIEW.PO_REF] ?: it[PURCHASE_ORDERS_VIEW.GRN_PO_REF],
                it[PURCHASE_ORDERS_VIEW.PO_DC_CODE] ?: it[PURCHASE_ORDERS_VIEW.GRN_DC_CODE],
            )
        }
        val asnMap = asnRecords.groupBy { (asn, _) -> AsnKey(asn.poId, asn.dcCode) }
        return purchaseOrderInfoMap.mapNotNull { (key, pos) ->
            try {
                val poView = pos.first()
                val localDcZoneId = dcConfigService.dcConfigurations[key.dcCode]?.zoneId ?: ZoneOffset.UTC

                val supplier = poView[PURCHASE_ORDERS_VIEW.PO_SUPPLIER_ID]?.let {
                    Supplier(it, poView[PURCHASE_ORDERS_VIEW.PO_SUPPLIER_NAME])
                }
                val purchaseOrderSkusForInboundVariance = buildPoSkusForInboundVariance(
                    pos,
                )
                val advancedShippingNotices = poView[PURCHASE_ORDERS_VIEW.PO_ID]?.let { poId ->
                    buildAdvancedShippingNotices(
                        asnMap[AsnKey(poId, key.dcCode)],
                        localDcZoneId,
                    )
                } ?: emptyList()

                PoVariance(
                    key.poNumber,
                    poView[PURCHASE_ORDERS_VIEW.PO_ID],
                    supplier,
                    purchaseOrderSkusForInboundVariance,
                    advancedShippingNotices,
                )
            } catch (e: IllegalArgumentException) {
                logger.error(
                    "Cannot build the purchase order for ${key.poNumber}, ${key.dcCode}, " +
                        "error = ${e.message}",
                )
                null
            }
        }
    }
}

private fun getMinimumLifeOnReceipt(record: PurchaseOrdersViewRecord) = record.poMlor ?: record.grnMlor

private fun getExpiryDateUsingMLor(minimumLifeOnReceipt: Int?, date: LocalDate) =
    minimumLifeOnReceipt?.let { date.plusDays(it.toLong() - 1L) }
// MLOR - (1 + ACL) = Number of days that stock is predicted to be usable for upon arrival

private fun buildAdvancedShippingNotices(
    asnRecords: List<JoinedAsnRecord>?,
    localDcZoneId: ZoneId
) = asnRecords
    ?.map { record ->
        val (asn, asnSku) = record
        Asn(
            id = asn.asnId,
            plannedDeliveryTime = asn.plannedDeliveryTime.toZonedDateTime().withZoneSameInstant(localDcZoneId),
            skuId = asnSku.skuId,
            shippedQuantity = SkuQuantity.fromBigDecimal(
                asnSku.shippedQuantity,
                SkuUomMapper.mapUomToSkuUOM(asnSku.uom),
            ),
        )
    }

private fun buildPoSkusForInboundVariance(
    purchaseOrderInfoRecords:
    List<Record17<UUID, String, UUID, UUID, String, String, String, UUID, String, BigDecimal, Uom, BigDecimal, Uom, UUID, String, String, String>>
) = purchaseOrderInfoRecords
    .groupBy { it[PURCHASE_ORDERS_VIEW.PO_SKU_ID] ?: it[PURCHASE_ORDERS_VIEW.GRN_SKU_ID] }
    .map { (skuId, skuInfos) ->
        PoSkuVariance(
            skuId = skuId,
            skuName = skuInfos.first()[SKU_SPECIFICATION_VIEW.NAME],
            skuCode = skuInfos.first()[SKU_SPECIFICATION_VIEW.CODE],
            skuCategory = skuInfos.first()[SKU_SPECIFICATION_VIEW.CATEGORY],
            expectedQuantity = with(skuInfos.first()) {
                if (this[PURCHASE_ORDERS_VIEW.PO_QUANTITY] != null) {
                    SkuQuantity.fromLong(
                        this[PURCHASE_ORDERS_VIEW.PO_QUANTITY].toLong(),
                        SkuUomMapper.mapUomToSkuUOM(this[PURCHASE_ORDERS_VIEW.PO_UOM]),
                    )
                } else {
                    null
                }
            },
            deliveredQuantity = with(skuInfos.first()) {
                if (this[PURCHASE_ORDERS_VIEW.GRN_QUANTITY] != null) {
                    SkuQuantity.fromLong(
                        this[PURCHASE_ORDERS_VIEW.GRN_QUANTITY].toLong(),
                        SkuUomMapper.mapUomToSkuUOM(this[PURCHASE_ORDERS_VIEW.GRN_UOM]),
                    )
                } else {
                    null
                }
            },
        )
    }

private fun buildPurchaseOrderSkus(
    params: PurchaseOrderSkusParams,
    unusableRulesEnabledFlag: (DCCategoryKey) -> Boolean
) = params.purchaseOrderInfoRecords
    .groupBy { it.poSkuId ?: it.grnSkuId }
    .map { (skuId, skuInfos) ->

        val isUnusableRulesEnabled =
            params.skuSpecificationMap[skuId]?.let {
                unusableRulesEnabledFlag(
                    DCCategoryKey(
                        dcCode = params.dcCode,
                        skuCategory = it.category,
                    ),
                )
            } ?: false

        val minimumLifeOnReceipt =
            if (isUnusableRulesEnabled) {
                null
            } else {
                getMinimumLifeOnReceipt(skuInfos.first())
            }

        PurchaseOrderSku(
            skuId = skuId,
            expectedQuantity = SkuQuantity.fromBigDecimal(
                skuInfos.first().poQuantity ?: BigDecimal.ZERO,
            ),
            deliveries = skuInfos.mapNotNull { it.createDeliveryInfo(minimumLifeOnReceipt, params.localDcZoneId) },
            expiryDate = params.expectedDate?.let {
                getExpiryDateUsingMLor(minimumLifeOnReceipt, it)
            },
        )
    }

private fun PurchaseOrdersViewRecord.createDeliveryInfo(minimumLifeOnReceipt: Int?, localDcZoneId: ZoneId) =
    this.grnDeliveryTime
        ?.atZoneSameInstant(localDcZoneId)
        ?.let { deliveryTime ->
            DeliveryInfo(
                this.grnDeliveryId,
                deliveryTime,
                DeliveryInfoStatus.valueOf(this.grnDeliveryStatus.uppercase()),
                SkuQuantity.fromBigDecimal(this.grnQuantity, SkuUomMapper.mapUomToSkuUOM(this.grnUom)),
                this.grnExpiryDate ?: getExpiryDateUsingMLor(minimumLifeOnReceipt, deliveryTime.toLocalDate()),
            )
        }

private fun isUnusableRulesEnabled(
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
): (DCCategoryKey) -> Boolean = { key: DCCategoryKey ->
    isUnusableFeatureFlag(statsigFeatureFlagClient, key)
}.memoize()

private fun isUnusableFeatureFlag(
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
    key: DCCategoryKey
): Boolean = statsigFeatureFlagClient.isEnabledFor(
    DisableExpiryUnusable(setOf(ContextData(DC, key.dcCode), ContextData(CATEGORY, key.skuCategory))),
)

private data class PurchaseOrderKey(val poNumber: String, val dcCode: String)

private data class AsnKey(val poId: UUID, val dcCode: String)

private data class DCCategoryKey(val dcCode: String, val skuCategory: String)

private data class PurchaseOrderSkusParams(
    val purchaseOrderInfoRecords: List<PurchaseOrdersViewRecord>,
    val expectedDate: LocalDate?,
    val localDcZoneId: ZoneId,
    val skuSpecificationMap: Map<UUID, SkuSpecificationRecord>,
    val dcCode: String
)
