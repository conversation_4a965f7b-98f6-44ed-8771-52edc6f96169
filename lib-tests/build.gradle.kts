plugins {
    id("com.hellofresh.cif.common-conventions")
}

description = "Set of common test code for testcontainers"
group = "$group.libTests"

dependencies {
    api(libs.postgresql.driver)
    api(libs.slf4j.simple)

    api(libs.flyway.core)
    api(libs.kafka.clients)
    api(libs.testcontainers.core)
    api(libs.testcontainers.kafka)
    api(libs.testcontainers.postgresql)
    api(libs.kafka.clients)
    implementation(libs.protobuf.grpc)
    api(libs.kafka.schema.registry.client)
    api(libs.hikaricp)

    api(projects.lib.db)
}
