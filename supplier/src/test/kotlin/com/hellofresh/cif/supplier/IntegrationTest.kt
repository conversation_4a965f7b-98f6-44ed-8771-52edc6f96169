package com.hellofresh.cif.supplier

import InfraPreparation
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.supplier.deserializer.GlobalSupplierAvro
import com.hellofresh.cif.supplier.deserializer.SupplierAvro
import com.hellofresh.cif.supplier.deserializer.SupplierSkuAvro
import com.hellofresh.cif.supplier.schema.Tables
import com.hellofresh.cif.supplier.sku.SupplierSkuApp
import io.confluent.kafka.schemaregistry.client.MockSchemaRegistryClient
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Duration
import java.util.concurrent.Executors
import kotlin.time.toKotlinDuration
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.common.serialization.ByteArraySerializer
import org.apache.kafka.common.serialization.Serdes
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.testcontainers.containers.KafkaContainer

open class IntegrationTest {

    @AfterEach
    fun tearDown() {
        dsl.deleteFrom(Tables.SUPPLIER).execute()
        dsl.deleteFrom(Tables.SUPPLIER_CULINARY_SKU).execute()
    }

    companion object {
        private lateinit var producerConfig: Map<String, String>
        private lateinit var consumerConfig: Map<String, String>

        lateinit var topicProducer: KafkaProducer<String, ByteArray>
        lateinit var dsl: MetricsDSLContext

        lateinit var supplierApp: SupplierApp
        lateinit var supplierSkuApp: SupplierSkuApp

        private lateinit var kafka: KafkaContainer
        val supplierTopic = facilityTopics.first()
        val globalSupplierTopic = globalSupplierTopics.first()
        val supplierSkuTopic = supplierSkuTopics.first()

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = InfraPreparation.startKafkaAndCreateTopics(
                listOf(supplierTopic, globalSupplierTopic, supplierSkuTopic),
            )

            topicProducer = InfraPreparation.createKafkaProducer(
                kafka, Serdes.String().serializer(),
                ByteArraySerializer(),
            )

            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.sku-specification-service-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            producerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                )
            val dataSource = InfraPreparation.getMigratedDataSource()
            val defaultConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(
                defaultConfiguration,
            ).withMetrics(SimpleMeterRegistry())

            val pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )
            supplierApp = SupplierApp(
                1,
                consumerConfig = consumerConfig,
                schemaRegistryClient = MockSchemaRegistryClient().apply {
                    register("$supplierTopic-value", SupplierAvro.avroSchema, 1, 1)
                    register("$globalSupplierTopic-value", GlobalSupplierAvro.globalSupplierAvroSchema, 1, 1)
                },
                pollConfig = pollConfig,
                dsl,
                SimpleMeterRegistry(),
            )
            supplierSkuApp = SupplierSkuApp(
                1,
                consumerConfig = consumerConfig,
                schemaRegistryClient = SupplierSkuAvro.testSupplierSkuSchemaRegistryClient,
                pollConfig = pollConfig,
                dsl,
                SimpleMeterRegistry(),
            )
        }
    }
}
