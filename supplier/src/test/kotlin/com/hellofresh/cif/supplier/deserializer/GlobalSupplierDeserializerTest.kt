package com.hellofresh.cif.supplier.deserializer

import com.hellofresh.cif.supplier.deserializer.GlobalSupplierAvro.avroGlobalSupplier
import com.hellofresh.cif.supplier.deserializer.GlobalSupplierAvro.testGlobalSupplierSchemaRegistryClient
import com.hellofresh.cif.supplier.model.Supplier
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class GlobalSupplierDeserializerTest {

    private val globalSupplierDeserializer = GlobalSupplierDeserializer(testGlobalSupplierSchemaRegistryClient)

    @Test
    fun `returns empty list if given input data is null`() {
        assertEquals(emptyList(), globalSupplierDeserializer.deserialize("test", null))
    }

    @Test
    fun `returns valid suppliers for the given input`() {
        val parentId = UUID.randomUUID()
        val supplierId = UUID.randomUUID()
        val name = "Test Supplier"
        val expectedSupplier = Supplier(supplierId, name, parentId)
        val avroByteArray = avroGlobalSupplier(supplierId, name, parentId)
        val actualSuppliers = globalSupplierDeserializer.deserialize("test", avroByteArray)
        assertEquals(listOf(expectedSupplier), actualSuppliers)
    }
}
