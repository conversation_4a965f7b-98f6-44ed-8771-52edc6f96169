package com.hellofresh.cif.supplier.deserializer

import com.hellofresh.cif.supplier.deserializer.SupplierAvro.avroSupplier
import com.hellofresh.cif.supplier.deserializer.SupplierAvro.testSchemaRegistryClient
import com.hellofresh.cif.supplier.model.Supplier
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

internal class SupplierDeserializerTest {

    private val supplierDeserializer = SupplierDeserializer(testSchemaRegistryClient)

    @Test
    fun `returned null if data is null`() {
        assertEquals(emptyList(), supplierDeserializer.deserialize("test", null))
    }

    @Test
    fun `deserialization succeeds`() {
        val id = UUID.fromString("00000000-0000-0000-0000-000000000000")
        val expected = Supplier(id, "ANAME", UUID.randomUUID())
        val actual = supplierDeserializer.deserialize(
            "test",
            avroSupplier(expected.id, expected.name, expected.parentId),
        )

        assertEquals(expected, actual[0])
    }

    companion object
}
