package com.hellofresh.cif.supplier.deserializer

import com.hellofresh.cif.supplier.deserializer.SupplierSkuAvro.avroSupplierSku
import com.hellofresh.cif.supplier.deserializer.SupplierSkuAvro.testSupplierSkuSchemaRegistryClient
import com.hellofresh.cif.supplier.model.SupplierSku
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class SupplierSkuDeserializerTest {

    private val supplierSkuDeserializer = SupplierSkuDeserializer(testSupplierSkuSchemaRegistryClient)

    @Test
    fun `returns empty list if given input data is null`() {
        assertEquals(emptyList(), supplierSkuDeserializer.deserialize("test", null))
    }

    @Test
    fun `returns valid supplier sku for the given input`() {
        val id = UUID.randomUUID()
        val supplierId = UUID.randomUUID()
        val culinarySkuId = UUID.randomUUID()
        val market = "dach"
        val status = "Active"
        val expectedSupplier = SupplierSku(id, supplierId, culinarySkuId, market, status)
        val avroByteArray = avroSupplierSku(id, supplierId, culinarySkuId, market, status)
        val actualSupplierSkus = supplierSkuDeserializer.deserialize("test", avroByteArray)
        assertEquals(listOf(expectedSupplier), actualSupplierSkus)
    }
}
