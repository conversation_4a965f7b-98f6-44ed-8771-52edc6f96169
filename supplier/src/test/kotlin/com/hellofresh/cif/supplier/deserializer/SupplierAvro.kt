package com.hellofresh.cif.supplier.deserializer

import com.hellofresh.cif.lib.kafka.avro.AvroBuilder
import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.client.MockSchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroSerializer
import java.util.UUID

object SupplierAvro {
    private val cskuAvroSchema = SupplierAvro::class.java.classLoader.getResourceAsStream(
        "supplier.avsc"
    )?.readAllBytes()?.let {
        String(it, Charsets.UTF_8)
    }
    val avroSchema = AvroSchema(cskuAvroSchema)
    private val avroSerializer = KafkaAvroSerializer(MockSchemaRegistryClient()).apply {
        configure(mapOf("schema.registry.url" to "mock://localhost:0"), false)
    }
    val testSchemaRegistryClient = MockSchemaRegistryClient().apply {
        register("test-topic", avroSchema, 1, 1)
    }

    fun avroSupplier(
        id: UUID,
        name: String,
        parentId: UUID?
    ): ByteArray {
        val record = AvroBuilder(avroSchema.rawSchema()).apply {
            set("event_type", "supplier_updated")
            set("id", id.toString())
            set("name", name)
            set("parent_id", parentId.toString())
            set("contact_persons", emptyList<String>())
        }
            .setRest()
            .build()
        return avroSerializer.serialize("", record)
    }
}
