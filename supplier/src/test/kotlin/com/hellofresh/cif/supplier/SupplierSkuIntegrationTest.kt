package com.hellofresh.cif.supplier

import com.hellofresh.cif.supplier.deserializer.SupplierSkuAvro
import com.hellofresh.cif.supplier.model.SupplierSku
import com.hellofresh.cif.supplier.schema.Tables
import com.hellofresh.cif.supplier.schema.tables.records.SupplierCulinarySkuRecord
import java.time.Duration
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.ProducerRecord
import org.testcontainers.shaded.org.awaitility.Awaitility

class SupplierSkuIntegrationTest : IntegrationTest() {
    @Test
    fun `should be able to process supplier sku records from supplier sku topics`() {
        // given

        val expectedSupplierSku = SupplierSku(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), "dach", "Active")

        // when
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                supplierSkuApp.runApp()
            }

            topicProducer.send(
                ProducerRecord(
                    supplierSkuTopic,
                    0,
                    expectedSupplierSku.id.toString(),
                    SupplierSkuAvro.avroSupplierSku(
                        expectedSupplierSku.id,
                        expectedSupplierSku.supplierId,
                        expectedSupplierSku.culinarySkuId,
                        expectedSupplierSku.market,
                        expectedSupplierSku.status,
                    ),
                ),
            ).get()

            var supplierCulinarySkuRecords = emptyList<SupplierCulinarySkuRecord>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    supplierCulinarySkuRecords = dsl.selectFrom(Tables.SUPPLIER_CULINARY_SKU).fetch()
                    supplierCulinarySkuRecords.size == 1
                }

            supplierCulinarySkuRecords.first { it.id == expectedSupplierSku.id }
                .also { supplierCulinarySkuRecord ->
                    assertEquals(expectedSupplierSku.supplierId, supplierCulinarySkuRecord.supplierId)
                    assertEquals(expectedSupplierSku.culinarySkuId, supplierCulinarySkuRecord.culinarySkuId)
                    assertEquals(expectedSupplierSku.market, supplierCulinarySkuRecord.market)
                    assertEquals(expectedSupplierSku.status, supplierCulinarySkuRecord.status)
                }

            app.cancel()
        }
    }
}
