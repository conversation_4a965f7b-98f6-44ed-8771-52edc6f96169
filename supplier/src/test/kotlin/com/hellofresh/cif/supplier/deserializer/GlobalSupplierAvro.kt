package com.hellofresh.cif.supplier.deserializer

import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.client.MockSchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroSerializer
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.apache.avro.generic.GenericRecordBuilder

object GlobalSupplierAvro {
    private val globalSupplyAvroSchema = GlobalSupplierAvro::class.java.classLoader.getResourceAsStream(
        "globalsupplier.avsc"
    )?.readAllBytes()?.let {
        String(it, Charsets.UTF_8)
    }
    val globalSupplierAvroSchema = AvroSchema(globalSupplyAvroSchema)
    private val globalSupplierAvroSerializer = KafkaAvroSerializer(MockSchemaRegistryClient()).apply {
        configure(mapOf("schema.registry.url" to "mock://localhost:0"), false)
    }
    val testGlobalSupplierSchemaRegistryClient = MockSchemaRegistryClient().apply {
        register("test-topic", globalSupplierAvroSchema, 1, 1)
    }

    fun avroGlobalSupplier(
        id: UUID,
        name: String,
        parentId: UUID?
    ): ByteArray {
        val schema = globalSupplierAvroSchema.rawSchema()
        val builder = GenericRecordBuilder(schema)

        builder.set("id", parentId?.toString() ?: "")
        builder.set("name", name)

        val addressSchema = schema.getField("addresses").schema().elementType
        val addressRecord = GenericRecordBuilder(addressSchema)
            .set("legal_address", true)
            .set("street", "Main St")
            .set("city", "Berlin")
            .set("post_code", "123456")
            .set("region", "")
            .set("country", "Germany")
            .build()

        val dcSchema = addressSchema.getField("distribution_centers").schema().elementType
        val dcRecord = GenericRecordBuilder(dcSchema)
            .set("id", id.toString())
            .build()

        val addresses = mutableListOf<GenericRecord>()
        addresses.add(
            addressRecord.apply {
                put("distribution_centers", mutableListOf(dcRecord))
            },
        )
        builder.set("addresses", addresses)
        val record: GenericRecord = builder.build()
        return globalSupplierAvroSerializer.serialize("", record)
    }
}
