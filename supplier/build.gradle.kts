import com.hellofresh.gradle.tasks.DownloadAvroSchemasTask

plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
    alias(libs.plugins.gradle.docker.compose)
    `test-functional`
}

description = "Dumps Supplier information from Kafka topics to the DB"
group = "$group.supplier"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "supplier | supplier_culinary_sku"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

val downloadAvroSchemas by tasks.registering(DownloadAvroSchemasTask::class) {
    group = "verification"
    subjectNames.addAll("public.planning.facility.v1-value")
    target.set(layout.projectDirectory.dir("src/test/resources/avro"))
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(libs.jooq.core)
    implementation(projects.lib)
    implementation(projects.lib.db)
    implementation(projects.dateUtilModels)
    implementation(libs.kafka.avro.serializer)

    testImplementation(libs.testcontainers.core)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.testcontainers.kafka)
    testImplementation(libs.testcontainers.junit)
    testImplementation(projects.libTests)
    testImplementation(libs.mockk)

    testFunctionalImplementation(projects.libTests)

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
