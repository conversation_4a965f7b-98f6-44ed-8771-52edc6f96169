plugins {
    id("com.hellofresh.cif.application-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}

description = "Process the transfer order data"
group = "$group.transfer.order"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "dc_config|transfer_order|transfer_order_skus|transfer_order_status|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.distributionCenterLib)
    implementation(projects.lib.db)
    implementation(projects.lib)
    implementation(libs.kafka.clients)
    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)
    implementation(libs.jackson.kotlin)
    implementation(projects.dateUtilModels)
    implementation(projects.lib.featureflags)
    implementation(projects.lib.featureflags)
    implementation(projects.transferOrder.transferOrderLib)

    testImplementation(libs.testcontainers.core)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.testcontainers.kafka)
    testImplementation(libs.testcontainers.junit)
    testImplementation(projects.libTests)
    testImplementation(libs.mockk)

    testFunctionalImplementation(libs.jooq.core)
    testFunctionalImplementation(projects.lib)
    testFunctionalImplementation(projects.libTests)
    testFunctionalImplementation(libs.coroutines.core)
    testFunctionalImplementation(testFixtures(projects.lib.featureflags))
}
