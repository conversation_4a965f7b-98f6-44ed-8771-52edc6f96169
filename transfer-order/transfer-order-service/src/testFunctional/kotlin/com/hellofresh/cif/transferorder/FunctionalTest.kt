import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.distribution_center_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.transfer.order.schema.Tables
import com.hellofresh.cif.transferorder.repository.TransferOrderRepository
import com.hellofresh.cif.transferorder.repository.TransferOrderRepositoryImpl
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.concurrent.Executors
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.TRANSFER_ORDER_SKUS).execute()
        dsl.deleteFrom(Tables.TRANSFER_ORDER).execute()
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
    }

    companion object {

        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()

        lateinit var dsl: MetricsDSLContext
        lateinit var dcConfigRepository: DcRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var transferOrderRepository: TransferOrderRepository

        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcConfigRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
            transferOrderRepository = TransferOrderRepositoryImpl(dsl)
        }

        fun createDcConfig(dcCode: String) =
            DcConfigRecord(
                dcCode, "DACH", "MONDAY", "FRIDAY", "Europe/Berlin",
                true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
                emptyArray()
            )
    }
}
