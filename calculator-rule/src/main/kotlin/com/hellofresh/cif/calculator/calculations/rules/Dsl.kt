package com.hellofresh.cif.calculator.calculations.rules

import com.hellofresh.cif.calculator.calculations.CalculationStep

// Rule defines a business which updates a calculation
internal fun interface Rule {
    operator fun invoke(step: CalculationStep): CalculationStep
    operator fun plus(r: Rule) = Rule { r(invoke(it)) }
}

// Predicate applies condition on a calculation and then may apply a rule
internal fun interface Predicate {
    operator fun invoke(step: CalculationStep): Boolean
    infix fun then(r: Rule) = Rule {
        if (invoke(it)) r(it) else it
    }

    infix fun and(p: Predicate) = Predicate {
        this(it) && p(it)
    }

    infix fun or(p: Predicate) = Predicate {
        this(it) || p(it)
    }

    operator fun not() = Predicate {
        !invoke(it)
    }
}

internal infix fun <T> ((T) -> Predicate).then(r: (T) -> Rule) = { t: T -> this(t).then(r(t)) }
internal infix fun <T> ((T) -> Predicate).then(r: Rule) = { t: T -> this(t).then(r) }
internal operator fun <T> ((T) -> Predicate).not() = { t: T -> !this(t) }
internal infix fun <T> ((T) -> Predicate).and(next: (T) -> Predicate) = { t: T -> this(t) and next(t) }

internal infix fun <T> ((T) -> Predicate).or(next: (T) -> Predicate) = { t: T -> this(t) or next(t) }
