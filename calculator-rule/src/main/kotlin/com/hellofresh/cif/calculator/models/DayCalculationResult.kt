package com.hellofresh.cif.calculator.models

import com.hellofresh.cif.calculator.calculations.SupplierSkuPoDueIn
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.time.LocalDate
import java.util.UUID

data class DayCalculationResult(
    val uom: SkuUOM,
    val cskuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val unusable: SkuQuantity,
    val openingStock: SkuQuantity,
    val present: SkuQuantity, // aka stock in POC
    val actualInbound: SkuQuantity,
    val actualInboundPurchaseOrders: Set<String>?,
    val expectedInbound: SkuQuantity,
    val expectedInboundPurchaseOrders: Set<String>?,
    val demanded: SkuQuantity,
    val closingStock: SkuQuantity,
    val dailyNeeds: SkuQuantity,
    val productionWeekStartStock: SkuQuantity,
    val productionWeek: String,
    val actualConsumption: SkuQuantity,
    val safetyStock: SkuQuantity? = null,
    val strategy: String,
    val safetyStockNeeds: SkuQuantity? = null,
    val stagingStock: SkuQuantity = SkuQuantity.fromLong(0, uom),
    val storageStock: SkuQuantity = SkuQuantity.fromLong(0, uom),
    val stockUpdate: SkuQuantity? = null,
    val purchaseOrderDueInForSuppliers: List<SupplierSkuPoDueIn>? = null,
    val maxPurchaseOrderDueIn: Int? = null,
    val netNeeds: SkuQuantity,
    val unusableInventory: List<CalculationInventory>? = null,
    val expectedInboundTransferOrders: Set<String>? = null,
    val expectedInboundTransferOrdersQuantity: SkuQuantity = SkuQuantity.fromLong(0, uom),
    val expectedOutboundTransferOrders: Set<String>? = null,
    val expectedOutboundTransferOrdersQuantity: SkuQuantity = SkuQuantity.fromLong(0, uom),
    val actualInboundTransferOrders: Set<String>? = null,
    val actualInboundTransferOrdersQuantity: SkuQuantity = SkuQuantity.fromLong(0, uom),
) {
    companion object
}
