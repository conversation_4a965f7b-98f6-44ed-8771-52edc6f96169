@file:Suppress("TopLevelPropertyNaming")

package com.hellofresh.cif.calculator.calculations.rules

import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.safetystock.model.SafetyStockKey

const val TARGET_SAFETY_STOCK_DEFAULT_STRATEGY = "UNSPECIFIED_STRATEGY"

private fun <T> set(values: Map<CalculationKey, T>, fn: DayCalculation.(T) -> Unit) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            val value = values[key]
            if (value != null) {
                fn(value)
            }
        }
    }
}

internal fun setStockUpdates(values: Map<CalculationKey, SkuQuantity>) =
    set(values) { this.stockUpdate = it }

internal fun setSafetyStock(data: CalculationData) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            val safetyStockMap = data.safetyStocks[
                this.key.toSafetyStockKey(dcConfig = data.dcConfig[this.key.dcCode]!!,)
            ]
            this.safetyStock = safetyStockMap?.let {
                SkuQuantity.fromLong(it.safetyStock, demand.unitOfMeasure)
            }
            this.strategy = safetyStockMap?.strategy ?: TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
        }
    }
}

private fun CalculationKey.toSafetyStockKey(dcConfig: DistributionCenterConfiguration) =
    SafetyStockKey(dcCode, DcWeek(date, dcConfig.productionStart), cskuId)
