package com.hellofresh.cif.calculator.models

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.SupplierSkuDetail
import java.util.UUID

data class InputData(
    val mode: CalculatorMode,
    val skuDcCandidates: Set<SkuDcCandidate>,
    val inventory: InventorySnapshots,
    val purchaseOrderInbounds: PurchaseOrderInbounds,
    val transferOrderInbounds: TransferOrderInbounds,
    val transferOrderOutbounds: TransferOrderOutbounds,
    val demands: Demands,
    val safetyStocks: Map<SafetyStockKey, SafetyStockValue>,

    /** Use by both experiment and stock update feature to update the stocks for
     * a given [CalculationKey] **/
    val stockUpdates: Map<CalculationKey, SkuQuantity>,
    val supplierSku: Map<UUID, List<SupplierSkuDetail>>,
    val preproductionCleardownDcs: Set<String>
) {
    val skuSpecs: Map<UUID, SkuSpecification> = skuDcCandidates.associateBy({ it.skuId }) { it.skuSpecification }

    val dcConfig: Map<DcCode, DistributionCenterConfiguration> = skuDcCandidates.associateBy({ it.dcCode }) { it.dc }
}

data class SkuDcCandidate(val skuId: UUID, val skuSpecification: SkuSpecification, val dc: DistributionCenterConfiguration) {
    val dcCode: DcCode
        get() = dc.dcCode
}
