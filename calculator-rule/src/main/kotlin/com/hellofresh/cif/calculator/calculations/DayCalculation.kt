package com.hellofresh.cif.calculator.calculations

import com.hellofresh.cif.calculator.calculations.rules.TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.sumOf
import com.hellofresh.sku.models.SkuSpecification

// Intermediate calculation for a key.
@Suppress("DataClassShouldBeImmutable")
internal data class DayCalculation(
    val key: CalculationKey,
    val sku: SkuSpecification,
) {
    val uom: SkuUOM
        get() = sku.uom
    val zero: SkuQuantity = SkuQuantity.fromLong(0, uom)
    var inventory: List<CalculationInventory> = emptyList()

    /**
     * Staging inventory fetched from the source.
     * It is updated realtime from the DB
     */
    var stagingFromSource: List<CalculationInventory> = emptyList()

    /**
     * Staging inventory we have right now. It is calculated initially then
     * we keep updating it by deducting demand.
     * Initial value:
     * stagingInventory(n-1) + (stagingFromSource(n) - stagingFromSource(n-1))
     */
    var stagingInventory: List<CalculationInventory> = emptyList()

    /**
     *  Inventory we have in storage.
     **/
    var storageInventory: List<CalculationInventory> = emptyList()

    var actualConsumption: SkuQuantity = zero

    /** demand we expect for the given date **/
    var demand: SkuQuantity = zero

    /** demand which is remaining after deductions. This is the field we use
     * to calculate **/
    var dailyNeeds: SkuQuantity = zero
    var expectedInboundQty: SkuQuantity = zero
    var expectedInboundPos: Set<String> = emptySet()
    var expectedCutOffPosQty: SkuQuantity? = null

    var actualInboundQty: SkuQuantity = zero
    var actualInboundPos: Set<String> = emptySet()

    var unusableInventory: List<CalculationInventory> = emptyList()
    var unusableStagingInventory: List<CalculationInventory> = emptyList()
    var unusableStorageInventory: List<CalculationInventory> = emptyList()
    var openingStock: SkuQuantity = zero
    var openingStagingStock: SkuQuantity = zero
    var openingStorageStock: SkuQuantity = zero
    var productionWeek: String = ""
    var stockUpdate: SkuQuantity? = null
    var safetyStock: SkuQuantity? = null
    var strategy: String = TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
    var safetyStockNeeds: SkuQuantity? = null
    var closingStock: SkuQuantity = zero
    var purchaseOrderDueInForSuppliers: List<SupplierSkuPoDueIn>? = null
    var maxPurchaseOrderDueIn: Int? = null
    var netNeeds: SkuQuantity = zero

    val unusable: SkuQuantity
        get() = unusableInventory.sumOf {
            it.qty
        } + unusableStagingInventory.sumOf {
            it.qty
        } + unusableStorageInventory.sumOf { it.qty }

    val storageStock: SkuQuantity get() = storageInventory.sumOf { it.qty }
    val stagingStock: SkuQuantity get() = stagingInventory.sumOf { it.qty }
    var expectedInboundTransferOrders: Set<String>? = null
    var expectedInboundTransferOrdersQuantity: SkuQuantity = zero
    var expectedOutboundTransferOrders: Set<String>? = null
    var expectedOutboundTransferOrdersQuantity: SkuQuantity = zero
    var actualInboundTransferOrders: Set<String>? = null
    var actualInboundTransferOrdersQuantity: SkuQuantity = zero
    fun calculate() =
        DayCalculationResult(
            uom = uom,
            cskuId = key.cskuId,
            dcCode = key.dcCode,
            date = key.date,
            unusable = unusable,
            openingStock = openingStock,
            present = openingStock + unusable,
            actualInbound = actualInboundQty,
            actualInboundPurchaseOrders = actualInboundPos,
            expectedInbound = expectedInboundQty,
            expectedInboundPurchaseOrders = expectedInboundPos,
            demanded = demand,
            closingStock = closingStock,
            dailyNeeds = dailyNeeds,
            productionWeekStartStock = zero,
            productionWeek = productionWeek,
            actualConsumption = actualConsumption,
            safetyStock = safetyStock,
            strategy = strategy,
            safetyStockNeeds = calculateSafetyStockNeeds(closingStock),
            stagingStock = openingStagingStock,
            storageStock = openingStorageStock,
            stockUpdate = stockUpdate,
            purchaseOrderDueInForSuppliers = purchaseOrderDueInForSuppliers,
            maxPurchaseOrderDueIn = maxPurchaseOrderDueIn,
            netNeeds = netNeeds,
            unusableInventory = unusableInventory.takeIf { it.isNotEmpty() },
            expectedInboundTransferOrders = expectedInboundTransferOrders,
            expectedInboundTransferOrdersQuantity = expectedInboundTransferOrdersQuantity,
            expectedOutboundTransferOrders = expectedOutboundTransferOrders,
            expectedOutboundTransferOrdersQuantity = expectedOutboundTransferOrdersQuantity,
            actualInboundTransferOrders = actualInboundTransferOrders,
            actualInboundTransferOrdersQuantity = actualInboundTransferOrdersQuantity,
        )

    /*
        1) Safety stock needs will be the difference between safety stock and closing stock.
        2) Safety sock needs cannot be greater than 0.
        3) Safety stock needs cannot be a larger negative value than safety stock.
     */
    private fun calculateSafetyStockNeeds(closingStock: SkuQuantity): SkuQuantity? =
        safetyStock?.let {
            if ((closingStock - it) > zero) {
                zero
            } else {
                if (closingStock > zero) {
                    (closingStock - it)
                } else {
                    -it
                }
            }
        }

    fun daysInFuture(n: Long) = if (n == 0L) this else DayCalculation(key = key.daysInFuture(n), this.sku)
}

fun CalculationKey.nextDay() = this.copy(date = this.date.plusDays(1))
fun CalculationKey.previousDay() = this.copy(date = this.date.minusDays(1))
fun CalculationKey.daysInFuture(n: Long) = this.copy(date = this.date.plusDays(n))
