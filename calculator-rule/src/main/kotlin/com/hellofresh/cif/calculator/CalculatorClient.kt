package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.enrich
import com.hellofresh.cif.calculator.calculations.rules.Rule
import com.hellofresh.cif.calculator.calculations.rules.initRules
import com.hellofresh.cif.calculator.calculations.rules.selectCalculationsRules
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneOffset
import org.apache.logging.log4j.kotlin.Logging

private const val DAYS_AHEAD = 112L // 16 weeks

class CalculatorClient(private val statsigFeatureFlagClient: StatsigFeatureFlagClient) {
    /**
     * Calculates the projected inventory for all skus in inputData.
     * @param inputData: a bag of data points for inventory, demand, inbound, actual consumption organized per sku.
     * @return a list of DayCalculationResults between the earliest past date found in InputData and 112 days
     * in the future.
     */
    fun runDailyCalculations(inputData: InputData): List<DayCalculationResult> {
        val calculationInputData = CalculationData(inputData, statsigFeatureFlagClient)

        val candidateSet = inputData.skuDcCandidates

        return candidateSet
            .start(initRules(calculationInputData))
            .map { (_, initialStep) ->
                (0..DAYS_AHEAD).asSequence().map { initialStep.daysInFuture(it) }
            }
            .flatten()
            .map {
                val productionStart = productionStart(inputData.dcConfig[it.key.dcCode]!!, inputData.mode)
                it.productionWeek = DcWeek(it.key.date, productionStart).value
                it
            }
            .applyCalculationsRules(calculationInputData)
            .map { it.calculate() }
    }

    private fun Sequence<DayCalculation>.applyCalculationsRules(calculationData: CalculationData) =
        this.groupBy { it.key.dcCode to it.key.cskuId }
            .flatMap { (dcSku, dcSkuCalculations) ->
                val (dc, skuId) = dcSku
                val skuCalculationsByKey = dcSkuCalculations.associateBy { it.key }
                runCatching {
                    dcSkuCalculations.sortedBy { it.key.date }
                        .map { skuDayCalculation ->
                            CalculationStep(skuCalculationsByKey, skuDayCalculation)
                                .apply(
                                    selectCalculationsRules(
                                        calculationData,
                                        automatedDcLiveRules = calculationData.featureFlag.isAutomatedDcLiveRulesFlag(
                                            calculationData.dcConfig[dc],
                                        ),
                                        live2Rules = calculationData.featureFlag.isLive2RulesFlagFunction(
                                            calculationData.dcConfig[dc],
                                        ),
                                    ),
                                )
                        }.enrich(calculationData)
                }.onFailure {
                    logger.error("Error running calculations for $dc - $skuId", it)
                }
                    .getOrDefault(emptyList())
            }

    private fun productionStart(dcConfig: DistributionCenterConfiguration, mode: CalculatorMode): DayOfWeek =
        if (mode == PRE_PRODUCTION || mode == LIVE_INVENTORY_PRE_PRODUCTION) {
            dcConfig.productionStart.minus(1)
        } else {
            dcConfig.productionStart
        }

    // returns a sequence of initial day calculations for each sku
    private fun Set<SkuDcCandidate>.start(r: Rule): Sequence<CalculationStep> = this.asSequence()
        .map { (skuId, skuSpec, dc) ->
            CalculationStep(
                mutableMapOf(),
                DayCalculation(CalculationKey(skuId, dc.dcCode, LocalDate.now(ZoneOffset.UTC)), skuSpec),
            ).apply(r)
        }

    companion object : Logging
}
