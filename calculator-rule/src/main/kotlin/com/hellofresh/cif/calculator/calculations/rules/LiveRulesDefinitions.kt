package com.hellofresh.cif.calculator.calculations.rules

import com.hellofresh.cif.calculator.calculations.previousDay
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.staging
import com.hellofresh.cif.calculator.models.storage
import com.hellofresh.inventory.models.InventorySnapshots

internal fun `use previous day live storage`(inventorySnapshots: InventorySnapshots) =
    Rule { calculations ->
        calculations.dayCalculation.apply {
            storageInventory =
                inventorySnapshots.getLiveInventory(key.previousDay())?.skuLiveInventory?.inventory?.toList()?.storage() ?: emptyList()
        }
        calculations
    }

internal fun `use previous day live staging`(inventorySnapshots: InventorySnapshots) =
    Rule { calculations ->
        calculations.dayCalculation.apply {
            stagingInventory =
                inventorySnapshots.getLiveInventory(key.previousDay())?.skuLiveInventory?.inventory?.staging() ?: emptyList()
        }
        calculations
    }

internal fun `use live storage`(inventorySnapshots: InventorySnapshots) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            storageInventory = inventorySnapshots.getLiveInventory(key)?.skuLiveInventory?.inventory?.storage() ?: emptyList()
        }
    }
}

internal fun `use live staging`(inventorySnapshots: InventorySnapshots) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            stagingInventory = inventorySnapshots.getLiveInventory(key)?.skuLiveInventory?.inventory?.staging() ?: emptyList()
        }
    }
}

internal fun `add inbounds to live storage inventory stock`(data: CalculationData) =
    addInboundsInLiveInventory(
        data,
        {
            data.inventory.getLiveInventory(key)?.inventoryTime ?: key.date.atStartOfDay()
        },
    ) { usable, unusable ->
        storageInventory = storageInventory + usable
        unusableStorageInventory = unusableStorageInventory + unusable
    }

internal fun InventorySnapshots.getLiveInventory(calculationKey: CalculationKey) =
    getLiveInventory(calculationKey.cskuId, calculationKey.dcCode, calculationKey.date)
