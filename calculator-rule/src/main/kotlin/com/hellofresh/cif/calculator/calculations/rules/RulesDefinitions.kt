@file:Suppress("TopLevelPropertyNaming", "TooManyFunctions", "MagicNumber")

package com.hellofresh.cif.calculator.calculations.rules

import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.nextDay
import com.hellofresh.cif.calculator.calculations.previousDay
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.inventory
import com.hellofresh.cif.calculator.models.staging
import com.hellofresh.cif.calculator.models.storage
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryPoInbound
import com.hellofresh.cif.models.purchaseorder.Inbounds
import com.hellofresh.cif.models.purchaseorder.PoInbounds
import com.hellofresh.cif.models.sumOf
import com.hellofresh.cif.transferorder.model.DeliveryToInbound
import com.hellofresh.cif.transferorder.model.DeliveryToOutbound
import com.hellofresh.cif.transferorder.model.Inbounds as TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.Outbounds
import com.hellofresh.cif.transferorder.model.ToInbounds
import com.hellofresh.cif.transferorder.model.ToOutbounds
import com.hellofresh.inventory.models.CleardownMode
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.InboundSnapshotState
import com.hellofresh.sku.models.SkuSpecification
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import org.jetbrains.annotations.VisibleForTesting

internal fun `use inventory for today`(inventorySnapshots: InventorySnapshots) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                inventory = inventorySnapshots.getInventory(key.copy(date = todayDc(dcConfig[key.dcCode]!!.zoneId)))
                    ?.inventory?.toList()?.inventory() ?: emptyList()
            }
        }
    }
}

internal fun `use previous day inventory`(inventorySnapshots: InventorySnapshots) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                inventory = inventorySnapshots.getInventory(key.copy(date = key.date.minusDays(1)))?.inventory?.toList()?.inventory() ?: emptyList()
            }
        }
    }

internal fun `use current day inventory`(inventorySnapshots: InventorySnapshots) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                inventory = inventorySnapshots.getInventory(key)?.inventory?.toList()?.inventory() ?: emptyList()
            }
        }
    }

internal fun `use previous day staging from source`(inventorySnapshots: InventorySnapshots) =
    Rule { calculations ->
        calculations.dayCalculation.apply {
            stagingFromSource =
                inventorySnapshots.getInventory(key.previousDay())?.inventory?.toList()?.staging() ?: emptyList()
        }
        calculations
    }

internal fun `use previous day storage`(inventorySnapshots: InventorySnapshots) =
    Rule { calculations ->
        calculations.dayCalculation.apply {
            storageInventory =
                inventorySnapshots.getInventory(key.previousDay())?.inventory?.toList()?.storage() ?: emptyList()
        }
        calculations
    }

internal fun `use staging from cleardown inventory`(inventorySnapshots: InventorySnapshots) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            stagingInventory = inventorySnapshots.getLatestCleardown(key.cskuId, key.dcCode)?.inventory?.toList()?.staging() ?: emptyList()
        }
    }
}

internal fun `use staging from inventory`(inventorySnapshots: InventorySnapshots) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            stagingInventory = inventorySnapshots.getInventory(key)?.inventory?.toList()?.staging() ?: emptyList()
        }
    }
}

internal fun `use storage from cleardown inventory`(inventorySnapshots: InventorySnapshots) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            storageInventory = inventorySnapshots.getLatestCleardown(key.cskuId, key.dcCode)?.inventory?.toList()?.storage() ?: emptyList()
        }
    }
}

internal fun `use storage from inventory`(inventorySnapshots: InventorySnapshots) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            storageInventory = inventorySnapshots.getInventory(key)?.inventory?.toList()?.storage() ?: emptyList()
        }
    }
}

internal val `empty rule` = Rule { it }

internal fun `items are expired the days in advance specified by acceptableCodeLife or have unusable state staging`(data: CalculationData) =
    `inventory partitioned by usable`(
        data,
        { stagingInventory },
        { stagingInventory = it },
        { unusableStagingInventory = it },
    )

internal fun `items are expired the days in advance specified by acceptableCodeLife or have unusable state storage`(data: CalculationData) =
    `inventory partitioned by usable`(
        data,
        { storageInventory },
        { storageInventory = it },
        { unusableStorageInventory = it },
    )

internal fun `inventory partitioned by usable`(
    data: CalculationData,
    fieldSelector: DayCalculation.() -> List<CalculationInventory>,
    setUsable: DayCalculation.(List<CalculationInventory>) -> Unit,
    setUnusable: DayCalculation.(List<CalculationInventory>) -> Unit = { },
) = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            val (usable, unusable) = fieldSelector().partition { it.isUsable(data, key, sku) }
            this.setUsable(usable)
            this.setUnusable(unusable)
        }
    }
}

internal val `opening stock is equal to inventory` = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            openingStock = dayCalculation.inventory.sumOf {
                it.qty
            }
        }
    }
}

internal val `live opening stock is equal to staging and storage` = Rule { calculations ->
    calculations.dayCalculation.apply {
        openingStagingStock = stagingStock
        openingStorageStock = storageStock
        openingStock = openingStagingStock + openingStorageStock
    }
    calculations
}

internal val `apply closing stock` = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            closingStock = inventory.sumOf {
                it.qty
            } - dailyNeeds
        }
    }
}

internal val `closing stock uses only staging` = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            closingStock =
                SkuQuantity.max(zero, (stagingStock - dailyNeeds)) + storageStock
        }
    }
}

internal val `apply closing stock live` = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            closingStock =
                (storageStock + stagingStock) - dailyNeeds
        }
    }
}

internal val hasCleardown = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        dcConfig[it.dayCalculation.key.dcCode]!!.hasCleardown
    }
}

internal val `is calculated for today` = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        todayDc(dcConfig[it.dayCalculation.key.dcCode]!!.zoneId) == it.dayCalculation.key.date
    }
}

internal fun `is triggered cleardown`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        dcConfig[it.dayCalculation.key.dcCode]!!
            .let { dc ->
                dc.hasCleardown &&
                    data.inventory.getLatestCleardown(dc.dcCode)!!.cleardownMode == CleardownMode.TRIGGERED
            }
    }
}

internal fun `the clear down occurs on last 3 days`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        val dcCode = it.dayCalculation.key.dcCode
        val currentDate = it.dayCalculation.key.date
        val zoneId = dcConfig[dcCode]!!.zoneId
        val productionStartDay = dcConfig[dcCode]!!.productionStart
        val dcWeek = DcWeek(currentDate, productionStartDay)
        val endDcWeekDate = dcWeek.getLastDateInDcWeek(productionStartDay, zoneId)
            .let { lastProductionDate ->
                if (data.mode.isPreProd()) lastProductionDate.minusDays(1) else lastProductionDate
            }

        currentDate.isAfter(endDcWeekDate.minusDays(3)) &&
            currentDate.isBefore(endDcWeekDate.plusDays(1))
    }
}

internal fun `is today and cleardown day`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    (`is calculated for today` and `is cleardown day`(data))(dcConfig)
}

internal fun `is cleardown today for live`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        todayDc(dcConfig[it.dayCalculation.key.dcCode]!!.zoneId) == it.dayCalculation.key.date &&
            data.inventory.getLiveInventory(it.dayCalculation.key)?.skuLiveInventory?.cleardownTime?.toLocalDate() == it.dayCalculation.key.date
    }
}

internal fun `is today and not cleardown`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    (`is calculated for today` and !`is cleardown day`(data))(dcConfig)
}

internal fun `is older than today and cleardown day`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    (`date older than today` and `is cleardown day`(data))(dcConfig)
}

internal fun `is older than today and not cleardown day`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    (`date older than today` and !`is cleardown day`(data))(dcConfig)
}

internal fun `serve soon to be expired inventory first`(
    fieldSelector: DayCalculation.() -> List<CalculationInventory>,
    setRemainingInventory: DayCalculation.(List<CalculationInventory>) -> Unit,
) = Rule { calculations ->
    calculations.apply {
        dayCalculation.reduceInventory(
            { dailyNeeds },
            { dailyNeeds = it },
            fieldSelector,
            setRemainingInventory,
        )
    }
}

internal fun DayCalculation.reduceInventory(
    demandSelector: DayCalculation.() -> SkuQuantity,
    setRemainingDemand: DayCalculation.(SkuQuantity) -> Unit,
    fieldSelector: DayCalculation.() -> List<CalculationInventory>,
    setRemainingInventory: DayCalculation.(List<CalculationInventory>) -> Unit,
) {
    val (remainingDemand, remainingInventory) = reduceInventory(this.fieldSelector(), demandSelector())
    this.setRemainingInventory(remainingInventory)
    this.setRemainingDemand(remainingDemand)
}

internal fun reduceInventory(
    inventory: List<CalculationInventory>,
    quantity: SkuQuantity,
): Pair<SkuQuantity, List<CalculationInventory>> {
    val result = mutableListOf<CalculationInventory>()
    var remainingQuantity = quantity

    inventory
        .sortedWith(compareBy(nullsLast()) { it.expiryDate })
        .forEach { inventoryQty ->
            if (remainingQuantity < inventoryQty.qty) {
                result.add(
                    inventoryQty.copy(
                        qty = inventoryQty.qty - remainingQuantity,
                    ),
                )
                remainingQuantity = SkuQuantity.ZERO
            } else {
                remainingQuantity -= inventoryQty.qty
            }
        }
    return remainingQuantity to result
}

internal val `date older than today` = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        it.dayCalculation.key.date < todayDc(dcConfig[it.dayCalculation.key.dcCode]!!.zoneId)
    }
}

internal fun `is cleardown day`(data: CalculationData) = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        isCleardownDate(it.dayCalculation.key.date, dcConfig[it.dayCalculation.key.dcCode]!!, data.inventory)
    }
}

internal val `date newer than today` = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Predicate {
        it.dayCalculation.key.date > todayDc(dcConfig[it.dayCalculation.key.dcCode]!!.zoneId)
    }
}

internal fun `apply demands`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                val demand = getDomainDemandData(key, data)
                this.demand = demand?.forecastedQty ?: SkuQuantity.fromLong(0)
                this.actualConsumption = demand?.actualConsumptionQty ?: zero
                if ((`is cleardown day`(data) and `is triggered cleardown`(data) and `the clear down occurs on last 3 days`(data))(
                        data.dcConfig,
                    )(calculations)
                ) {
                    this.dailyNeeds = zero
                } else {
                    this.dailyNeeds = demand?.actualDemand ?: zero
                }
            }
        }
    }

internal fun `apply automated live demands`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                val demand = getDomainDemandData(key, data)
                this.demand = demand?.forecastedQty ?: SkuQuantity.fromLong(0)
                this.actualConsumption = demand?.actualConsumptionQty ?: zero
                if ((`is calculated for today` or `date newer than today`)(data.dcConfig)(calculations)) {
                    this.dailyNeeds = demand?.actualDemand ?: zero
                }
            }
        }
    }

internal fun `apply live demands`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                val demand = getDomainDemandData(key, data)
                this.demand = demand?.forecastedQty ?: SkuQuantity.fromLong(0)
                this.actualConsumption = demand?.actualConsumptionQty ?: zero
                if ((`is cleardown today for live`(data) and `the clear down occurs on last 3 days`(data))(
                        data.dcConfig,
                    )(calculations)
                ) {
                    this.dailyNeeds = zero
                } else {
                    if ((`is calculated for today` or `date newer than today`)(data.dcConfig)(calculations)) {
                        this.dailyNeeds = demand?.actualDemand ?: zero
                    }
                }
            }
        }
    }

private fun getDomainDemandData(key: CalculationKey, data: CalculationData) = if (data.mode.isPreProd()) {
    data.demands.getDemand(key.cskuId, key.dcCode, key.nextDay().date, data.dcConfig[key.dcCode]!!.zoneId)
} else {
    data.demands.getDemand(key.cskuId, key.dcCode, key.date, data.dcConfig[key.dcCode]!!.zoneId)
}

internal fun `when cleardown day add received missing inbounds to inventory stocks`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                if (isCleardownDate(key.date, data.dcConfig[key.dcCode]!!, data.inventory)) {
                    val snapshotInbounds = data.inventory.cleardownInventorySnapshotInbounds[key.dcCode]!!
                    // On cleardown date, we get the inbounds from the previous days too, in case, the inbounds are not yet processed.
                    val recentInboundDates = snapshotInbounds.inboundsRange.dates() + key.date

                    val inbounds = data.purchaseOrderInbounds.inbounds(
                        key.cskuId,
                        key.dcCode,
                        recentInboundDates,
                    )

                    // Add just deliveries that were not inbounded before cleardown
                    convertInboundsToInventory(
                        data,
                        emptyList(),
                        inbounds.pos.flatMap { poInbound ->
                            poInbound.deliveries.filter { delivery ->
                                snapshotInbounds.inboundsRange.contains(delivery.deliveryTime) &&
                                    InboundSnapshotState.NON_INBOUNDED == snapshotInbounds.isPurchaseOrderInbounded(
                                        data.dcConfig[key.dcCode]!!,
                                        key.cskuId,
                                        delivery.sourcePo.number,
                                    )
                            }
                        },
                    ) { usable, unusable ->
                        inventory = inventory + usable
                        unusableInventory = unusableInventory + unusable
                    }
                }
            }
        }
    }

internal fun `add inbounds to inventory stocks`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                val dcConfig = getDcConfig(data, key.dcCode)
                val inbounds = data.purchaseOrderInbounds.inbounds(key.cskuId, dcConfig.dcCode, key.date)
                expectedInboundQty = inbounds.poQuantity
                expectedInboundPos = inbounds.expectedPoNumbers
                expectedCutOffPosQty = getPoCutOffQty(data, inbounds, dcConfig.poCutoffTime)
                actualInboundQty = inbounds.actualQuantity
                actualInboundPos = inbounds.actualPoNumbers

                val toInbounds = data.transferOrderInbounds.inbounds(key.cskuId, dcConfig.dcCode, key.date)
                expectedInboundTransferOrders = toInbounds.expectedToNumbers
                expectedInboundTransferOrdersQuantity = toInbounds.expectedQuantity
                actualInboundTransferOrdersQuantity = toInbounds.actualQuantity
                actualInboundTransferOrders = toInbounds.actualToNumbers

                val transferOrderOutbounds = data.transferOrderOutbounds.outbounds(
                    key.cskuId,
                    dcConfig.dcCode,
                    key.date
                )
                if (transferOrderOutbounds.actualToNumbers.isNotEmpty() && transferOrderOutbounds.actualQuantity > SkuQuantity.ZERO) {
                    expectedOutboundTransferOrders = transferOrderOutbounds.actualToNumbers
                    expectedOutboundTransferOrdersQuantity = transferOrderOutbounds.actualQuantity
                } else {
                    expectedOutboundTransferOrders = transferOrderOutbounds.expectedToNumbers
                    expectedOutboundTransferOrdersQuantity = transferOrderOutbounds.expectedQuantity
                }

                if (hasCleardown(data.dcConfig)(calculations)) {
                    addInboundsToInventory(key, data, inbounds)
                    addTransferOrderInboundsToInventory(key, data, toInbounds)
                    reduceTransferOrderOutboundsFromInventory(key, data, transferOrderOutbounds)
                } else if (
                    (`is calculated for today`(data.dcConfig)(calculations) && inbounds.actualPoNumbers.isEmpty()) ||
                    (`date newer than today`(data.dcConfig)(calculations))
                ) {
                    addInboundsToInventory(key, data, inbounds)
                    addTransferOrderInboundsToInventory(key, data, toInbounds)
                    reduceTransferOrderOutboundsFromInventory(key, data, transferOrderOutbounds)
                }
            }
        }
    }

internal fun `when cleardown day consider yesterday's demand for preprod`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                if (
                    data.preproductionCleardownDcs.contains(key.dcCode) &&
                    (`is cleardown day`(data))(data.dcConfig)(calculations) &&
                    data.mode.isPreProd()
                ) {
                    val previousDayDemandData = getDomainDemandData(key.previousDay(), data)
                    dayCalculation.reduceInventory(
                        { previousDayDemandData?.actualDemand ?: zero },
                        { },
                        { inventory },
                        { inventory = it },
                    )
                }
            }
        }
    }

private fun DayCalculation.addInboundsToInventory(
    calculationKey: CalculationKey,
    data: CalculationData,
    inbounds: Inbounds
) {
    val validActualInboundsFrom = getInventoryTimeFrom(calculationKey, data)
    convertInboundsToInventory(
        data,
        inbounds,
        { usable, unusable ->
            inventory = inventory + usable
            unusableInventory = unusableInventory + unusable
        },
    ) { delivery -> delivery.deliveryTime >= validActualInboundsFrom }
}

private fun DayCalculation.addTransferOrderInboundsToInventory(
    calculationKey: CalculationKey,
    data: CalculationData,
    toInbounds: TransferOrderInbounds
) {
    val validActualInboundsFrom = getInventoryTimeFrom(calculationKey, data)
    convertTransferOrderInboundsToInventory(
        data = data,
        toInbounds = toInbounds,
        processNewInventory = { usable, unusable ->
            inventory = inventory + usable
            unusableInventory = unusableInventory + unusable
        },
    ) { toDelivery -> toDelivery.deliveryTime >= validActualInboundsFrom }
}

private fun DayCalculation.reduceTransferOrderOutboundsFromInventory(
    calculationKey: CalculationKey,
    data: CalculationData,
    outbounds: Outbounds
) {
    val validActualOutboundsFrom = getInventoryTimeFrom(calculationKey, data)

    convertTransferOrderOutboundsToInventory(
        data = data,
        outbounds = outbounds,
        processNewInventory = { usable, unusable ->
            inventory = inventory + usable
            unusableInventory = unusableInventory + unusable
        },
    ) { toDelivery -> toDelivery.deliveryTime >= validActualOutboundsFrom }
}

internal fun `add inbounds to live inventory stock`(data: CalculationData) =
    addInboundsInLiveInventory(data, { key.date.atStartOfDay() }) { usable, unusable ->
        inventory = inventory + usable
        unusableInventory = unusableInventory + unusable
    }

internal fun `add inbounds to live cleardown storage inventory stock`(data: CalculationData) =
    addInboundsInLiveInventory(
        data,
        {
            if (isCleardownDate(key.date, data.dcConfig[key.dcCode]!!, data.inventory)) {
                getCleardownTimeToApply(data, key.dcCode)
            } else {
                data.inventory.getInventory(key)?.inventoryTime ?: key.date.atStartOfDay()
            }
        },
    ) { usable, unusable ->
        storageInventory = storageInventory + usable
        unusableStorageInventory = unusableStorageInventory + unusable
    }

internal fun addInboundsInLiveInventory(
    data: CalculationData,
    validAfterInboundTime: DayCalculation.() -> LocalDateTime,
    setInventory: DayCalculation.(usable: List<CalculationInventory>, unusable: List<CalculationInventory>) -> Unit
) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                val dcConfig = getDcConfig(data, key.dcCode)
                val inbounds =
                    data.purchaseOrderInbounds.inbounds(key.cskuId, dcConfig.dcCode, listOf(key.date))
                expectedInboundQty = inbounds.poQuantity
                expectedCutOffPosQty = getPoCutOffQty(data, inbounds, dcConfig.poCutoffTime)
                expectedInboundPos = inbounds.expectedPoNumbers
                actualInboundQty = inbounds.actualQuantity
                actualInboundPos = inbounds.actualPoNumbers

                val toInbounds = data.transferOrderInbounds.inbounds(key.cskuId, dcConfig.dcCode, key.date)
                expectedInboundTransferOrders = toInbounds.expectedToNumbers
                expectedInboundTransferOrdersQuantity = toInbounds.expectedQuantity
                actualInboundTransferOrdersQuantity = toInbounds.actualQuantity
                actualInboundTransferOrders = toInbounds.actualToNumbers

                val toOutbounds = data.transferOrderOutbounds.outbounds(key.cskuId, dcConfig.dcCode, key.date)
                if (toOutbounds.actualToNumbers.isNotEmpty() && toOutbounds.actualQuantity > SkuQuantity.ZERO) {
                    expectedOutboundTransferOrders = toOutbounds.actualToNumbers
                    expectedOutboundTransferOrdersQuantity = toOutbounds.actualQuantity
                } else {
                    expectedOutboundTransferOrders = toOutbounds.expectedToNumbers
                    expectedOutboundTransferOrdersQuantity = toOutbounds.expectedQuantity
                }

                if ((`is calculated for today` or `date newer than today`)(data.dcConfig)(calculations)) {
                    convertInboundsToInventory(data, inbounds, setInventory) { delivery ->
                        delivery.deliveryTime >= validAfterInboundTime()
                    }
                    convertTransferOrderInboundsToInventory(
                        data = data,
                        toInbounds = toInbounds,
                        processNewInventory = setInventory
                    ) { toDelivery ->
                        toDelivery.deliveryTime >= validAfterInboundTime()
                    }
                }
            }
        }
    }

private fun DayCalculation.convertInboundsToInventory(
    data: CalculationData,
    inbounds: Inbounds,
    processNewInventory:
    DayCalculation.(usable: List<CalculationInventory>, unusable: List<CalculationInventory>) -> Unit,
    selectDelivery: (delivery: DeliveryPoInbound) -> Boolean,
) =
    convertInboundsToInventory(
        data,
        inbounds.pos,
        inbounds.pos.flatMap { poInbound -> poInbound.deliveries.filter { selectDelivery(it) } },
        processNewInventory,
    )

private fun DayCalculation.convertTransferOrderInboundsToInventory(
    data: CalculationData,
    toInbounds: TransferOrderInbounds,
    processNewInventory:
    DayCalculation.(usable: List<CalculationInventory>, unusable: List<CalculationInventory>) -> Unit,
    selectDelivery: (delivery: DeliveryToInbound) -> Boolean,
) = convertTransferOrderInboundsToInventory(
    data = data,
    remainingToInbounds = toInbounds.tos,
    deliveries = toInbounds.tos.flatMap { toInbound ->
        toInbound.deliveries.filter { selectDelivery(it) }
    },
    processNewInventory = processNewInventory,
)
private fun DayCalculation.convertTransferOrderOutboundsToInventory(
    data: CalculationData,
    outbounds: Outbounds,
    processNewInventory:
    DayCalculation.(usable: List<CalculationInventory>, unusable: List<CalculationInventory>,) -> Unit,
    selectDelivery: (delivery: DeliveryToOutbound) -> Boolean,
) = convertTransferOrderOutboundsToInventory(
    data = data,
    remainingToInbounds = outbounds.tos,
    deliveries = outbounds.tos.flatMap { toOutbound ->
        toOutbound.deliveries.filter { selectDelivery(it) }
    },
    processNewInventory = processNewInventory,
)

private fun DayCalculation.convertInboundsToInventory(
    data: CalculationData,
    remainingPoInbounds: List<PoInbounds>,
    deliveries: List<DeliveryPoInbound>,
    processNewInventory:
    DayCalculation.(usable: List<CalculationInventory>, unusable: List<CalculationInventory>) -> Unit,
) {
    val remainingPoInventory = remainingPoInbounds.mapNotNull { poInbound ->
        poInbound.remainingExpectedQuantity?.let { remainingExpectedQuantity ->
            CalculationInventory(
                remainingExpectedQuantity,
                poInbound.expectedExpiryDate,
                locationType = LocationType.LOCATION_TYPE_STORAGE,
                sourcePoNumber = poInbound.sourcePo.number,
            )
        }
    }
    val deliveriesInventory = deliveries.groupBy { it.sourcePo.number to it.actualExpiryDate }
        .map { (groupKey, groupedDeliveries) ->
            val (po, expiry) = groupKey
            CalculationInventory(
                SkuQuantity.fromLong(groupedDeliveries.sumOf { it.actualQuantity }.getValue().toLong()),
                expiry,
                locationType = LocationType.LOCATION_TYPE_STORAGE,
                sourcePoNumber = groupedDeliveries.firstNotNullOfOrNull { it.sourcePo.number },
            )
        }
    val (usable, unusable) =
        (remainingPoInventory + deliveriesInventory)
            .filter { it.qty.getValue().toLong() > 0 }
            .partition { it.isNotExpired(data, key, sku) }

    processNewInventory(usable, unusable)
}

private fun DayCalculation.convertTransferOrderInboundsToInventory(
    data: CalculationData,
    remainingToInbounds: List<ToInbounds>,
    deliveries: List<DeliveryToInbound>,
    processNewInventory:
    DayCalculation.(usable: List<CalculationInventory>, unusable: List<CalculationInventory>) -> Unit,
) {
    val remainingToInventory = remainingToInbounds.mapNotNull { toInbound ->
        toInbound.remainingExpectedQuantity?.let { remainingExpectedQuantity ->
            CalculationInventory(
                qty = remainingExpectedQuantity,
                expiryDate = toInbound.expectedExpiryDate,
                locationType = LocationType.LOCATION_TYPE_STORAGE,
                sourcePoNumber = toInbound.sourceTo.transferOrderNumber,
            )
        }
    }
    val deliveriesInventory = deliveries
        .groupBy { it.sourceTo.transferOrderNumber to it.actualExpiryDate }
        .map { (groupKey, groupedDeliveries) ->
            val (_, expiry) = groupKey
            CalculationInventory(
                qty = SkuQuantity.fromLong(groupedDeliveries.sumOf { it.actualQuantity }.getValue().toLong()),
                expiryDate = expiry,
                locationType = LocationType.LOCATION_TYPE_STORAGE,
                sourcePoNumber = groupedDeliveries.firstNotNullOfOrNull { it.sourceTo.transferOrderNumber },
            )
        }
    val (usable, unusable) =
        (remainingToInventory + deliveriesInventory)
            .filter { it.qty.getValue().toLong() > 0 }
            .partition { it.isNotExpired(data, key, sku) }

    processNewInventory(usable, unusable)
}

private fun DayCalculation.convertTransferOrderOutboundsToInventory(
    data: CalculationData,
    remainingToInbounds: List<ToOutbounds>,
    deliveries: List<DeliveryToOutbound>,
    processNewInventory:
    DayCalculation.(usable: List<CalculationInventory>, unusable: List<CalculationInventory>,) -> Unit,
) {
    val remainingToInventory = remainingToInbounds.mapNotNull { toOutbound ->
        toOutbound.remainingExpectedQuantity?.let { remainingExpectedQuantity ->
            // Added - negative sign to qty, as this is an outbound
            CalculationInventory(
                qty = -remainingExpectedQuantity,
                expiryDate = toOutbound.expectedExpiryDate,
                locationType = LocationType.LOCATION_TYPE_STORAGE,
                sourcePoNumber = toOutbound.sourceTo.transferOrderNumber,
            )
        }
    }
    val deliveriesInventory = deliveries
        .groupBy { it.sourceTo.transferOrderNumber to it.actualExpiryDate }
        .map { (groupKey, groupedDeliveries) ->
            val (_, expiry) = groupKey
            // Added - negative sign to qty, as this is an outbound
            CalculationInventory(
                qty = SkuQuantity.fromLong(-groupedDeliveries.sumOf { it.actualQuantity }.getValue().toLong()),
                expiryDate = expiry,
                locationType = LocationType.LOCATION_TYPE_STORAGE,
                sourcePoNumber = groupedDeliveries.firstNotNullOfOrNull { it.sourceTo.transferOrderNumber },
            )
        }
    val (usable, unusable) =
        (remainingToInventory + deliveriesInventory)
            .partition { it.isNotExpired(data, key, sku) }

    processNewInventory(usable, unusable)
}

private fun CalculationInventory.isNotExpired(data: CalculationData, calculationKey: CalculationKey, sku: SkuSpecification) =
    !data.usableInventoryEvaluator.isExpired(
        calculationKey.dcCode,
        calculationKey.date,
        expiryDate,
        sku.acceptableCodeLife,
        sku.category,
    )

private fun CalculationInventory.isUsable(data: CalculationData, calculationKey: CalculationKey, sku: SkuSpecification) =
    data.usableInventoryEvaluator.isUsable(
        calculationKey.dcCode,
        calculationKey.date,
        locationType,
        expiryDate,
        sku.acceptableCodeLife,
        sku.category,
    ).usable

internal fun `remaining inventory is carried over to the next day`(
    fieldSelector: DayCalculation.() -> List<CalculationInventory>,
    setRemainingInventory: DayCalculation.(List<CalculationInventory>) -> Unit,
) = Rule { calculations ->
    calculations.apply {
        val nextDayCalculation = values[dayCalculation.key.nextDay()]
        nextDayCalculation?.setRemainingInventory(dayCalculation.fieldSelector())
    }
}

/**
 * Stock Updates are only meant to be used with cleardown rules,
 * if needed to be used in a different rule set (live, automated, etc.) this logic should be revisited
 */
internal val `apply stock updates if present` = Rule { calculations ->
    calculations.apply {
        dayCalculation.apply {
            stockUpdate?.let {
                if (it >= zero) {
                    inventory += CalculationInventory(it, expiryDate = null, locationType = LOCATION_TYPE_STAGING)
                } else {
                    dailyNeeds -= it
                }
            }
        }
    }
}

//  stagingInventory(n-1) + (inventory(n) - inventory(n-1))
internal val `calculate staging stock` = Rule { calculations ->
    calculations.apply {
        val closingStagingYesterday = calculations.previousDay()?.stagingInventory ?: emptyList()
        val sourceStagingYesterday = calculations.previousDay()?.stagingFromSource ?: emptyList()
        // (y1/2) - (x,y) = (y1/2)
        val newInStaging = dayCalculation.stagingFromSource.removeStaging(sourceStagingYesterday)
        dayCalculation.stagingInventory = closingStagingYesterday + newInStaging
    }
}

internal val `use previous day closing stock` =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.inventory = calculations.previousDay()?.inventory ?: emptyList()
        }
    }

internal val `use previous day closing storage stock` =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.storageInventory = calculations.previousDay()?.storageInventory ?: emptyList()
        }
    }

internal val `use previous day closing staging stock` =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.stagingInventory = calculations.previousDay()?.stagingInventory ?: emptyList()
        }
    }

internal val `net needs is consumption plus safety stock minus opening_stock` = { data: CalculationData ->
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                val consumption = getDomainDemandData(key, data)?.actualDemand ?: zero
                val dcConfig = getDcConfig(data, key.dcCode)
                netNeeds = SkuQuantity.max(
                    zero, (consumption + (safetyStock ?: zero) - openingStock),
                )

                if (dcConfig.poCutoffTime != null) {
                    expectedCutOffPosQty?.let { netNeeds = SkuQuantity.max(zero, netNeeds - it) }
                }
            }
        }
    }
}

internal fun InventorySnapshots.getInventory(calculationKey: CalculationKey) =
    getInventory(calculationKey.cskuId, calculationKey.dcCode, calculationKey.date)

internal fun isCleardownDate(
    date: LocalDate,
    dcConfiguration: DistributionCenterConfiguration,
    inventorySnapshots: InventorySnapshots
) =
    dcConfiguration.hasCleardown &&
        getLatestCleardown(dcConfiguration, inventorySnapshots)?.cleardownTime?.toLocalDate() == date

internal fun getLatestCleardown(
    dcConfiguration: DistributionCenterConfiguration,
    inventorySnapshots: InventorySnapshots
) =
    if (dcConfiguration.hasCleardown) {
        inventorySnapshots.getLatestCleardown(dcConfiguration.dcCode)!!
    } else {
        null
    }

/**
 * Return time when deliveries should be considered
 */
internal fun getInventoryTimeFrom(key: CalculationKey, data: CalculationData) =
    if (isCleardownDate(key.date, data.dcConfig[key.dcCode]!!, data.inventory)) {
        getCleardownTimeToApply(data, key.dcCode)
    } else {
        key.date.atStartOfDay()
    }

private fun getCleardownTimeToApply(
    data: CalculationData,
    dcCode: String
): LocalDateTime {
    val latestCleardown = data.inventory.getLatestCleardown(dcCode)!!
    val startOfDay = latestCleardown.cleardownTime.toLocalDate().atStartOfDay()
    return maxOf(
        minOf(
            latestCleardown.cleardownTime,
            latestCleardown.snapshot?.snapshotTime ?: latestCleardown.cleardownTime,
        ),
        startOfDay,
    )
}

/*
 * this will be the base list
 * removeList will have its items removed from the base list
 * outputList is the resulting list
 */
@VisibleForTesting
internal fun List<CalculationInventory>.removeStaging(
    removeList: List<CalculationInventory>
): List<CalculationInventory> = buildList {
    <EMAIL> { todayInventoryItem ->
        val matchingStock = removeList
            .filter {
                it.expiryDate == todayInventoryItem.expiryDate && it.locationType.isUsable() == todayInventoryItem.locationType.isUsable()
            }
            .also { check(it.size <= 1) { "Inventory is not grouped by expiry and state" } }
            .sumOf { it.qty }
            .getValue()

        if (todayInventoryItem.qty.getValue() > matchingStock) {
            this.add(
                todayInventoryItem.copy(
                    qty = SkuQuantity.fromBigDecimal(todayInventoryItem.qty.getValue() - matchingStock),
                ),
            )
        }
    }
}

internal fun todayDc(zoneId: ZoneId) = LocalDate.now(zoneId)
internal fun yesterdayDc(zoneId: ZoneId) = todayDc(zoneId).minusDays(1)

private fun getDcConfig(data: CalculationData, dcCode: String): DistributionCenterConfiguration {
    val dcConfig = data.dcConfig[dcCode]
    require(dcConfig != null) { "DC not found $dcCode" }
    return dcConfig
}

private fun getPoCutOffQty(data: CalculationData, inbounds: Inbounds, dcPoCutOffTime: LocalTime?) =
    if (dcPoCutOffTime != null) {
        data.purchaseOrderInbounds.purchaseOrdersCutOffQuantity(
            inbounds.pos,
            dcPoCutOffTime
        )
    } else {
        null
    }
