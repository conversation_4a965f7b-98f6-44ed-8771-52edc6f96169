package com.hellofresh.cif.calculator.models

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.SupplierSkuDetail
import java.util.UUID

internal data class CalculationData(
    val mode: CalculatorMode,
    val dcConfig: Map<DcCode, DistributionCenterConfiguration>,
    val skuSpecs: Map<UUID, SkuSpecification>,
    val inventory: InventorySnapshots,
    val purchaseOrderInbounds: PurchaseOrderInbounds,
    val transferOrderInbounds: TransferOrderInbounds,
    val transferOrderOutbounds: TransferOrderOutbounds,
    val demands: Demands,
    val safetyStocks: Map<SafetyStockKey, SafetyStockValue> = emptyMap(),
    val stockUpdates: Map<CalculationKey, SkuQuantity> = emptyMap(),
    val supplierSku: Map<UUID, List<SupplierSkuDetail>> = emptyMap(),
    val usableInventoryEvaluator: UsableInventoryEvaluator,
    val featureFlag: CalculatorFeatureFlag,
    val preproductionCleardownDcs: Set<String> = emptySet(),
) {

    val inventoryMovements: Map<CalculationKey, List<InventoryMovement>> =
        inventory.inventoryActivities
            .filterIsInstance<InventoryMovement>()
            .groupBy {
                CalculationKey(it.skuId, it.dcCode, it.activityTime.toLocalDate())
            }

    constructor(inputData: InputData, statsigFeatureFlagClient: StatsigFeatureFlagClient) : this(
        inputData.mode,
        inputData.dcConfig,
        inputData.skuSpecs,
        inputData.inventory,
        inputData.purchaseOrderInbounds,
        inputData.transferOrderInbounds,
        inputData.transferOrderOutbounds,
        inputData.demands,
        inputData.safetyStocks,
        inputData.stockUpdates,
        inputData.supplierSku,
        UsableInventoryEvaluator(statsigFeatureFlagClient),
        CalculatorFeatureFlag(inputData.mode, statsigFeatureFlagClient),
        inputData.preproductionCleardownDcs,
    )
}
