package com.hellofresh.cif.calculator.calculations.rules

import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.inventory
import com.hellofresh.inventory.models.InventoryMovement

@Suppress("LongMethod")
internal fun `apply opening stock unusable movements`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                if (unusableMovementsEnabled(data)) {
                    val deliveredPoNumbers = data.purchaseOrderInbounds.inbounds(
                        key.cskuId,
                        key.dcCode,
                        key.date
                    ).actualPoNumbers

                    getSortedInventoryMovements(key, data)
                        .filter { !deliveredPoNumbers.contains(it.poNumber) }
                        .forEach { inventoryMovement ->
                            if (isMovedFromUsableToUnusable(inventoryMovement)) {
                                val matchedOpeningStock = isInventoryMovementMatching(inventory, inventoryMovement)

                                val cleardownInventories = data.inventory.getLatestCleardown(key.cskuId, key.dcCode)
                                    ?.inventory?.inventory() ?: emptyList()

                                val matchedCleardownStock = isInventoryMovementMatching(
                                    cleardownInventories,
                                    inventoryMovement,
                                )
                                when {
                                    matchedOpeningStock -> {
                                        val newOpeningStocks = filterMatchingMovementLocation(
                                            inventory,
                                            inventoryMovement,
                                        )
                                        inventory = newOpeningStocks
                                        unusableInventory += listOf(
                                            inventoryMovement.toCalculationInventory(),
                                        )
                                    }

                                    matchedCleardownStock -> {
                                        unusableInventory += listOf(
                                            inventoryMovement.toCalculationInventory(),
                                        )
                                    }

                                    else -> {
                                        dayCalculation.reduceInventory(
                                            { inventoryMovement.quantity },
                                            { },
                                            { inventory },
                                            { inventory = it },
                                        )
                                        unusableInventory += listOf(
                                            inventoryMovement.toCalculationInventory(),
                                        )
                                    }
                                }
                            } else if (isMovedFromUnusableToUsable(inventoryMovement)) {
                                dayCalculation.reduceInventory(
                                    { inventoryMovement.quantity },
                                    { },
                                    { unusableInventory },
                                    { unusableInventory = it },
                                )
                                inventory += listOf(
                                    inventoryMovement.toCalculationInventory(),
                                )
                            }
                        }
                }
            }
        }
    }

@Suppress("LongMethod")
internal fun `apply inbounds unusable movements`(data: CalculationData) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                if (unusableMovementsEnabled(data)) {
                    val deliveredPoNumbers = data.purchaseOrderInbounds.inbounds(
                        key.cskuId,
                        key.dcCode,
                        key.date
                    ).actualPoNumbers

                    getSortedInventoryMovements(key, data)
                        .filter { deliveredPoNumbers.contains(it.poNumber) }
                        .forEach { inventoryMovement ->
                            if (isMovedFromUsableToUnusable(inventoryMovement)) {
                                val (usable, usablePo) = inventory.partition { it.sourcePoNumber != inventoryMovement.poNumber }
                                val unusablePoFromMovement = inventoryMovement.toCalculationInventory()
                                val (_, remainingPoUsable) = reduceInventory(usablePo, unusablePoFromMovement.qty)
                                inventory = usable + remainingPoUsable
                                unusableInventory += unusablePoFromMovement
                            } else if (isMovedFromUnusableToUsable(inventoryMovement)) {
                                val (unusable, unusablePo) = unusableInventory.partition { it.sourcePoNumber != inventoryMovement.poNumber }
                                val usablePoFromMovement = inventoryMovement.toCalculationInventory()
                                val (_, remainingPoUnusable) = reduceInventory(unusablePo, usablePoFromMovement.qty)
                                inventory += usablePoFromMovement
                                unusableInventory = unusable + remainingPoUnusable
                            }
                        }
                }
            }
        }
    }

private fun getSortedInventoryMovements(key: CalculationKey, data: CalculationData): Sequence<InventoryMovement> {
    val validInventoryMovementsFrom = getInventoryTimeFrom(key, data)
    return data.inventoryMovements[key]
        ?.asSequence()
        ?.filter { it.activityTime > validInventoryMovementsFrom.atOffset(it.activityTime.offset) }
        ?.sortedBy { it.activityTime }
        ?: emptySequence()
}

private fun filterMatchingMovementLocation(
    inventory: List<CalculationInventory>,
    inventoryMovement: InventoryMovement,
): List<CalculationInventory> =
    inventory.filter { openingStock ->
        !matchesOriginLocation(openingStock, inventoryMovement)
    }

private fun DayCalculation.unusableMovementsEnabled(data: CalculationData) =
    data.featureFlag.isUnusableMovementsFlagFunction(key.dcCode)

private fun isInventoryMovementMatching(
    inventory: List<CalculationInventory>,
    inventoryMovement: InventoryMovement
): Boolean =
    inventory.any { openingStock ->
        matchesOriginLocation(openingStock, inventoryMovement)
    }

private fun matchesOriginLocation(
    calculationInventory: CalculationInventory,
    inventoryMovement: InventoryMovement
): Boolean =
    calculationInventory.inventoryLocation == inventoryMovement.originInventoryLocation

private fun InventoryMovement.toCalculationInventory(): CalculationInventory =
    CalculationInventory(
        qty = this.quantity,
        expiryDate = this.expirationDate,
        locationType = this.destinationLocationType,
        location = this.destinationInventoryLocation.location,
        sourcePoNumber = this.poNumber,
    )

private fun isMovedFromUnusableToUsable(inventoryMovement: InventoryMovement) =
    (
        !inventoryMovement.originLocationType.isUsable() &&
            inventoryMovement.destinationLocationType.isUsable()
        )

private fun isMovedFromUsableToUnusable(inventoryMovement: InventoryMovement) =
    inventoryMovement.originLocationType.isUsable() &&
        !inventoryMovement.destinationLocationType.isUsable()
