@file:Suppress("TopLevelPropertyNaming")

package com.hellofresh.cif.calculator.calculations.rules

import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.inventory
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.inventory.models.InventorySnapshots

// Rules to initialize the calculations for the sku.
// The rules here strictly ordered, the calculations could
// differ when you change the order.
internal fun initRules(
    data: CalculationData
) = listOf(
    hasCleardown(data.dcConfig) then `start from cleardown`(data),
    (!hasCleardown then `start from today`)(data.dcConfig),
    `is automatic dc live rules`(data) then `start from yesterday`(data.dcConfig),
    `is live2 rules`(data) then `start from yesterday`(data.dcConfig),
    if (data.mode.isCleardown()) {
        (hasCleardown then `use cleardown inventory`(data.inventory))(data.dcConfig) +
            (!hasCleardown then `use inventory for today`(data.inventory))(data.dcConfig)
    } else {
        `empty rule`
    },
).fold(`empty rule`) { a, b -> a + b }

internal fun `is automatic dc live rules`(data: CalculationData) =
    Predicate { data.featureFlag.isAutomatedDcLiveRulesFlag(data.dcConfig[it.dayCalculation.key.dcCode]) }

internal fun `is live2 rules`(data: CalculationData) =
    Predicate { data.featureFlag.isLive2RulesFlagFunction(data.dcConfig[it.dayCalculation.key.dcCode]) }

internal val `start from yesterday` = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Rule { (_, dayCalculation) ->
        val (skuId, dcCode, _) = dayCalculation.key
        val yesterday = yesterdayDc(dcConfig[dcCode]!!.zoneId)
        val key = CalculationKey(skuId, dcCode, yesterday)
        val startDayCalculation = DayCalculation(key, dayCalculation.sku)
        CalculationStep(mapOf(key to startDayCalculation), startDayCalculation)
    }
}
internal val `start from cleardown` = { inputData: CalculationData ->
    Rule { (_, dayCalculation) ->
        val (skuId, dcCode, _) = dayCalculation.key
        val cleardownTime = inputData.inventory.getLatestCleardown(dcCode)!!.cleardownTime
        val key = CalculationKey(skuId, dcCode, cleardownTime.toLocalDate())
        val startDayCalculation = DayCalculation(key, dayCalculation.sku)
        CalculationStep(mapOf(key to startDayCalculation), startDayCalculation)
    }
}

internal val `start from today` = { dcConfig: Map<DcCode, DistributionCenterConfiguration> ->
    Rule { (_, dayCalculation) ->
        val (skuId, dcCode, _) = dayCalculation.key
        val today = todayDc(dcConfig[dcCode]!!.zoneId)
        val key = CalculationKey(skuId, dcCode, today)
        val startDayCalculation = DayCalculation(key, dayCalculation.sku)
        CalculationStep(mapOf(key to startDayCalculation), startDayCalculation)
    }
}

internal fun `use cleardown inventory`(inventorySnapshots: InventorySnapshots) =
    Rule { calculations ->
        calculations.apply {
            dayCalculation.apply {
                inventory =
                    inventorySnapshots.getLatestCleardown(key.cskuId, key.dcCode)?.inventory?.toList()?.inventory() ?: emptyList()
            }
        }
    }
