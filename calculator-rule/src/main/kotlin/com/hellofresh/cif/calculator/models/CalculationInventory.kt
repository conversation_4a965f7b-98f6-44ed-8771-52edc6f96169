package com.hellofresh.cif.calculator.models

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.sumOf
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryLocation
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType
import java.time.LocalDate

// TODO: Better name
data class CalculationInventory(
    val qty: SkuQuantity,
    val expiryDate: LocalDate?,
    val locationType: LocationType,
    val location: Location? = null,
    val inventoryPoReference: String? = null,
    val sourcePoNumber: String? = null,
) {

    val inventoryLocation: InventoryLocation?
        get() = location?.let { InventoryLocation(location, expiryDate) }

    companion object
}

fun List<Inventory>.storage() =
    filter { it.location.type.isStorage() }
        .map { it.toCalculationInventory() }

fun List<Inventory>.staging() =
    this.asSequence()
        .filter { it.location.type.isStaging() }
        .map { it.toCalculationInventory() }
        .groupBy { it.expiryDate to it.locationType.isUsable() }
        .map { (_, values) ->
            values.first().copy(
                qty = SkuQuantity.fromBigDecimal(values.sumOf { it.qty }.getValue()),
            )
        }

fun List<Inventory>.inventory() = map { it.toCalculationInventory() }

private fun Inventory.toCalculationInventory() = CalculationInventory(
    qty = qty,
    expiryDate = expiryDate,
    locationType = location.type,
    location = location,
    inventoryPoReference = poReference,
)
