package com.hellofresh.cif.inventoryvariance

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryRepository
import com.hellofresh.cif.inventoryvariance.InventoryVarianceService.SkuInventorySnapshot
import com.hellofresh.cif.inventoryvariance.repository.CalculationData
import com.hellofresh.cif.inventoryvariance.repository.CalculationVarianceRepository
import com.hellofresh.cif.inventoryvariance.repository.InventoryVarianceRepository
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.DayOfWeek.SUNDAY
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

@DisplayName("Inventory Variance service test")
class InventoryVarianceServiceTest {

    private val inventoryRepository = mockk<InventoryRepository>()
    private val calculationVarianceRepository = mockk<CalculationVarianceRepository>()
    private val inventoryVarianceRepository = mockk<InventoryVarianceRepository>()
    private val skuSpecificationService = mockk<SkuSpecificationService>(relaxed = true)
    private val dcConfigService = mockk<DcConfigService>()
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val sut = InventoryVarianceService(
        dcConfigService,
        inventoryRepository,
        calculationVarianceRepository,
        inventoryVarianceRepository,
        skuSpecificationService,
        statsigFeatureFlagClient,
    )
    private val usableInventoryEvaluator = UsableInventoryEvaluator(statsigFeatureFlagClient)
    private val dateString = "2023-06-02"
    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    private val startDate = LocalDate.parse(dateString, formatter)
    private val dcWeek = DcWeek(startDate, SUNDAY)
    private val defaultDcCode = "VE"
    private val defaultDistributionCenter = DistributionCenterConfiguration(
        dcCode = defaultDcCode,
        enabled = true,
        productionStart = SUNDAY,
        market = "",
        cleardown = startDate.plusDays(1).dayOfWeek,
        zoneId = ZoneId.of("Europe/Berlin"),
        hasCleardown = true,
        wmsType = WmsSystem.WMS_SYSTEM_FCMS,
    )
    private val defaultSkuCategory = "cat"

    @Test
    fun `returns the live variance data for the given dc code and dc week`() {
        // given
        val expectedProductionCleardownVariance = SkuQuantity.fromLong(59L, UOM_UNIT)
        val expectedLiveCleardownVariance = SkuQuantity.fromLong(59L, UOM_UNIT)
        val skuId = UUID.randomUUID()

        coEvery {
            skuSpecificationService.specifications
        } returns mapOf(skuId to SkuSpecification.default)

        val calculationData = listOf(
            createCalculationData(skuId, 21L, 21L, startDate),
            createCalculationData(skuId, 200L, 200L, startDate.plusDays(1)),
            createCalculationData(skuId, 300L, 300L, startDate.plusDays(2)),
            createCalculationData(skuId, 100L, 100L, startDate.plusDays(3)),
            createCalculationData(skuId, 400L, 400L, startDate.plusDays(4)),
            createCalculationData(skuId, 400L, 400L, startDate.plusDays(5)),
            createCalculationData(skuId, 400L, 400L, startDate.plusDays(6)),
        )

        // when
        coEvery {
            calculationVarianceRepository.fetchSkuCalculationData(defaultDcCode, any())
        } returns calculationData

        val inventoryData = listOf(
            createInventorySnapshot(skuId, BigDecimal(51L), startDate),
            createInventorySnapshot(skuId, BigDecimal(150L), startDate.plusDays(1)),
            createInventorySnapshot(skuId, BigDecimal(250L), startDate.plusDays(2)),
            createInventorySnapshot(skuId, BigDecimal(350L), startDate.plusDays(3)),
            createInventorySnapshot(skuId, BigDecimal(450L), startDate.plusDays(4)),
            createInventorySnapshot(skuId, BigDecimal(450L), startDate.plusDays(5)),
            createInventorySnapshot(skuId, BigDecimal(450L), startDate.plusDays(6)),
        )

        val skuInventorySnapshotData = inventoryData.flatMap { snapshot ->
            snapshot.skus.map { skuInventory ->
                SkuInventorySnapshot(
                    skuInventory = skuInventory,
                    inventorySnapshot = snapshot,
                )
            }
        }

        coEvery {
            inventoryRepository.fetchBy(setOf(defaultDcCode), any())
        } returns inventoryData

        val report = runBlocking { sut.calculateInventoryVariance(defaultDistributionCenter, dcWeek) }

        // then
        report.first().apply {
            assertEquals(skuId, this.skuId)
            assertEquals(expectedProductionCleardownVariance, cleardownVariance)
            assertEquals(expectedLiveCleardownVariance, liveVariance)
        }
        assertDailyInventoryVariance(
            calculationData,
            skuInventorySnapshotData,
            report.first().dailyInventoryVarianceData,
        )
    }

    @Test
    fun `returns the live variance data even if the cleardown date does not match the variance date ranges`() {
        // given
        val expectedVariance = SkuQuantity.fromLong(0L)
        val skuId = UUID.randomUUID()
        val calculationData = listOf(
            CalculationData(
                skuId,
                startDate.plusDays(4),
                SkuQuantity.fromLong(300L),
                SkuQuantity.fromLong(300L),
            ),
            CalculationData(
                skuId,
                startDate.plusDays(3),
                SkuQuantity.fromLong(200L),
                SkuQuantity.fromLong(200L),
            ),
            CalculationData(
                skuId,
                startDate.plusDays(2),
                SkuQuantity.fromLong(100L),
                SkuQuantity.fromLong(100L),
            ),
        )

        // when
        coEvery {
            calculationVarianceRepository.fetchSkuCalculationData(defaultDcCode, any())
        } returns calculationData
        coEvery {
            skuSpecificationService.specifications
        } returns mapOf(skuId to SkuSpecification.default.copy(category = defaultSkuCategory, acceptableCodeLife = 0))

        val inventoryData = listOf(
            createInventorySnapshot(skuId, BigDecimal(350L), startDate.plusDays(4)),
            createInventorySnapshot(skuId, BigDecimal(250L), startDate.plusDays(3)),
            createInventorySnapshot(skuId, BigDecimal(150L), startDate.plusDays(2)),
        )

        val skuInventorySnapshotData = inventoryData.flatMap { snapshot ->
            snapshot.skus.map { skuInventory ->
                SkuInventorySnapshot(
                    skuInventory = skuInventory,
                    inventorySnapshot = snapshot,
                )
            }
        }

        coEvery {
            inventoryRepository.fetchBy(setOf(defaultDcCode), any())
        } returns inventoryData

        val report = runBlocking { sut.calculateInventoryVariance(defaultDistributionCenter, dcWeek) }

        // then
        report.first().apply {
            assertEquals(this.skuId, skuId)
            assertEquals(expectedVariance, cleardownVariance)
            assertEquals(expectedVariance, liveVariance)
        }
        assertDailyInventoryVariance(
            calculationData,
            skuInventorySnapshotData,
            report.first().dailyInventoryVarianceData,
        )
    }

    @Test
    fun `returns the live variance data when the dc week starts with the cleardown date`() {
        // given
        val expectedVariance = SkuQuantity.fromLong(0L)
        val skuId = UUID.randomUUID()
        val startDayOfWeekMatchingClearDownDate = startDate.plusDays(6)
        val calculationData = listOf(
            createCalculationData(skuId, 100L, 100L, startDayOfWeekMatchingClearDownDate),
            createCalculationData(skuId, 200L, 200L, startDate.plusDays(7)),
            createCalculationData(skuId, 300L, 300L, startDate.plusDays(8)),
        )

        // when
        coEvery {
            calculationVarianceRepository.fetchSkuCalculationData(defaultDcCode, any())
        } returns calculationData
        coEvery {
            skuSpecificationService.specifications
        } returns mapOf(skuId to SkuSpecification.default)

        val inventoryData = listOf(
            createInventorySnapshot(skuId, BigDecimal(150L), startDayOfWeekMatchingClearDownDate),
            createInventorySnapshot(skuId, BigDecimal(250L), startDate.plusDays(7)),
            createInventorySnapshot(skuId, BigDecimal(350L), startDate.plusDays(8)),
        )

        val skuInventorySnapshotData = inventoryData.flatMap { snapshot ->
            snapshot.skus.map { skuInventory ->
                SkuInventorySnapshot(
                    skuInventory = skuInventory,
                    inventorySnapshot = snapshot,
                )
            }
        }

        coEvery {
            inventoryRepository.fetchBy(setOf(defaultDcCode), any())
        } returns inventoryData

        val report = runBlocking { sut.calculateInventoryVariance(defaultDistributionCenter, dcWeek) }

        // then
        report.first().apply {
            assertEquals(this.skuId, skuId)
            assertEquals(expectedVariance, cleardownVariance)
            assertEquals(expectedVariance, liveVariance)
        }
        assertDailyInventoryVariance(
            calculationData,
            skuInventorySnapshotData,
            report.first().dailyInventoryVarianceData,
        )
    }

    @Test
    fun `should return 100  variance if the inventory quantity is zero`() {
        // given
        val expectedVariance = SkuQuantity.fromLong(100L)
        val skuId = UUID.randomUUID()

        val inventoryVarianceData = getInventoryVarianceData(skuId)

        // when
        coEvery {
            calculationVarianceRepository.fetchSkuCalculationData(defaultDcCode, any())
        } returns inventoryVarianceData
        coEvery {
            skuSpecificationService.specifications
        } returns mapOf(skuId to SkuSpecification.default)

        val inventoryData = inventoryVarianceData.map { createInventorySnapshot(it.skuId, BigDecimal.ZERO, it.date) }

        val skuInventorySnapshotData = inventoryData.flatMap { snapshot ->
            snapshot.skus.map { skuInventory ->
                SkuInventorySnapshot(
                    skuInventory = skuInventory,
                    inventorySnapshot = snapshot,
                )
            }
        }

        coEvery {
            inventoryRepository.fetchBy(setOf(defaultDcCode), any())
        } returns inventoryData

        val report = runBlocking { sut.calculateInventoryVariance(defaultDistributionCenter, dcWeek) }

        // then
        report.first().apply {
            assertEquals(this.skuId, skuId)
            assertEquals(expectedVariance, cleardownVariance)
            assertEquals(expectedVariance, liveVariance)
        }
        assertDailyInventoryVariance(
            inventoryVarianceData,
            skuInventorySnapshotData,
            report.first().dailyInventoryVarianceData,
        )
    }

    @Test
    fun `should return 0  variance if the inventory quantity does not exist`() {
        // given
        val expectedVariance = SkuQuantity.fromLong(0L)
        val skuId = UUID.randomUUID()
        val inventoryVarianceData = getInventoryVarianceData(skuId)

        // when
        coEvery {
            calculationVarianceRepository.fetchSkuCalculationData(defaultDcCode, any())
        } returns inventoryVarianceData
        coEvery {
            inventoryRepository.fetchBy(setOf(defaultDcCode), any())
        } returns emptyList()
        coEvery {
            skuSpecificationService.specifications
        } returns mapOf(skuId to SkuSpecification.default)

        val report = runBlocking { sut.calculateInventoryVariance(defaultDistributionCenter, dcWeek) }

        // then
        report.first().apply {
            assertEquals(this.skuId, skuId)
            assertEquals(expectedVariance, cleardownVariance)
            assertEquals(expectedVariance, liveVariance)
        }
        assertDailyInventoryVariance(inventoryVarianceData, emptyList(), report.first().dailyInventoryVarianceData)
    }

    @Test
    fun `should return abs variance`() {
        // given
        val expectedVariance = SkuQuantity.fromLong(50L)
        val skuId = UUID.randomUUID()
        val calculationData = listOf(
            createCalculationData(skuId, 30L, 30L),
        )

        // when
        coEvery {
            calculationVarianceRepository.fetchSkuCalculationData(defaultDcCode, any())
        } returns calculationData
        coEvery {
            skuSpecificationService.specifications
        } returns mapOf(skuId to SkuSpecification.default)

        val inventoryData = listOf(createInventorySnapshot(skuId, BigDecimal(20L)))
        val skuInventorySnapshotData = inventoryData.flatMap { snapshot ->
            snapshot.skus.map { skuInventory ->
                SkuInventorySnapshot(
                    skuInventory = skuInventory,
                    inventorySnapshot = snapshot,
                )
            }
        }

        coEvery {
            inventoryRepository.fetchBy(setOf(defaultDcCode), any())
        } returns inventoryData

        val report = runBlocking { sut.calculateInventoryVariance(defaultDistributionCenter, dcWeek) }

        // then
        report.first().apply {
            assertEquals(this.skuId, skuId)
            assertEquals(expectedVariance, cleardownVariance)
            assertEquals(expectedVariance, liveVariance)
        }
        assertDailyInventoryVariance(
            calculationData,
            skuInventorySnapshotData,
            report.first().dailyInventoryVarianceData,
        )
    }

    private fun assertDailyInventoryVariance(
        calculationData: List<CalculationData>,
        skuInventorySnapshot: List<SkuInventorySnapshot>,
        dailyInventoryVarianceData: List<DailyInventoryVarianceData>
    ) {
        calculationData.forEach { data ->
            val expectedInventoryQty =
                skuInventorySnapshot.find { it.inventorySnapshot.snapshotTime.toLocalDate() == data.date }
                    ?.skuInventory
                    ?.inventory
                    ?.filter {
                        usableInventoryEvaluator.isUsable(
                            defaultDcCode,
                            data.date,
                            it,
                            0,
                            defaultSkuCategory,
                        ).usable
                    }
                    ?.sumOf { it.qty.getValue() }

            dailyInventoryVarianceData.first { data.date == it.date }.apply {
                assertEquals(expectedInventoryQty?.toLong(), inventoryQty?.getValue()?.toLong())
                assertEquals(data.cleardownClosingStock, cleardownClosingStock)
                assertEquals(data.liveClosingStock, liveClosingStock)
            }
        }
    }

    private fun createCalculationData(
        skuId: UUID,
        cleardownClosingStock: Long,
        liveClosingStock: Long,
        date: LocalDate = startDate,
        uom: SkuUOM = UOM_UNIT
    ) =
        CalculationData(
            skuId,
            date,
            SkuQuantity.fromLong(cleardownClosingStock, uom),
            SkuQuantity.fromLong(liveClosingStock, uom),
        )

    private fun createInventorySnapshot(skuId: UUID, qty: BigDecimal, date: LocalDate = startDate, uom: SkuUOM = UOM_UNIT) =
        InventorySnapshot(
            defaultDcCode,
            UUID.randomUUID(),
            date.atStartOfDay(),
            listOf(
                SkuInventory(
                    skuId,
                    listOf(
                        Inventory(
                            SkuQuantity.fromBigDecimal(qty, uom),
                            null,
                            location = Location("", LOCATION_TYPE_STAGING, null),
                        ),
                    ),
                ),
            ),
        )

    private fun getInventoryVarianceData(skuId: UUID) = listOf(
        createCalculationData(skuId, 50L, 50L, startDate),
        createCalculationData(skuId, 100L, 100L, startDate.plusDays(1)),
        createCalculationData(skuId, 200L, 200L, startDate.plusDays(2)),
        createCalculationData(skuId, 300L, 300L, startDate.plusDays(3)),
        createCalculationData(skuId, 400L, 400L, startDate.plusDays(4)),
        createCalculationData(skuId, 400L, 400L, startDate.plusDays(5)),
        createCalculationData(skuId, 400L, 400L, startDate.plusDays(6)),
    )
}
