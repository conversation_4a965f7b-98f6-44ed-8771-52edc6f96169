package com.hellofresh.cif.inventoryvariance

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.TypeFactory
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.calculation.fixtures.liveInventoryCalculationRecord
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.distribution_center_lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.distribution_center_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryRepository
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory_variance_job.schema.Tables
import com.hellofresh.cif.inventory_variance_job.schema.Tables.INVENTORY_SNAPSHOT
import com.hellofresh.cif.inventory_variance_job.schema.Tables.INVENTORY_VARIANCE
import com.hellofresh.cif.inventory_variance_job.schema.tables.records.InventorySnapshotRecord
import com.hellofresh.cif.inventory_variance_job.schema.tables.records.InventoryVarianceRecord
import com.hellofresh.cif.inventoryvariance.repository.CalculationVarianceRepository
import com.hellofresh.cif.inventoryvariance.repository.InventoryVarianceRepository
import com.hellofresh.cif.inventoryvariance.repository.toDbUOM
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_LITRE
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_specification_lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@DisplayName("Inventory Variance Job functional test")
class InventoryVarianceJobFunctionalTest {

    @ParameterizedTest
    @CsvSource("0", "2", "4", "52")
    fun `should calculate and persist inventory variances for given dc past weeks`(weeksCount: Int) {
        // given
        val givenDcWeeks = (0..weeksCount).map {
            val dcWeek = DcWeek(LocalDate.now(UTC).minusWeeks(it.toLong()), defaultProductionStart)
            val skuIds = List<UUID>(10) { UUID.randomUUID() }
            dcWeek to skuIds
        }

        val skuIdsCount = givenDcWeeks.map { it.second.size }.sum()
        val closingStockQty = BigDecimal.valueOf(2)
        val liveClosingStockQty = BigDecimal(3)
        val inventoryQty = BigDecimal(4L)
        val expectedProductionCleardownVariance = SkuQuantity.fromLong(50L)
        val expectedLiveCleardownVariance = SkuQuantity.fromLong(25L)
        givenDcWeeks.forEach {
            val givenDcWeek = it.first
            val skuIds = it.second
            skuIds.forEach { skuId ->
                persistCalculationRecords(skuId, closingStockQty, givenDcWeek, defaultProductionStart)
                persistLiveCalculationRecords(skuId, liveClosingStockQty, givenDcWeek, defaultProductionStart)
                persistInventoryRecords(skuId, inventoryQty, givenDcWeek, defaultProductionStart)
                persistSkuSpecification(skuId, 1)
            }
        }

        persistDcConfig()

        // when
        runBlocking {
            skuSpecificationService.fetchOnDemand()
            inventoryVarianceService.fillInventoryVariance(weeksInPast = weeksCount)
        }

        val inventorVarianceRecords = fetchInventorVarianceRecords()

        assertEquals(skuIdsCount, inventorVarianceRecords.size)

        inventorVarianceRecords.forEach {
            assertEquals(expectedProductionCleardownVariance, SkuQuantity.fromBigDecimal(it.cleardownVariance))
            assertEquals(expectedLiveCleardownVariance, SkuQuantity.fromBigDecimal(it.liveVariance))
            assertEquals(UOM_LITRE.toDbUOM(), it.uom)

            val dailyInventoryVarianceData = deserializeDailyInventoryVarianceData(it)
            assertEquals(7, dailyInventoryVarianceData.size)
            dailyInventoryVarianceData.forEach { dailyData ->
                assertEquals(SkuQuantity.fromBigDecimal(inventoryQty, UOM_LITRE), dailyData.inventoryQty)
                assertEquals(SkuQuantity.fromBigDecimal(closingStockQty, UOM_LITRE), dailyData.cleardownClosingStock)
                assertEquals(SkuQuantity.fromBigDecimal(liveClosingStockQty, UOM_LITRE), dailyData.liveClosingStock)
            }
        }
    }

    // This makes sense as the calculator itself will skip such sku
    @Test
    fun `should not return variance for sku with no specification`() {
        val skuId = UUID.randomUUID()
        val givenDcWeek = DcWeek(LocalDate.now(UTC), defaultProductionStart)
        persistCalculationRecords(skuId, BigDecimal(100), givenDcWeek, defaultProductionStart)
        persistLiveCalculationRecords(skuId, BigDecimal(100), givenDcWeek, defaultProductionStart)
        persistInventoryRecords(skuId, BigDecimal(100), givenDcWeek, defaultProductionStart)
        persistDcConfig()

        // when
        runBlocking {
            skuSpecificationService.fetchOnDemand()
            inventoryVarianceService.fillInventoryVariance()
        }

        val inventorVarianceRecords = fetchInventorVarianceRecords()

        assertEquals(0, inventorVarianceRecords.size)
    }

    @Test
    fun `should not count unusable (including expired) into account`() {
        // given
        val givenDcWeek = DcWeek(LocalDate.now(UTC), defaultProductionStart)
        val skus = mutableListOf(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID())
        val closingStockQty = BigDecimal(2)
        val liveClosingStockQty = BigDecimal(3)
        val inventoryQty = BigDecimal(4L)
        skus.forEachIndexed { i, skuId ->
            persistCalculationRecords(skuId, closingStockQty, givenDcWeek, defaultProductionStart, UOM_UNIT)
            persistLiveCalculationRecords(skuId, liveClosingStockQty, givenDcWeek, defaultProductionStart, UOM_UNIT)
            val inventory = when (i) {
                0 -> Inventory(
                    SkuQuantity.fromBigDecimal(inventoryQty),
                    expiryDate = null,
                    location = Location("", LOCATION_TYPE_STAGING, null),
                    null,
                )
                // unusable
                1 -> Inventory(
                    SkuQuantity.fromBigDecimal(inventoryQty),
                    expiryDate = null,
                    location = Location("", LOCATION_TYPE_QUARANTINE, null),
                    null,
                )
                // expired
                2 -> Inventory(
                    SkuQuantity.fromBigDecimal(inventoryQty),
                    expiryDate = LocalDate.now(UTC),
                    location = Location("", LOCATION_TYPE_STAGING, null),
                    null,
                )

                else -> error("bad index")
            }
            persistInventoryRecords(
                skuId,
                inventoryQty,
                givenDcWeek,
                defaultProductionStart,
                inventory.qty.unitOfMeasure,
                inventory,
            )
            persistSkuSpecification(skuId, 2, UOM_UNIT)
        }

        persistDcConfig()

        // Why skuSpecifications is null? Mock?
        // when
        runBlocking {
            skuSpecificationService.fetchOnDemand()
            inventoryVarianceService.fillInventoryVariance()
        }
        val inventorVarianceRecords = fetchInventorVarianceRecords()
        assertEquals(3, inventorVarianceRecords.size)

        inventorVarianceRecords.forEach { variance ->
            if (skus[0] == variance.skuId) {
                assertEquals(50, variance.cleardownVariance.toInt())
                assertEquals(25, variance.liveVariance.toInt()) // Issue here. Why do we get 50?
            } else {
                // Inventory is 0 due to being unusable
                assertEquals(100, variance.cleardownVariance.toInt())
                assertEquals(100, variance.liveVariance.toInt())
            }
        }
    }

    private fun fetchInventorVarianceRecords() = dsl.selectFrom(INVENTORY_VARIANCE).fetch()

    private fun deserializeDailyInventoryVarianceData(
        inventoryVarianceRecord: InventoryVarianceRecord,
    ): List<DailyInventoryVarianceData> =
        objectMapper.readValue(
            inventoryVarianceRecord.value.toString(),
            TypeFactory.defaultInstance()
                .constructCollectionType(List::class.java, DailyInventoryVarianceData::class.java),
        )

    fun persistDcConfig() {
        val dcConfigRecord = DcConfigRecord().apply {
            market = "GB"
            this.dcCode = defaultDcCode
            this.productionStart = defaultProductionStart.toString()
            zoneId = "UTC"
            enabled = true
            recordTimestamp_ = LocalDateTime.now(UTC)
            hasCleardown = true
            cleardown = defaultProductionStart.toString()
        }
        dsl.batchInsert(dcConfigRecord).execute()
    }

    fun persistCalculationRecords(
        skuId: UUID = UUID.randomUUID(),
        closingStock: BigDecimal,
        dcWeek: DcWeek,
        productionStartDay: DayOfWeek,
        uom: SkuUOM = UOM_LITRE
    ) {
        val dates = datesForDcWeek(dcWeek, productionStartDay)
        val calculationRecords = dates.map {
            Default.calculationRecord {
                this.cskuId = skuId
                this.dcCode = defaultDcCode
                this.productionWeek = dcWeek.toString()
                this.closingStock = closingStock
                this.demanded = BigDecimal.ONE
                this.date = it
                this.uom = Uom.valueOf(uom.name)
            }
        }
        dsl.batchInsert(calculationRecords).execute()
    }

    fun persistLiveCalculationRecords(
        skuId: UUID = UUID.randomUUID(),
        closingStock: BigDecimal,
        dcWeek: DcWeek,
        productionStartDay: DayOfWeek,
        uom: SkuUOM = UOM_LITRE
    ) {
        val dates = datesForDcWeek(dcWeek, productionStartDay)
        val calculationRecordsLive = dates.map {
            Default.liveInventoryCalculationRecord {
                this.cskuId = skuId
                this.dcCode = defaultDcCode
                this.productionWeek = dcWeek.toString()
                this.closingStock = closingStock
                this.storageStock = BigDecimal.ZERO
                this.stagingStock = BigDecimal.ZERO
                this.date = it
                this.uom = Uom.valueOf(uom.name)
            }
        }
        dsl.batchInsert(calculationRecordsLive).execute()
    }

    fun persistSkuSpecification(skuId: UUID, acl: Int, uom: SkuUOM = UOM_LITRE) =
        SkuSpecificationRecord().apply {
            id = skuId
            parentId = null
            category = "BAK"
            code = UUID.randomUUID().toString()
            name = UUID.randomUUID().toString()
            acceptableCodeLife = acl
            coolingType = UUID.randomUUID().toString()
            packaging = UUID.randomUUID().toString()
            market = "DACH"
            this.uom = com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.valueOf(uom.name)

            dsl.batchInsert(this).execute()
            refreshSkuView()
        }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    fun persistInventoryRecords(
        skuId: UUID = UUID.randomUUID(),
        qty: BigDecimal,
        dcWeek: DcWeek,
        cleardownDay: DayOfWeek,
        uom: SkuUOM = UOM_LITRE,
        inventory: Inventory = Inventory(
            SkuQuantity.fromBigDecimal(qty, uom),
            expiryDate = null,
            location = Location("", LOCATION_TYPE_STAGING, null),
            null,
        ),
    ): List<InventorySnapshotRecord> {
        val dates = datesForDcWeek(dcWeek, cleardownDay)
        val inventoryRecords = dates.map {
            InventorySnapshotRecord().apply {
                this.dcCode = defaultDcCode
                this.date = it
                this.skuId = skuId
                this.snapshotId = UUID.randomUUID()
                this.value = JSONB.valueOf(
                    objectMapper.writeValueAsString(
                        InventoryValue(inventory),
                    ),
                )
                this.snapshotTime = it.atTime(LocalTime.NOON).atOffset(UTC)
            }
        }
        dsl.batchInsert(inventoryRecords).execute()
        return inventoryRecords
    }

    private fun datesForDcWeek(dcWeek: DcWeek, cleardownDay: DayOfWeek) =
        DateRange(
            dcWeek.getStartDateInDcWeek(cleardownDay, UTC),
            dcWeek.getLastDateInDcWeek(cleardownDay, UTC),
        ).let {
            val days = ChronoUnit.DAYS.between(it.fromDate, it.toDate)
            (0..days).map { d ->
                it.fromDate.plusDays(d)
            }
        }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        refreshSkuView()
        dsl.deleteFrom(Tables.CALCULATION).execute()
        dsl.deleteFrom(Tables.LIVE_INVENTORY_CALCULATION).execute()
        dsl.deleteFrom(INVENTORY_SNAPSHOT).execute()
        dsl.deleteFrom(Tables.INVENTORY_VARIANCE).execute()
    }

    companion object {

        const val defaultDcCode = "XX"
        val defaultProductionStart = DayOfWeek.SUNDAY

        private val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

        lateinit var dsl: MetricsDSLContext
        lateinit var dcConfigService: DcConfigService
        lateinit var inventoryRepository: InventoryRepository
        lateinit var calculationVarianceRepository: CalculationVarianceRepository
        lateinit var inventoryVarianceRepository: InventoryVarianceRepository
        lateinit var inventoryVarianceService: InventoryVarianceService
        lateinit var skuSpecificationService: SkuSpecificationService

        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            val meterRegistry = SimpleMeterRegistry()
            dsl = DSL.using(dbConfiguration).withMetrics(meterRegistry)
            dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(dsl))
            inventoryRepository = InventoryRepositoryImpl(dsl)
            calculationVarianceRepository = CalculationVarianceRepository(dsl)
            inventoryVarianceRepository = InventoryVarianceRepository(dsl)
            skuSpecificationService = SkuSpecificationService(meterRegistry, SkuSpecificationRepositoryImpl(dsl))
            inventoryVarianceService =
                InventoryVarianceService(
                    dcConfigService = dcConfigService,
                    inventoryRepository = inventoryRepository,
                    calculationVarianceRepository = calculationVarianceRepository,
                    inventoryVarianceRepository = inventoryVarianceRepository,
                    skuSpecificationService = skuSpecificationService,
                    statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet()),
                )
        }
    }
}
