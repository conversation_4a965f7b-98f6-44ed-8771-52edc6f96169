package com.hellofresh.cif.business.stockupdate.repository

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.business.stock_update.schema.Tables.STOCK_UPDATE
import com.hellofresh.cif.business.stockupdate.model.Reason
import com.hellofresh.cif.business.stockupdate.model.StockUpdateDto
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.models.SkuQuantity
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class StockUpdateRepositoryImplTest {

    @Test
    fun `test insert stock updates`() {
        val stockUpdateDto1 = StockUpdateDto(
            skuId = UUID.randomUUID(),
            dcCode = "VE",
            date = LocalDate.now(),
            quantity = SkuQuantity.fromLong(10),
            reason = Reason.STOCK_RECOUNT,
            week = "2025-W19",
            reasonDetail = "Test reason detail",
            authorName = "John Doe",
            authorEmail = "<EMAIL>",
            deleted = false,
            version = 1
        )

        val stockUpdateDto2 = stockUpdateDto1.copy(
            skuId = UUID.randomUUID(),
            dcCode = "VR",
        )

        val stockUpdateList = listOf(stockUpdateDto1, stockUpdateDto2)

        stockUpdateRepository.upsertStockUpdate(
            dslContext = dsl,
            stockUpdates = stockUpdateList
        ).execute()

        val result = dsl.selectFrom(STOCK_UPDATE)
            .fetch()

        assertEquals(2, result.size)
        assertEquals(stockUpdateDto1.skuId, result[0].skuId)
        assertEquals(stockUpdateDto1.version, result[0].version)

        assertEquals(stockUpdateDto2.skuId, result[1].skuId)
        assertEquals(stockUpdateDto2.version, result[1].version)
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(STOCK_UPDATE).execute()
    }

    companion object {
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        private lateinit var dsl: MetricsDSLContext
        private lateinit var stockUpdateRepository: StockUpdateRepositoryImpl

        @JvmStatic
        @BeforeAll
        fun setUp() {
            // Set up the test environment
            val config = DefaultConfiguration()
                .set(dataSource)
                .set(POSTGRES)
                .set(Executors.newSingleThreadExecutor())

            dsl = DSL.using(
                config
            ).withMetrics(SimpleMeterRegistry())
            stockUpdateRepository = StockUpdateRepositoryImpl()
        }
    }
}
