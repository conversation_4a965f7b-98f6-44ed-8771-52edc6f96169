plugins {
    id("com.hellofresh.cif.common-conventions")
}

group = "$group.${project.name}"
description = """
    The module contains the library functions to shared among services. Note that this module shall only contain the
    logic and not models.
"""
extra["deploy"] = false
dependencies {
    api(libs.kafka.clients)
    api(libs.kafka.schema.registry.client)
    api(libs.jackson.kotlin)
    api(libs.jackson.jsr310)
    api(libs.jackson.databind)
    api(projects.lib.logging)
    api(projects.lib.configuration)
    api(projects.lib.checks)
    api(projects.lib.shutdown)
    api(libs.micrometer.core)
    api(libs.micrometer.registry.prometheus)
    api(libs.hellofresh.schemaregistry)
    api(libs.coroutines.core)
    api(libs.coroutines.jdk8)
    api(libs.resilience4j.kotlin)
    api(libs.resilience4j.retry)
    api(projects.lib.models)

    implementation(libs.krontab)
    implementation(libs.ktor.netty)
    implementation(libs.ktor.core)

    testImplementation(libs.restassured.core)
    testImplementation(libs.mockk)
    testImplementation(projects.lib.logging)
    testImplementation(projects.inventoryModels)
}
