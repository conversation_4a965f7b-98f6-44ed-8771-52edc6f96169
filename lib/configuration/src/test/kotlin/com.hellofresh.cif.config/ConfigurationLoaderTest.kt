package com.hellofresh.cif.config

import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ConfigurationLoaderTest {
    @BeforeEach
    fun cleanUp() {
        System.clearProperty("HF_TIER")
    }

    @Test
    fun `should load the application configuration`() {
        assertApplicationProperties()
    }

    @Test
    fun `should load the multiple application configurations`() {
        assertApplicationProperties()
        assertEquals("http://mock-test-bootstrap-server", ConfigurationLoader.getStringOrFail("bootstrap.servers"))
    }

    @Test
    fun `should load properties for a profile and an environment`() {
        val profile = "us"
        System.setProperty("HF_TIER", "test")
        assertEquals("http://mock-test-bootstrap-server", ConfigurationLoader.getStringOrFail("bootstrap.servers"))
        runBlocking {
            ConfigurationLoader.withProfile(profile) {
                // us profile overrides the properties
                assertEquals("http://us-mock-test-bootstrap-server", it.getStringOrFail("bootstrap.servers"))

                // other properties are still available
                assertEquals("value", it.getStringOrFail("property"))
            }
            assertEquals("http://mock-test-bootstrap-server", ConfigurationLoader.getStringOrFail("bootstrap.servers"))
        }
    }

    @Test
    fun `should load properties for a profile with no environment`() {
        val profile = "us"
        assertEquals("https//mock-schema-registry-url", ConfigurationLoader.getStringOrFail("schema.registry.url"))
        runBlocking {
            ConfigurationLoader.withProfile(profile) {
                // us profile overrides the properties
                assertEquals("https//us-mock-schema-registry-url", it.getStringOrFail("schema.registry.url"))

                // other properties are still available
                assertEquals("value", it.getStringOrFail("property"))
            }
            assertEquals(
                "https//mock-schema-registry-url",
                ConfigurationLoader.getStringOrFail("schema.registry.url"),
            )
        }
    }

    @Test
    fun `should override the latest application configurations`() {
        assertApplicationProperties()
        assertEquals("http://mock-test-bootstrap-server", ConfigurationLoader.getStringOrFail("bootstrap.servers"))
    }

    @Test
    fun `should load the basic configurations when the environment is local`() {
        System.setProperty("HF_TIER", "local")
        assertEquals(7, ConfigurationLoader.loadKafkaConsumerConfigurations().size)
    }

    @Test
    fun `environment variable should override application properties`() {
        System.setProperty("HF_INVENTORY_DB_MASTER_HOST_OVERRIDE", "env-master-host")
        System.setProperty("HF_TIER", "local")
        assertEquals("env-master-host", ConfigurationLoader.getStringOrFail("HF_INVENTORY_DB_MASTER_HOST_OVERRIDE"))
    }

    @Test
    fun `should get the integer value`() {
        assertEquals(10, ConfigurationLoader.getIntegerOrDefault("parallelism", 1))
    }

    @Test
    fun `should get the list value`() {
        assertEquals(listOf("AA", "BB", "CC"), ConfigurationLoader.getList("list.property"))
        assertTrue(ConfigurationLoader.getList("list.property.unknown").isEmpty())
    }

    @Test
    fun `should get map value`() {
        assertEquals(
            mapOf("K1" to listOf("V1"), "K2" to listOf("V2", "V3", "V2")),
            ConfigurationLoader.getMap("map.property"),
        )
        assertTrue(ConfigurationLoader.getList("map.property.unknown").isEmpty())
    }

    @Test
    fun `should get map value with transformation`() {
        assertEquals(
            mapOf("K1" to setOf("V1"), "K2" to setOf("V2", "V3")),
            ConfigurationLoader.getMapWithSetValue("map.property"),
        )

        assertEquals(
            mapOf("K1" to "V1", "K2" to "V2"),
            ConfigurationLoader.getMap("map.property") { it.minOf { it } },
        )
    }

    @Test
    fun `should get the valid prometheus config`() {
        val prometheusConfig = ConfigurationLoader.getPrometheusConfig()
        prometheusConfig.apply {
            assertEquals("test-config-application", applicationName)
            assertEquals("false", prometheusDescription)
            assertEquals("procurement_csku-inventory-forecast", projectName)
            assertEquals("PT1M", prometheusScrapeInterval)
        }
    }

    @Test
    fun `should get the valid environment variable`() {
        System.setProperty("HF_TIER", "test")
        assertEquals("test", ConfigurationLoader.getEnvironment())
    }

    @Test
    fun `should get the string value`() {
        assertEquals("PT5S", ConfigurationLoader.getStringOrFail("poll_timeout"))
    }

    @Test
    fun `should return default value for the non existing property`() {
        val defaultValue = "default"
        assertEquals(defaultValue, ConfigurationLoader.getStringOrDefault("non-existing-key", defaultValue))
    }

    @Test
    fun `should return null value string for the non existing property`() {
        assertNull(ConfigurationLoader.getStringIfPresent("non-existing-key"))
    }

    @Test
    fun `should return null value int for the non existing property`() {
        assertNull(ConfigurationLoader.getIntegerIfPresent("non-existing-key"))
    }

    private fun assertApplicationProperties() {
        ConfigurationLoader.apply {
            assertEquals("test-config-project", getStringOrFail("project.name"))
            assertEquals("test-config-application", getStringOrFail("application.name"))
            assertEquals("PT5S", getStringOrFail("poll_timeout"))
            assertEquals("https//mock-schema-registry-url", getStringOrFail("schema.registry.url"))
            assertEquals(20000, getIntegerOrDefault("fetch.min.bytes", 20000))
        }
    }
}
