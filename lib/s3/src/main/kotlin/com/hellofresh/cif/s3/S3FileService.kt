package com.hellofresh.cif.s3

import java.io.InputStream
import java.net.URI
import java.net.URL
import java.time.Duration
import java.time.Instant
import org.apache.logging.log4j.kotlin.Logging
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region.EU_WEST_1
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.S3Configuration
import software.amazon.awssdk.services.s3.model.GetObjectAttributesRequest
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.ObjectAttributes.E_TAG
import software.amazon.awssdk.services.s3.model.ObjectAttributes.OBJECT_PARTS
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest

class S3FileService(private val s3Client: S3Client, private val s3Presigner: S3Presigner) : AutoCloseable {

    constructor(localS3Host: String? = null) :
        this(
            localS3Host?.let {
                localS3Client(localS3Host)
            } ?: createS3Client(),
            localS3Host?.let {
                localS3PresignerClient(localS3Host)
            } ?: createS3PresignerClient(),
        )

    fun fetchObjectContent(bucket: String, key: String): InputStream =
        s3Client.getObject(
            GetObjectRequest.builder()
                .bucket(bucket)
                .key(key).build(),
        )

    fun fetchObjectSummaries(bucket: String, prefix: String): List<FileObject> {
        val request = ListObjectsV2Request.builder()
            .bucket(bucket)
            .prefix(prefix)
            .build()

        val response = s3Client.listObjectsV2(request)

        val keyList = mutableListOf<FileObject>()
        response.contents().forEach {
            keyList.add(FileObject(lastModified = it.lastModified(), key = it.key()))
        }
        return keyList
    }

    fun putObject(
        bucket: String,
        key: String,
        content: String,
        metadata: Map<String, String> = emptyMap()
    ): FileObject {
        s3Client.putObject(
            PutObjectRequest.builder()
                .bucket(bucket)
                .key(key)
                .metadata(metadata)
                .build(),
            RequestBody.fromString(content),
        )

        val objectAttributes = s3Client.getObjectAttributes(
            GetObjectAttributesRequest.builder()
                .bucket(bucket).key(key)
                .objectAttributes(
                    OBJECT_PARTS,
                    E_TAG,
                ).build(),
        )
        val lastModified = objectAttributes.lastModified()

        logger.info("File Uploaded to S3: $bucket/$key - $lastModified - eTag: ${objectAttributes.eTag()}")

        return FileObject(key, lastModified)
    }

    fun getPresignedGetUrl(
        bucket: String,
        key: String,
        expiration: Duration
    ) =
        s3Presigner.presignGetObject(
            GetObjectPresignRequest.builder()
                .signatureDuration(expiration)
                .getObjectRequest(
                    GetObjectRequest.builder()
                        .bucket(bucket)
                        .key(key)
                        .build(),
                )
                .build(),
        ).url()

    fun getPreSignedPutUrl(
        bucket: String,
        expiration: Duration,
        objectKey: String,
        metadata: Map<String, String>,
    ): URL =
        s3Presigner.presignPutObject(
            PutObjectPresignRequest.builder()
                .signatureDuration(expiration)
                .putObjectRequest(
                    PutObjectRequest.builder()
                        .bucket(bucket)
                        .key(objectKey)
                        .metadata(metadata)
                        .build(),
                )
                .build(),
        ).url()

    override fun close() {
        s3Presigner.close()
        s3Client.close()
    }

    companion object : Logging {

        val region = EU_WEST_1

        private fun localS3Client(localS3Host: String) =
            S3Client.builder()
                .region(region)
                .endpointOverride(URI.create(localS3Host))
                .serviceConfiguration(S3Configuration.builder().pathStyleAccessEnabled(true).build())
                .build()

        private fun localS3PresignerClient(localS3Host: String) =
            S3Presigner.builder()
                .region(region)
                .endpointOverride(URI.create(localS3Host))
                .serviceConfiguration(S3Configuration.builder().pathStyleAccessEnabled(true).build())
                .build()

        private fun createS3Client() =
            S3Client.builder()
                .credentialsProvider(
                    DefaultCredentialsProvider.create(),
                )
                .region(region)
                .build()

        private fun createS3PresignerClient() =
            S3Presigner.builder()
                .credentialsProvider(DefaultCredentialsProvider.create())
                .region(region)
                .build()
    }
}

data class FileObject(
    val key: String,
    val lastModified: Instant,
)
