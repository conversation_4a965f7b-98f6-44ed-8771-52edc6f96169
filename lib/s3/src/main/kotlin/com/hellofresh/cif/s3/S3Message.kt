package com.hellofresh.cif.s3

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class S3Message(
    @JsonProperty("Message")
    val message: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class S3Event(
    @JsonProperty("Records")
    val records: List<Record>? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class Record(
    val eventVersion: String? = null,
    val eventSource: String? = null,
    val awsRegion: String? = null,
    val eventTime: String? = null,
    val eventName: String? = null,
    val userIdentity: UserIdentity? = null,
    val requestParameters: RequestParameters? = null,
    val s3: S3? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class UserIdentity(
    val principalId: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class RequestParameters(
    val sourceIPAddress: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class S3(
    val s3SchemaVersion: String? = null,
    val configurationId: String? = null,
    val bucket: Bucket? = null,
    val `object`: S3Object? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class Bucket(
    val name: String? = null,
    val ownerIdentity: OwnerIdentity? = null,
    val arn: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class OwnerIdentity(
    val principalId: String? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class S3Object(
    val key: String? = null,
    val size: Long? = null,
    val eTag: String? = null,
    val versionId: String? = null,
    val sequencer: String? = null
)

data class S3FileUserIdentity(val userIdentity: UserIdentity?) {
    private val principal = userIdentity?.principalId?.substringAfterLast(":")
    val authorName = principal?.substringBefore("@")
    val authorEmail = principal?.substringAfter("@")
}
