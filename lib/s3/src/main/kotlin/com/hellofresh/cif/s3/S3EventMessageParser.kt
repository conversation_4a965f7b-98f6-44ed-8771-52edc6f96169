package com.hellofresh.cif.s3

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import java.time.Instant
import org.apache.logging.log4j.kotlin.Logging

class S3EventMessageParser {
    fun getS3File(body: String): S3File? {
        val json = prepareForS3Event(body)
        val s3Event = objectMapper.readValue(json, S3Event::class.java)
        val firstRecord = s3Event?.records?.firstOrNull()
        val key = firstRecord?.s3?.`object`?.key ?: ""
        val decodedKey = URLDecoder.decode(key, StandardCharsets.UTF_8.name())

        if (!isValidEvents(s3Event)) {
            logger.warn("Ignoring the SQS message as it is a test event. Message: $body.")
            return null
        }

        val bucket = firstRecord?.s3?.bucket?.name ?: ""

        return if (decodedKey.isEmpty() || bucket.isEmpty()) {
            logger.warn("Failed to read S3 File. Bucket: $bucket, Key: $decodedKey. | S3Message: $body}")
            null
        } else {
            S3File(bucket, decodedKey, firstRecord?.userIdentity)
        }
    }

    fun getS3Event(body: String): S3Event = objectMapper.readValue(body, S3Event::class.java)

    fun isValidEvents(event: S3Event): Boolean = event.records?.firstOrNull()?.eventName !in TEST_EVENTS

    private fun prepareForS3Event(body: String): String =
        if (objectMapper.readTree(body).has("Message")) {
            val snsMessage = objectMapper.readValue(body, S3Message::class.java)
            val unescapedMessage = snsMessage.message.replace("\\\"", "\"") // Replace escaped quotes
            objectMapper.readTree(unescapedMessage).toString()
        } else {
            body
        }

    companion object : Logging {
        private val objectMapper: ObjectMapper = jacksonObjectMapper()
            .findAndRegisterModules()

        private val TEST_EVENTS = listOf<String>("TestEvent", "s3:TestEvent")
    }
}

data class S3File(
    val bucket: String,
    val key: String,
    val userIdentity: UserIdentity? = null,
    val lastModified: Instant = Instant.now(),
) {
    val fileName: String = key.substringAfterLast('/')
}
