plugins {
    id("com.hellofresh.cif.common-conventions")
}

dependencies {
    api(libs.aws.java.sdk.sqs)
    implementation(libs.aws.java.sdk.sts)
    implementation(libs.jackson.kotlin)
    implementation(libs.jackson.jsr310)
    implementation(libs.jackson.databind)
    implementation(projects.lib.logging)
    implementation(projects.lib.configuration)
    implementation(projects.lib.s3)

    testImplementation(projects.libTests)
    testImplementation(libs.mockk)
    testImplementation(libs.coroutines.core)
    testImplementation(libs.coroutines.jdk8)
}
