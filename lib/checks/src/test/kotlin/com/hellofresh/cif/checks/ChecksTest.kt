package com.hellofresh.cif.checks

import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

internal class ChecksTest {

    @Test
    fun `checks calls all check list`() {
        val check1 = mockk<Check>()
        val check2 = mockk<Check>()
        val checks = Checks("test checks")

        checks.add(check1)
        checks.add(check2)

        runBlocking { checks.check() }

        coVerify(exactly = 1) { check1.check() }
        coVerify(exactly = 1) { check2.check() }
    }

    @Test
    fun `checks is ok if all internal checks are ok list`() {
        val check1 = { CheckResult("1", true) }
        val check2 = { CheckResult("2", true) }
        val checks = Checks("test checks")

        checks.add(check1)
        checks.add(check2)

        assertTrue(runBlocking { checks.check().result })
    }

    @Test
    fun `checks is ko if one internal check is ko`() {
        val check1 = { CheckResult("1", true) }
        val check2 = { CheckResult("2", false) }
        val checks = Checks("test checks")

        checks.add(check1)
        checks.add(check2)

        assertFalse(runBlocking { checks.check().result })
    }
}
