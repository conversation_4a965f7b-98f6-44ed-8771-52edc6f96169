package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.inventory.models.Inventory
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID

sealed interface TestData {
    var dcCode: String
    var market: String
    var productionStartDay: DayOfWeek
    var cleardownDay: DayOfWeek
    var date: LocalDate
    var skuCategory: String
    var skuId: UUID
    var skuCode: String
    var country: String
    var skuName: String
    var uom: SkuUOM
    var expired: BigDecimal
    var openingStock: BigDecimal
    var storageStock: BigDecimal?
    var stagingStock: BigDecimal?
    var stockUpdate: BigDecimal?
    var demanded: BigDecimal
    var present: BigDecimal
    var closingStock: BigDecimal
    var actualInbound: BigDecimal
    var expectedInbound: BigDecimal
    var dailyNeeds: BigDecimal
    var productionWeek: String
    var actualInboundPo: String?
    var expectedInboundPo: String?
    var supplierId: UUID
    var packaging: String
    var actualConsumption: BigDecimal
    var safetyStock: BigDecimal
    var safetyStockNeeds: BigDecimal
    var netNeeds: BigDecimal
    var unusableInventory: List<Inventory>?
}
