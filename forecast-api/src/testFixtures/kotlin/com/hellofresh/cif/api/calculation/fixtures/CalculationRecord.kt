package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.api.schema.enums.Uom.valueOf
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.LiveInventoryCalculationRecord

fun TestData.calculationRecord(fn: CalculationRecord.() -> Unit): CalculationRecord =
    this.let { testData ->
        CalculationRecord()
            .apply {
                dcCode = testData.dcCode
                cskuId = testData.skuId
                productionWeek = testData.productionWeek
                date = testData.date
                uom = valueOf(testData.uom.name)
                expired = testData.expired
                openingStock = testData.openingStock
                demanded = testData.demanded
                present = testData.present
                closingStock = testData.closingStock
                actualInbound = testData.actualInbound
                expectedInbound = testData.expectedInbound
                actualInboundPo = testData.actualInboundPo
                expectedInboundPo = testData.expectedInboundPo
                dailyNeeds = testData.dailyNeeds
                packaging = testData.packaging
                actualConsumption = testData.actualConsumption
                safetystock = testData.safetyStock
                safetystockNeeds = testData.safetyStockNeeds
                netNeeds = testData.netNeeds
                stockUpdate = testData.stockUpdate
            }.apply(fn)
    }

fun TestData.liveInventoryCalculationRecord(fn: LiveInventoryCalculationRecord.() -> Unit) =
    this.let { testData ->
        LiveInventoryCalculationRecord()
            .apply {
                dcCode = testData.dcCode
                cskuId = testData.skuId
                productionWeek = testData.productionWeek
                date = testData.date
                uom = valueOf(testData.uom.name)
                expired = testData.expired
                openingStock = testData.openingStock
                storageStock = testData.storageStock
                stagingStock = testData.stagingStock
                demanded = testData.demanded
                present = testData.present
                closingStock = testData.closingStock
                actualInbound = testData.actualInbound
                expectedInbound = testData.expectedInbound
                actualInboundPo = testData.actualInboundPo
                expectedInboundPo = testData.expectedInboundPo
                dailyNeeds = testData.dailyNeeds
                packaging = testData.packaging
                actualConsumption = testData.actualConsumption
                safetystock = testData.safetyStock
                safetystockNeeds = testData.safetyStockNeeds
                netNeeds = testData.netNeeds
            }.apply(fn)
    }
