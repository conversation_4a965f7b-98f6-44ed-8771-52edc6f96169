package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.inventory.models.Inventory
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID

object Default : TestData {
    override var dcCode = "VE"
    override var market = "DACH"
    override var productionStartDay = DayOfWeek.MONDAY
    override var cleardownDay = DayOfWeek.FRIDAY
    override var date = LocalDate.now()
    override var skuCategory = "SPI"
    override var skuId = UUID(0, 0)
    override var skuCode = "$skuCategory-123-4567"
    override var country = "DE"
    override var skuName = "Test"
    override var uom = SkuUOM.UOM_LITRE
    override var expired = ONE
    override var openingStock = ONE
    override var storageStock: BigDecimal? = null
    override var stagingStock: BigDecimal? = null
    override var stockUpdate: BigDecimal? = null
    override var demanded = ONE
    override var present = ONE
    override var closingStock = ONE
    override var actualInbound = ONE
    override var expectedInbound = ONE
    override var dailyNeeds = ONE
    override var productionWeek = "2022-W34"
    override var actualInboundPo: String? = UUID(0, 0).toString()
    override var expectedInboundPo: String? = UUID(0, 0).toString()
    override var supplierId = UUID(0, 0)
    override var packaging: String = ""
    override var actualConsumption = ONE
    override var safetyStock = ZERO
    override var safetyStockNeeds = ZERO
    override var netNeeds: BigDecimal = ZERO
    override var unusableInventory: List<Inventory>? = emptyList()
}
