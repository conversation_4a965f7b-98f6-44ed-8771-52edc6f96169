package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationViewRecord
import java.util.UUID
import kotlin.random.Random

data class CulinarySkuFixture(
    val id: UUID,
    val code: String,
    val name: String
) {
    fun getCategory() = code.take(3)
    fun toSkuSpecificationRecord() = SkuSpecificationRecord().also { self ->
        self.id = id
        self.name = name
        self.code = code
        self.category = code.take(3)
        self.acceptableCodeLife = 0
        self.coolingType = ""
        self.packaging = ""
        self.market = ""
    }

    fun toSkuSpecificationViewRecord() = SkuSpecificationViewRecord().also { self ->
        self.id = id
        self.name = name
        self.code = code
        self.category = code.take(3)
        self.acceptableCodeLife = 0
        self.coolingType = ""
        self.packaging = ""
        self.market = ""
    }
}

fun firstCulinarySku() = CulinarySkuFixture(
    id = UUID.fromString("da3db753-94f5-4c38-a0dd-cdd612d26ac8"),
    code = "PRO-00-00000-1",
    name = "First CSKU"
)
fun secondCulinarySku() = CulinarySkuFixture(
    id = UUID.fromString("8cb87631-c1e9-48e6-abb6-79c97d135b66"),
    code = "PRO-00-00000-2",
    name = "Second CSKU"
)
fun thirdCulinarySku() = CulinarySkuFixture(
    id = UUID.fromString("d487af18-3bea-4d46-a578-5bd290ab3aac"),
    code = "PRO-00-00000-3",
    name = "Third CSKU"
)

fun randomCulinarySku() = CulinarySkuFixture(
    id = UUID.randomUUID(),
    code = "PRO-00-00000-${Random(100).nextInt()}",
    name = "Culinary CSKU"
)
