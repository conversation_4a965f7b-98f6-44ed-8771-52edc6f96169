package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SkuDetail
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.safetystock.model.SkuRiskRating.MEDIUM
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID

fun SkuDetail.Companion.default(skuId: UUID) = SkuDetail(
    skuId = skuId,
    skuCode = "PHF-00-00000-00",
    skuName = "Chicken meatballs / Polpette di pollo 540g",
    skuCategory = "PHF",
    inventoryRollover = BigDecimal(10),
    stockUpdates = BigDecimal.ZERO,
    demand = BigDecimal(10),
    safetyStock = BigDecimal(10),
    safetyStockRiskMultiplier = BigDecimal.ONE,
    supplyQuantityRecommendation = BigDecimal(10),
    recommendationEnabled = false,
    multiWeekEnabled = false,
    updatedAt = OffsetDateTime.now(),
    uom = UOM_UNIT,
    skuRiskRating = MEDIUM,
    bufferPercentage = BigDecimal.ZERO,
)
