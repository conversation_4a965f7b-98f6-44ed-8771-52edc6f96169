package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendation
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID

fun SupplyQuantityRecommendation.Companion.default(skuId: UUID, dcCode: String, week: String) = SupplyQuantityRecommendation(
    skuId = skuId,
    skuCode = "PHF-00-00000-00",
    skuName = "Chicken meatballs / Polpette di pollo 540g",
    skuCategory = "PHF",
    dcCode = dcCode,
    week = week,
    demand = BigDecimal.TEN,
    inventoryRollover = BigDecimal(10),
    safetyStock = BigDecimal(10),
    supplyQuantityRecommendation = BigDecimal(10),
    recommendationEnabled = false,
    multiWeekEnabled = false,
    updatedAt = OffsetDateTime.now(),
    uom = UOM_UNIT,
)
