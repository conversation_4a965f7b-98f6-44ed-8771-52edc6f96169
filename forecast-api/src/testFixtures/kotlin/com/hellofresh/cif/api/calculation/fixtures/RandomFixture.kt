package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.inventory.models.Inventory
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID
import java.util.UUID.randomUUID
import kotlin.random.Random

class RandomFixture(seed: Long = System.currentTimeMillis()) : TestData {
    private var rand = Random(seed)
    override var dcCode = "VE"
    override var market = "DACH"
    override var productionStartDay = DayOfWeek.MONDAY
    override var cleardownDay = DayOfWeek.FRIDAY
    override var date = LocalDate.now()
    override var skuCategory = randomUUID().toString().take(3).uppercase()
    override var skuId = randomUUID()
    override var skuCode = "$skuCategory-${rand.nextInt(100, 999)}-${rand.nextInt(1000, 9999)}"
    override var country = "DE"
    override var skuName = "$skuCode-${randomUUID()}"
    override var uom = SkuUOM.entries.random(rand)
    override var expired = rand.nextDouble(10000.0).toBigDecimal()
    override var openingStock = rand.nextDouble(10000.0).toBigDecimal()
    override var storageStock: BigDecimal? = null
    override var stagingStock: BigDecimal? = null
    override var stockUpdate: BigDecimal? = rand.nextDouble(10000.0).toBigDecimal()
    override var demanded = rand.nextDouble(10000.0).toBigDecimal()
    override var present = rand.nextDouble(10000.0).toBigDecimal()
    override var closingStock = rand.nextDouble(10000.0).toBigDecimal()
    override var actualInbound = rand.nextDouble(10000.0).toBigDecimal()
    override var expectedInbound = rand.nextDouble(10000.0).toBigDecimal()
    override var dailyNeeds = rand.nextDouble(10000.0).toBigDecimal()
    override var productionWeek = "2022-W${rand.nextInt(10, 52)}"
    override var actualInboundPo: String? = randomUUID().toString()
    override var expectedInboundPo: String? = randomUUID().toString()
    override var supplierId: UUID = randomUUID()
    override var packaging: String = ""
    override var actualConsumption = rand.nextDouble(10000.0).toBigDecimal()
    override var safetyStock: BigDecimal = rand.nextDouble(10000.0).toBigDecimal()
    override var safetyStockNeeds: BigDecimal = rand.nextDouble(10000.0).toBigDecimal()
    override var netNeeds: BigDecimal = rand.nextDouble(10000.0).toBigDecimal()
    override var unusableInventory: List<Inventory>? = emptyList()
}
