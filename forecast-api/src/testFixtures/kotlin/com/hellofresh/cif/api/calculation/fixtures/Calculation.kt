package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.api.calculation.Calculation
import com.hellofresh.cif.api.calculation.DailyView
import com.hellofresh.cif.api.calculation.ProductionDay
import com.hellofresh.cif.api.calculation.Sku
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.util.UUID

const val DEFAULT_TEST_STRATEGY = "UNSPECIFIED_STRATEGY"

fun DailyView.Companion.default(
    dcCode: String = "DC",
    skuId: UUID = UUID.randomUUID()
) =
    DailyView(
        productionDay = ProductionDay(
            week = DcWeek(LocalDate.now(), MONDAY).value,
            day = LocalDate.now(),
        ),
        sku = Sku(skuId, UUID.randomUUID().toString(), UUID.randomUUID().toString(), ""),
        calculation = Calculation.default(dcCode),
    )

fun Calculation.Companion.default(dcCode: String = "DC") =
    Calculation(
        uom = UOM_UNIT,
        incomingPos = ONE,
        inbound = ONE,
        pos = emptySet(),
        usableStock = ZERO,
        unusableStock = ZERO,
        consumption = ZERO,
        dailyNeed = ZERO,
        closingStock = TEN,
        actualConsumption = ZERO,
        dcCode = dcCode,
        skuAtRisk = false,
        safetyStock = null,
        strategy = DEFAULT_TEST_STRATEGY,
        safetyStockNeeds = null,
        storageStock = ZERO,
        stagingStock = ZERO,
        poDueIn = null,
        netNeeds = TEN,
        substituted = 0,
        prekitting = 0,
    )

fun CalculationRecord.Companion.default(
    dcCode: String = "DC",
    skuId: UUID = UUID.randomUUID()
) =
    CalculationRecord(
        productionWeek = DcWeek(LocalDate.now(), MONDAY).value,
        date = LocalDate.now(),
        cskuId = skuId,
        code = UUID.randomUUID().toString(),
        name = UUID.randomUUID().toString(),
        category = UUID.randomUUID().toString(),
        coolingType = null,
        packaging = null,
        acceptableCodeLife = 0,

        dcCode = dcCode,
        uom = Uom.UOM_UNIT,
        expectedInbound = ONE,
        expectedInboundPo = null,
        actualInbound = ONE,
        actualInboundPo = null,
        openingStock = ZERO,
        expired = ZERO,
        demanded = ZERO,
        dailyNeeds = ZERO,
        closingStock = TEN,
        actualConsumption = ZERO,
        skuAtRisk = false,
        safetyStock = null,
        strategy = DEFAULT_TEST_STRATEGY,
        safetyStockNeeds = null,
        storageStock = ZERO,
        stagingStock = ZERO,
        poDueIn = null,
        netNeeds = TEN,
        subbed = null,
        consumptionDetails = null,
        stockUpdate = null,
        present = ZERO,
        expectedInboundTo = emptySet(),
        expectedInboundToQty = ZERO,
        actualInboundTo = emptySet(),
        actualInboundToQty = ZERO,
        expectedOutboundTo = emptySet(),
        expectedOutboundToQty = ZERO,
    )
