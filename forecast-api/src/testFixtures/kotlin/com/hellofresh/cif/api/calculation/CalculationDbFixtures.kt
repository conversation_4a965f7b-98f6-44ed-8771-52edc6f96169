package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.LiveInventoryCalculationRecord
import com.hellofresh.cif.api.schema.tables.records.LiveInventoryPreProductionCalculationRecord
import com.hellofresh.cif.api.schema.tables.records.PreProductionCalculationRecord
import java.math.BigDecimal
import java.time.LocalDate

const val FIXTURE_DC = "VE"

fun emptyCalculationRecord(week: String, calculationDate: LocalDate, culinarySkuFixture: CulinarySkuFixture) =
    CalculationRecord().apply {
        dcCode = FIXTURE_DC
        productionWeek = week
        cskuId = culinarySkuFixture.id
        date = calculationDate
        uom = UOM_UNIT
        expired = BigDecimal.ZERO
        openingStock = BigDecimal.ZERO
        demanded = BigDecimal.ZERO
        actualConsumption = BigDecimal.ZERO
        present = BigDecimal.ZERO
        closingStock = BigDecimal.ZERO
        actualInbound = BigDecimal.ZERO
        expectedInbound = BigDecimal.ZERO
        dailyNeeds = BigDecimal.ZERO
        maxPurchaseOrderDueIn = null
        netNeeds = BigDecimal.ZERO
        strategy = DEFAULT_TEST_STRATEGY
    }

fun emptyPreProdCalculationRecord(week: String, calculationDate: LocalDate, culinarySkuFixture: CulinarySkuFixture) =
    PreProductionCalculationRecord().apply {
        dcCode = FIXTURE_DC
        productionWeek = week
        cskuId = culinarySkuFixture.id
        date = calculationDate
        uom = UOM_UNIT
        expired = BigDecimal.ZERO
        openingStock = BigDecimal.ZERO
        demanded = BigDecimal.ZERO
        actualConsumption = BigDecimal.ZERO
        present = BigDecimal.ZERO
        closingStock = BigDecimal.ZERO
        actualInbound = BigDecimal.ZERO
        expectedInbound = BigDecimal.ZERO
        dailyNeeds = BigDecimal.ZERO
        maxPurchaseOrderDueIn = null
        netNeeds = BigDecimal.ZERO
        strategy = DEFAULT_TEST_STRATEGY
    }

fun emptyLiveInventoryCalculationRecord(
    week: String,
    calculationDate: LocalDate,
    culinarySkuFixture: CulinarySkuFixture,
    storageStockQty: BigDecimal = BigDecimal.TWO,
    stagingStockQty: BigDecimal = BigDecimal.ONE
) =
    LiveInventoryCalculationRecord().apply {
        dcCode = FIXTURE_DC
        productionWeek = week
        cskuId = culinarySkuFixture.id
        date = calculationDate
        uom = UOM_UNIT
        expired = BigDecimal.ZERO
        openingStock = BigDecimal.ZERO
        storageStock = storageStockQty
        stagingStock = stagingStockQty
        demanded = BigDecimal.ZERO
        actualConsumption = BigDecimal.ZERO
        present = BigDecimal.ZERO
        closingStock = BigDecimal.ZERO
        actualInbound = BigDecimal.ZERO
        expectedInbound = BigDecimal.ZERO
        dailyNeeds = BigDecimal.ZERO
        netNeeds = BigDecimal.ZERO
        strategy = DEFAULT_TEST_STRATEGY
    }

fun emptyLivePreProdInventoryCalculationRecord(
    week: String,
    calculationDate: LocalDate,
    culinarySkuFixture: CulinarySkuFixture,
    storageStockQty: BigDecimal = BigDecimal(2),
    stagingStockQty: BigDecimal = BigDecimal.ONE
) =
    LiveInventoryPreProductionCalculationRecord().apply {
        dcCode = FIXTURE_DC
        productionWeek = week
        cskuId = culinarySkuFixture.id
        date = calculationDate
        uom = UOM_UNIT
        expired = BigDecimal.ZERO
        openingStock = BigDecimal.ZERO
        storageStock = storageStockQty
        stagingStock = stagingStockQty
        demanded = BigDecimal.ZERO
        actualConsumption = BigDecimal.ZERO
        present = BigDecimal.ZERO
        closingStock = BigDecimal.ZERO
        actualInbound = BigDecimal.ZERO
        expectedInbound = BigDecimal.ZERO
        dailyNeeds = BigDecimal.ZERO
        netNeeds = BigDecimal.ZERO
        strategy = DEFAULT_TEST_STRATEGY
    }
