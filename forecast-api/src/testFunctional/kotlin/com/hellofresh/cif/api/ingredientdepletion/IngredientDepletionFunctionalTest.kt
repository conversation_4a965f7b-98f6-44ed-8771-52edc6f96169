package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuUOM
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionFunctionalTest : FunctionalTest() {

    private lateinit var repository: IngredientDepletionRepository
    private lateinit var service: IngredientDepletionService
    private val mockDcConfigService = mockk<DcConfigService>()

    @BeforeEach
    fun setUp() {
        repository = IngredientDepletionRepositoryImpl(dsl)
        service = IngredientDepletionService(repository, mockDcConfigService)

        // Mock DC configurations for testing
        coEvery { mockDcConfigService.dcConfigurations } returns mapOf(
            "GB01" to createDcConfig("GB01", "GB"),
            "DE01" to createDcConfig("DE01", "DE"),
            "US01" to createDcConfig("US01", "US")
        )
    }

    @Test
    fun `should fetch ingredient depletion data from database`() = runBlocking {
        // Given
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val dcCodes = listOf("GB01", "DE01")

        // Insert test SKUs
        val sku1 = SkuSpecificationRecord().apply {
            id = skuId1
            code = "SKU001"
            name = "Organic Tomatoes"
            category = "Vegetables"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        val sku2 = SkuSpecificationRecord().apply {
            id = skuId2
            code = "SKU002"
            name = "Fresh Basil"
            category = "Herbs"
            uom = Uom.UOM_UNIT
            acceptableCodeLife = 3
            packaging = "Bunch"
            coolingType = "Chilled"
            market = "DE"
        }

        insertSkuRecords(sku1, sku2)

        // Insert calculation records
        val calculation1 = CalculationRecord().apply {
            dcCode = "GB01"
            cskuId = skuId1
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("10.5")
            openingStock = BigDecimal("5.0")
            netNeeds = BigDecimal("5.5")
            strategy = "TEST_STRATEGY"
        }

        val calculation2 = CalculationRecord().apply {
            dcCode = "DE01"
            cskuId = skuId2
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("20.0")
            openingStock = BigDecimal("8.0")
            netNeeds = BigDecimal("12.0")
            strategy = "TEST_STRATEGY"
        }

        dsl.batchInsert(calculation1, calculation2).execute()

        // When
        val result = repository.getIngredientDepletionData(dcCodes)

        // Then
        assertEquals(2, result.ingredientSummary.size)

        val tomatoes = result.ingredientSummary.find { it.skuCode == "SKU001" }!!
        assertEquals("GB01", tomatoes.site)
        assertEquals("SKU001", tomatoes.skuCode)
        assertEquals("Organic Tomatoes", tomatoes.skuName)
        assertEquals("Vegetables", tomatoes.category)
        assertEquals(SkuUOM.UOM_KG, tomatoes.uom)

        val basil = result.ingredientSummary.find { it.skuCode == "SKU002" }!!
        assertEquals("DE01", basil.site)
        assertEquals("SKU002", basil.skuCode)
        assertEquals("Fresh Basil", basil.skuName)
        assertEquals("Herbs", basil.category)
        assertEquals(SkuUOM.UOM_UNIT, basil.uom)
    }

    @Test
    fun `should return empty result when no calculations exist for dc codes`() = runBlocking {
        // Given
        val dcCodes = listOf("NONEXISTENT")

        // When
        val result = repository.getIngredientDepletionData(dcCodes)

        // Then
        assertTrue(result.ingredientSummary.isEmpty())
    }

    @Test
    fun `should only return SKUs that exist in calculation table`() = runBlocking {
        // Given
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val dcCode = "GB01"

        // Insert two SKUs
        val sku1 = SkuSpecificationRecord().apply {
            id = skuId1
            code = "SKU001"
            name = "SKU with calculation"
            category = "Category1"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        val sku2 = SkuSpecificationRecord().apply {
            id = skuId2
            code = "SKU002"
            name = "SKU without calculation"
            category = "Category2"
            uom = Uom.UOM_UNIT
            acceptableCodeLife = 3
            packaging = "Bunch"
            coolingType = "Chilled"
            market = "GB"
        }

        insertSkuRecords(sku1, sku2)

        // Insert calculation only for first SKU
        val calculation = CalculationRecord().apply {
            dcCode = dcCode
            cskuId = skuId1
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("10.0")
            openingStock = BigDecimal("5.0")
            netNeeds = BigDecimal("5.0")
            strategy = "TEST_STRATEGY"
        }

        dsl.batchInsert(calculation).execute()

        // When
        val result = repository.getIngredientDepletionData(listOf(dcCode))

        // Then
        assertEquals(1, result.ingredientSummary.size)
        assertEquals("SKU001", result.ingredientSummary[0].skuCode)
        assertEquals("SKU with calculation", result.ingredientSummary[0].skuName)
    }

    @Test
    fun `service should filter dc codes by brand when no dc codes provided`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val skuId = UUID.randomUUID()

        // Insert test data for multiple DCs
        val sku = SkuSpecificationRecord().apply {
            id = skuId
            code = "SKU001"
            name = "Test SKU"
            category = "Test Category"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        insertSkuRecords(sku)

        // Insert calculations for all DCs that support hellofresh
        val calculations = listOf("GB01", "DE01", "US01").map { dcCode ->
            CalculationRecord().apply {
                this.dcCode = dcCode
                cskuId = skuId
                date = LocalDate.now()
                productionWeek = "2024-W01"
                demanded = BigDecimal("10.0")
                openingStock = BigDecimal("5.0")
                netNeeds = BigDecimal("5.0")
                strategy = "TEST_STRATEGY"
            }
        }

        dsl.batchInsert(calculations).execute()

        // When
        val result = service.getIngredientDepletionData(brand, null)

        // Then
        assertEquals(3, result.ingredientSummary.size)
        val dcCodes = result.ingredientSummary.map { it.site }.toSet()
        assertEquals(setOf("GB01", "DE01", "US01"), dcCodes)
    }

    @Test
    fun `service should use provided dc codes regardless of brand support`() = runBlocking {
        // Given
        val brand = "everyplate" // Only supported by US01
        val providedDcCodes = listOf("GB01", "DE01") // These don't support everyplate
        val skuId = UUID.randomUUID()

        // Insert test data
        val sku = SkuSpecificationRecord().apply {
            id = skuId
            code = "SKU001"
            name = "Test SKU"
            category = "Test Category"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        insertSkuRecords(sku)

        // Insert calculations for the provided DC codes
        val calculations = providedDcCodes.map { dcCode ->
            CalculationRecord().apply {
                this.dcCode = dcCode
                cskuId = skuId
                date = LocalDate.now()
                productionWeek = "2024-W01"
                demanded = BigDecimal("10.0")
                openingStock = BigDecimal("5.0")
                netNeeds = BigDecimal("5.0")
                strategy = "TEST_STRATEGY"
            }
        }

        dsl.batchInsert(calculations).execute()

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes)

        // Then
        assertEquals(2, result.ingredientSummary.size)
        val dcCodes = result.ingredientSummary.map { it.site }.toSet()
        assertEquals(setOf("GB01", "DE01"), dcCodes)
    }

    @Test
    fun `service should return empty result for unsupported brand when no dc codes provided`() = runBlocking {
        // Given
        val brand = "unsupported-brand"

        // When
        val result = service.getIngredientDepletionData(brand, null)

        // Then
        assertTrue(result.ingredientSummary.isEmpty())
    }
}
