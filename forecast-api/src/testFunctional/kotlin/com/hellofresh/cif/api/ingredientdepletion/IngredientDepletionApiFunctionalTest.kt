package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.serialization.kotlinx.json.json
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.testing.testApplication
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionApiFunctionalTest : FunctionalTest() {

    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @BeforeEach
    fun setUp() {
        // Create test DC configurations
        createDcConfig("GB01", "GB")
        createDcConfig("DE01", "DE")
        createDcConfig("US01", "US")
    }

    @Test
    fun `should return ingredient depletion data via API`() = runBlocking {
        // Given
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val brand = "hellofresh"
        val dcCodes = "GB01,DE01"

        // Insert test SKUs
        val sku1 = SkuSpecificationRecord().apply {
            id = skuId1
            code = "SKU001"
            name = "Organic Tomatoes"
            category = "Vegetables"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        val sku2 = SkuSpecificationRecord().apply {
            id = skuId2
            code = "SKU002"
            name = "Fresh Basil"
            category = "Herbs"
            uom = Uom.UOM_UNIT
            acceptableCodeLife = 3
            packaging = "Bunch"
            coolingType = "Chilled"
            market = "DE"
        }

        insertSkuRecords(sku1, sku2)

        // Insert calculation records
        val calculation1 = CalculationRecord().apply {
            dcCode = "GB01"
            cskuId = skuId1
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("10.5")
            openingStock = BigDecimal("5.0")
            netNeeds = BigDecimal("5.5")
            strategy = "TEST_STRATEGY"
        }

        val calculation2 = CalculationRecord().apply {
            dcCode = "DE01"
            cskuId = skuId2
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("20.0")
            openingStock = BigDecimal("8.0")
            netNeeds = BigDecimal("12.0")
            strategy = "TEST_STRATEGY"
        }

        dsl.batchInsert(calculation1, calculation2).execute()

        // When
        val response = getIngredientDepletionResponse("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes")

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(2, responseBody.ingredientSummary.size)

        val tomatoes = responseBody.ingredientSummary.find { it.skuCode == "SKU001" }!!
        assertEquals("GB01", tomatoes.site)
        assertEquals("SKU001", tomatoes.skuCode)
        assertEquals("Organic Tomatoes", tomatoes.skuName)
        assertEquals("Vegetables", tomatoes.category)
        assertEquals("UOM_KG", tomatoes.uom.toString())
        assertEquals(brand, tomatoes.brand)

        val basil = responseBody.ingredientSummary.find { it.skuCode == "SKU002" }!!
        assertEquals("DE01", basil.site)
        assertEquals("SKU002", basil.skuCode)
        assertEquals("Fresh Basil", basil.skuName)
        assertEquals("Herbs", basil.category)
        assertEquals("UOM_UNIT", basil.uom.toString())
        assertEquals(brand, basil.brand)
    }

    @Test
    fun `should return ingredient depletion data without dc codes parameter`() = runBlocking {
        // Given
        val skuId = UUID.randomUUID()
        val brand = "hellofresh"

        // Insert test SKU
        val sku = SkuSpecificationRecord().apply {
            id = skuId
            code = "SKU001"
            name = "Test SKU"
            category = "Test Category"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        insertSkuRecords(sku)

        // Insert calculation record
        val calculation = CalculationRecord().apply {
            dcCode = "GB01"
            cskuId = skuId
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("10.0")
            openingStock = BigDecimal("5.0")
            netNeeds = BigDecimal("5.0")
            strategy = "TEST_STRATEGY"
        }

        dsl.batchInsert(calculation).execute()

        // When
        val response = getIngredientDepletionResponse("/api/v1/ingredient-depletion?brand=$brand")

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertTrue(responseBody.ingredientSummary.isNotEmpty())
        assertEquals("SKU001", responseBody.ingredientSummary[0].skuCode)
        assertEquals(brand, responseBody.ingredientSummary[0].brand)
    }

    @Test
    fun `should return bad request when brand parameter is missing`() = runBlocking {
        // When
        val response = getIngredientDepletionResponse("/api/v1/ingredient-depletion")

        // Then
        assertEquals(HttpStatusCode.BadRequest, response.status)
    }

    @Test
    fun `should return empty result when no calculations exist for dc codes`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val dcCodes = "NONEXISTENT"

        // When
        val response = getIngredientDepletionResponse("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes")

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertTrue(responseBody.ingredientSummary.isEmpty())
    }

    @Test
    fun `should handle dc codes with spaces correctly`() = runBlocking {
        // Given
        val skuId = UUID.randomUUID()
        val brand = "hellofresh"
        val dcCodes = "GB01, DE01 , US01" // With spaces

        // Insert test SKU
        val sku = SkuSpecificationRecord().apply {
            id = skuId
            code = "SKU001"
            name = "Test SKU"
            category = "Test Category"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        insertSkuRecords(sku)

        // Insert calculation records for all DCs
        val calculations = listOf("GB01", "DE01", "US01").map { dcCode ->
            CalculationRecord().apply {
                this.dcCode = dcCode
                cskuId = skuId
                date = LocalDate.now()
                productionWeek = "2024-W01"
                demanded = BigDecimal("10.0")
                openingStock = BigDecimal("5.0")
                netNeeds = BigDecimal("5.0")
                strategy = "TEST_STRATEGY"
            }
        }

        dsl.batchInsert(calculations).execute()

        // When
        val response = getIngredientDepletionResponse("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes")

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(3, responseBody.ingredientSummary.size)
        val returnedDcCodes = responseBody.ingredientSummary.map { it.site }.toSet()
        assertEquals(setOf("GB01", "DE01", "US01"), returnedDcCodes)
    }

    @Test
    fun `should require authentication`() = runBlocking {
        // Given
        val brand = "hellofresh"

        // When
        val response = getIngredientDepletionResponseWithoutAuth("/api/v1/ingredient-depletion?brand=$brand")

        // Then
        assertEquals(HttpStatusCode.Unauthorized, response.status)
    }

    private fun getIngredientDepletionResponse(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                install(ContentNegotiation) {
                    json()
                }
                ingredientDepletionModule()
            }
            response = client.get(url) {
                addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun getIngredientDepletionResponseWithoutAuth(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                install(ContentNegotiation) {
                    json()
                }
                ingredientDepletionModule()
            }
            response = client.get(url)
        }
        return response
    }
}
