package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.AuthUtils
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionApiFunctionalTest : FunctionalTest() {

    @BeforeEach
    fun setUp() {
        // Create test DC configurations
        createDcConfig("GB01", "GB")
        createDcConfig("DE01", "DE")
        createDcConfig("US01", "US")
    }

    @Test
    fun `should return ingredient depletion data via API`() {
        // Given
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val brand = "hellofresh"
        val dcCodes = "GB01,DE01"

        // Insert test SKUs
        val sku1 = SkuSpecificationRecord().apply {
            id = skuId1
            code = "SKU001"
            name = "Organic Tomatoes"
            category = "Vegetables"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        val sku2 = SkuSpecificationRecord().apply {
            id = skuId2
            code = "SKU002"
            name = "Fresh Basil"
            category = "Herbs"
            uom = Uom.UOM_UNIT
            acceptableCodeLife = 3
            packaging = "Bunch"
            coolingType = "Chilled"
            market = "DE"
        }

        insertSkuRecords(sku1, sku2)

        // Insert calculation records
        val calculation1 = CalculationRecord().apply {
            dcCode = "GB01"
            cskuId = skuId1
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("10.5")
            openingStock = BigDecimal("5.0")
            netNeeds = BigDecimal("5.5")
            strategy = "TEST_STRATEGY"
        }

        val calculation2 = CalculationRecord().apply {
            dcCode = "DE01"
            cskuId = skuId2
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("20.0")
            openingStock = BigDecimal("8.0")
            netNeeds = BigDecimal("12.0")
            strategy = "TEST_STRATEGY"
        }

        dsl.batchInsert(calculation1, calculation2).execute()

        // When
        val response = given()
            .contentType(ContentType.JSON)
            .header("Authorization", "Bearer ${AuthUtils.generateJwtToken(jwtSecret, testSuperManagerRole)}")
            .queryParam("brand", brand)
            .queryParam("dcCodes", dcCodes)
            .`when`()
            .get("/api/v1/ingredient-depletion")
            .then()
            .statusCode(200)
            .extract()
            .`as`(IngredientDepletionResponse::class.java)

        // Then
        assertEquals(2, response.ingredientSummary.size)

        val tomatoes = response.ingredientSummary.find { it.skuCode == "SKU001" }!!
        assertEquals("GB01", tomatoes.site)
        assertEquals("SKU001", tomatoes.skuCode)
        assertEquals("Organic Tomatoes", tomatoes.skuName)
        assertEquals("Vegetables", tomatoes.category)
        assertEquals("UOM_KG", tomatoes.uom.toString())
        assertEquals(brand, tomatoes.brand)

        val basil = response.ingredientSummary.find { it.skuCode == "SKU002" }!!
        assertEquals("DE01", basil.site)
        assertEquals("SKU002", basil.skuCode)
        assertEquals("Fresh Basil", basil.skuName)
        assertEquals("Herbs", basil.category)
        assertEquals("UOM_UNIT", basil.uom.toString())
        assertEquals(brand, basil.brand)
    }

    @Test
    fun `should return ingredient depletion data without dc codes parameter`() {
        // Given
        val skuId = UUID.randomUUID()
        val brand = "hellofresh"

        // Insert test SKU
        val sku = SkuSpecificationRecord().apply {
            id = skuId
            code = "SKU001"
            name = "Test SKU"
            category = "Test Category"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        insertSkuRecords(sku)

        // Insert calculation record
        val calculation = CalculationRecord().apply {
            dcCode = "GB01"
            cskuId = skuId
            date = LocalDate.now()
            productionWeek = "2024-W01"
            demanded = BigDecimal("10.0")
            openingStock = BigDecimal("5.0")
            netNeeds = BigDecimal("5.0")
            strategy = "TEST_STRATEGY"
        }

        dsl.batchInsert(calculation).execute()

        // When
        val response = given()
            .contentType(ContentType.JSON)
            .header("Authorization", "Bearer ${AuthUtils.generateJwtToken(jwtSecret, testSuperManagerRole)}")
            .queryParam("brand", brand)
            .`when`()
            .get("/api/v1/ingredient-depletion")
            .then()
            .statusCode(200)
            .extract()
            .`as`(IngredientDepletionResponse::class.java)

        // Then
        assertTrue(response.ingredientSummary.isNotEmpty())
        assertEquals("SKU001", response.ingredientSummary[0].skuCode)
        assertEquals(brand, response.ingredientSummary[0].brand)
    }

    @Test
    fun `should return bad request when brand parameter is missing`() {
        // When & Then
        given()
            .contentType(ContentType.JSON)
            .header("Authorization", "Bearer ${AuthUtils.generateJwtToken(jwtSecret, testSuperManagerRole)}")
            .`when`()
            .get("/api/v1/ingredient-depletion")
            .then()
            .statusCode(400)
    }

    @Test
    fun `should return empty result when no calculations exist for dc codes`() {
        // Given
        val brand = "hellofresh"
        val dcCodes = "NONEXISTENT"

        // When
        val response = given()
            .contentType(ContentType.JSON)
            .header("Authorization", "Bearer ${AuthUtils.generateJwtToken(jwtSecret, testSuperManagerRole)}")
            .queryParam("brand", brand)
            .queryParam("dcCodes", dcCodes)
            .`when`()
            .get("/api/v1/ingredient-depletion")
            .then()
            .statusCode(200)
            .extract()
            .`as`(IngredientDepletionResponse::class.java)

        // Then
        assertTrue(response.ingredientSummary.isEmpty())
    }

    @Test
    fun `should handle dc codes with spaces correctly`() {
        // Given
        val skuId = UUID.randomUUID()
        val brand = "hellofresh"
        val dcCodes = "GB01, DE01 , US01" // With spaces

        // Insert test SKU
        val sku = SkuSpecificationRecord().apply {
            id = skuId
            code = "SKU001"
            name = "Test SKU"
            category = "Test Category"
            uom = Uom.UOM_KG
            acceptableCodeLife = 7
            packaging = "Box"
            coolingType = "Chilled"
            market = "GB"
        }

        insertSkuRecords(sku)

        // Insert calculation records for all DCs
        val calculations = listOf("GB01", "DE01", "US01").map { dcCode ->
            CalculationRecord().apply {
                this.dcCode = dcCode
                cskuId = skuId
                date = LocalDate.now()
                productionWeek = "2024-W01"
                demanded = BigDecimal("10.0")
                openingStock = BigDecimal("5.0")
                netNeeds = BigDecimal("5.0")
                strategy = "TEST_STRATEGY"
            }
        }

        dsl.batchInsert(calculations).execute()

        // When
        val response = given()
            .contentType(ContentType.JSON)
            .header("Authorization", "Bearer ${AuthUtils.generateJwtToken(jwtSecret, testSuperManagerRole)}")
            .queryParam("brand", brand)
            .queryParam("dcCodes", dcCodes)
            .`when`()
            .get("/api/v1/ingredient-depletion")
            .then()
            .statusCode(200)
            .extract()
            .`as`(IngredientDepletionResponse::class.java)

        // Then
        assertEquals(3, response.ingredientSummary.size)
        val returnedDcCodes = response.ingredientSummary.map { it.site }.toSet()
        assertEquals(setOf("GB01", "DE01", "US01"), returnedDcCodes)
    }

    @Test
    fun `should require authentication`() {
        // Given
        val brand = "hellofresh"

        // When & Then
        given()
            .contentType(ContentType.JSON)
            .queryParam("brand", brand)
            .`when`()
            .get("/api/v1/ingredient-depletion")
            .then()
            .statusCode(401)
    }
}
