package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.calculation.generated.model.IngredientSummary
import com.hellofresh.cif.models.SkuUOM
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.auth.authenticate
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.jwt.jwt
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import io.ktor.serialization.kotlinx.json.json
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.delay
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionModuleTest {

    private val ingredientDepletionService = mockk<IngredientDepletionService>()
    private val timeout = 30.seconds

    @BeforeEach
    fun setUp() {
        // Reset mocks before each test
        io.mockk.clearMocks(ingredientDepletionService)
    }

    @Test
    fun `should return ingredient depletion data successfully`() = testApplication {
        // Given
        val brand = "hellofresh"
        val dcCodes = "GB01,US01"
        val expectedServiceResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                )
            )
        )

        coEvery {
            ingredientDepletionService.getIngredientDepletionData(brand, listOf("GB01", "US01"))
        } returns expectedServiceResponse

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(ingredientDepletionService, timeout.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(1, responseBody.ingredientSummary.size)
        assertEquals("GB01", responseBody.ingredientSummary[0].site)
        assertEquals("SKU001", responseBody.ingredientSummary[0].skuCode)
        assertEquals("Test SKU", responseBody.ingredientSummary[0].skuName)
        assertEquals("Test Category", responseBody.ingredientSummary[0].category)
        assertEquals("UOM_UNIT", responseBody.ingredientSummary[0].uom)
        assertEquals(brand, responseBody.ingredientSummary[0].brand)

        coVerify { ingredientDepletionService.getIngredientDepletionData(brand, listOf("GB01", "US01")) }
    }

    @Test
    fun `should return ingredient depletion data without dc codes parameter`() = testApplication {
        // Given
        val brand = "hellofresh"
        val expectedServiceResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_KG
                )
            )
        )

        coEvery {
            ingredientDepletionService.getIngredientDepletionData(brand, null)
        } returns expectedServiceResponse

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(ingredientDepletionService, timeout.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(1, responseBody.ingredientSummary.size)
        assertEquals("UOM_KG", responseBody.ingredientSummary[0].uom)

        coVerify { ingredientDepletionService.getIngredientDepletionData(brand, null) }
    }

    @Test
    fun `should return bad request when brand parameter is missing`() = testApplication {
        application {
            configureTestApplication()
            routing {
                ingredientDepletion(ingredientDepletionService, timeout.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.BadRequest, response.status)
        assertTrue(response.bodyAsText().contains("Brand parameter is required"))
    }

    @Test
    fun `should handle service exception and return internal server error`() = testApplication {
        // Given
        val brand = "hellofresh"
        val exception = RuntimeException("Service error")

        coEvery {
            ingredientDepletionService.getIngredientDepletionData(brand, null)
        } throws exception

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(ingredientDepletionService, timeout.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.InternalServerError, response.status)
    }

    @Test
    fun `should handle dc codes with spaces correctly`() = testApplication {
        // Given
        val brand = "hellofresh"
        val dcCodes = "GB01, US01 , DE01"
        val expectedServiceResponse = IngredientDepletionResponseDto(ingredientSummary = emptyList())

        coEvery {
            ingredientDepletionService.getIngredientDepletionData(brand, listOf("GB01", "US01", "DE01"))
        } returns expectedServiceResponse

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(ingredientDepletionService, timeout.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)
        coVerify { ingredientDepletionService.getIngredientDepletionData(brand, listOf("GB01", "US01", "DE01")) }
    }

    @Test
    fun `should handle empty ingredient summary list`() = testApplication {
        // Given
        val brand = "hellofresh"
        val expectedServiceResponse = IngredientDepletionResponseDto(ingredientSummary = emptyList())

        coEvery {
            ingredientDepletionService.getIngredientDepletionData(brand, null)
        } returns expectedServiceResponse

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(ingredientDepletionService, timeout.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(0, responseBody.ingredientSummary.size)
    }

    @Test
    fun `should map multiple ingredient summaries correctly`() = testApplication {
        // Given
        val brand = "hellofresh"
        val expectedServiceResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU 1",
                    category = "Category 1",
                    uom = SkuUOM.UOM_UNIT
                ),
                IngredientSummaryDto(
                    site = "US01",
                    skuCode = "SKU002",
                    skuName = "Test SKU 2",
                    category = "Category 2",
                    uom = SkuUOM.UOM_KG
                )
            )
        )

        coEvery {
            ingredientDepletionService.getIngredientDepletionData(brand, null)
        } returns expectedServiceResponse

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(ingredientDepletionService, timeout.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(2, responseBody.ingredientSummary.size)

        // Verify first ingredient
        assertEquals("GB01", responseBody.ingredientSummary[0].site)
        assertEquals("SKU001", responseBody.ingredientSummary[0].skuCode)
        assertEquals(brand, responseBody.ingredientSummary[0].brand)

        // Verify second ingredient
        assertEquals("US01", responseBody.ingredientSummary[1].site)
        assertEquals("SKU002", responseBody.ingredientSummary[1].skuCode)
        assertEquals(brand, responseBody.ingredientSummary[1].brand)
    }

    @Test
    fun `should map IngredientDepletionResponseDto to IngredientDepletionResponse correctly`() {
        // Given
        val brand = "hellofresh"
        val dto = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                ),
                IngredientSummaryDto(
                    site = "US01",
                    skuCode = "SKU002",
                    skuName = "Test SKU 2",
                    category = "Test Category 2",
                    uom = SkuUOM.UOM_KG
                )
            )
        )

        // When
        val result = mapToIngredientDepletionResponse(dto, brand)

        // Then
        assertEquals(2, result.ingredientSummary.size)

        val firstSummary = result.ingredientSummary[0]
        assertEquals("GB01", firstSummary.site)
        assertEquals("SKU001", firstSummary.skuCode)
        assertEquals("Test SKU", firstSummary.skuName)
        assertEquals("Test Category", firstSummary.category)
        assertEquals("UOM_UNIT", firstSummary.uom)
        assertEquals(brand, firstSummary.brand)

        val secondSummary = result.ingredientSummary[1]
        assertEquals("US01", secondSummary.site)
        assertEquals("SKU002", secondSummary.skuCode)
        assertEquals("Test SKU 2", secondSummary.skuName)
        assertEquals("Test Category 2", secondSummary.category)
        assertEquals("UOM_KG", secondSummary.uom)
        assertEquals(brand, secondSummary.brand)
    }

    private fun Application.configureTestApplication() {
        // Configure authentication for testing
        install(Authentication) {
            jwt {
                validate { credential ->
                    if (credential.payload.getClaim("sub").asString() != null) {
                        JWTPrincipal(credential.payload)
                    } else {
                        null
                    }
                }
            }
        }

        // Configure serialization
        install(ContentNegotiation) {
            json()
        }
    }
}
