package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuUOM
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionServiceTest {

    private val ingredientDepletionRepository = mockk<IngredientDepletionRepository>()
    private val dcConfigService = mockk<DcConfigService>()

    private lateinit var service: IngredientDepletionService

    private val dcConfigGB = DistributionCenterConfiguration.default("GB").copy(
        dcCode = "GB01",
        market = "GB",
        brands = listOf("hellofresh", "greenchef")
    )

    private val dcConfigDE = DistributionCenterConfiguration.default("DE").copy(
        dcCode = "DE01",
        market = "DE",
        brands = listOf("hellofresh")
    )

    private val dcConfigUS = DistributionCenterConfiguration.default("US").copy(
        dcCode = "US01",
        market = "US",
        brands = listOf("hellofresh", "greenchef", "everyplate")
    )

    @BeforeEach
    fun setUp() {
        service = IngredientDepletionService(ingredientDepletionRepository, dcConfigService)

        coEvery { dcConfigService.dcConfigurations } returns mapOf(
            "GB01" to dcConfigGB,
            "DE01" to dcConfigDE,
            "US01" to dcConfigUS
        )
    }

    @Test
    fun `should use provided dc codes when specified`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val providedDcCodes = listOf("GB01", "DE01")
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) }
    }

    @Test
    fun `should filter dc codes by brand when dc codes not provided`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val expectedDcCodes = listOf("GB01", "DE01", "US01") // All DCs that support hellofresh
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, null)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) }
    }

    @Test
    fun `should filter dc codes by greenchef brand when dc codes not provided`() = runBlocking {
        // Given
        val brand = "greenchef"
        val expectedDcCodes = listOf("GB01", "US01") // Only DCs that support greenchef
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Green Chef SKU",
                    category = "Green Category",
                    uom = SkuUOM.UOM_KG
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, null)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) }
    }

    @Test
    fun `should filter dc codes by everyplate brand when dc codes not provided`() = runBlocking {
        // Given
        val brand = "everyplate"
        val expectedDcCodes = listOf("US01") // Only US DC supports everyplate
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "US01",
                    skuCode = "SKU001",
                    skuName = "EveryPlate SKU",
                    category = "EveryPlate Category",
                    uom = SkuUOM.UOM_LBS
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, null)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) }
    }

    @Test
    fun `should return empty result when brand not supported by any dc`() = runBlocking {
        // Given
        val brand = "unsupported-brand"
        val expectedDcCodes = emptyList<String>() // No DCs support this brand
        val expectedResponse = IngredientDepletionResponseDto(ingredientSummary = emptyList())

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, null)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(expectedDcCodes) }
    }

    @Test
    fun `should handle empty dc codes list when provided`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val providedDcCodes = emptyList<String>()
        val expectedResponse = IngredientDepletionResponseDto(ingredientSummary = emptyList())

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) }
    }

    @Test
    fun `should handle repository exception`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val providedDcCodes = listOf("GB01")
        val exception = RuntimeException("Repository error")

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) } throws exception

        // When & Then
        try {
            service.getIngredientDepletionData(brand, providedDcCodes)
            assert(false) { "Expected exception to be thrown" }
        } catch (e: RuntimeException) {
            assertEquals("Repository error", e.message)
        }

        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) }
    }

    @Test
    fun `should use provided dc codes even if they don't support the brand`() = runBlocking {
        // Given - providing DC codes that don't support the brand
        val brand = "everyplate" // Only supported by US01
        val providedDcCodes = listOf("GB01", "DE01") // These don't support everyplate
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes)

        // Then - should still use the provided DC codes, not filter by brand
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) }
    }

    @Test
    fun `should handle multiple ingredient summaries`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val providedDcCodes = listOf("GB01", "US01")
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU 1",
                    category = "Category 1",
                    uom = SkuUOM.UOM_UNIT
                ),
                IngredientSummaryDto(
                    site = "US01",
                    skuCode = "SKU002",
                    skuName = "Test SKU 2",
                    category = "Category 2",
                    uom = SkuUOM.UOM_KG
                ),
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU003",
                    skuName = "Test SKU 3",
                    category = "Category 3",
                    uom = SkuUOM.UOM_LITRE
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes)

        // Then
        assertEquals(expectedResponse, result)
        assertEquals(3, result.ingredientSummary.size)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes) }
    }
}
