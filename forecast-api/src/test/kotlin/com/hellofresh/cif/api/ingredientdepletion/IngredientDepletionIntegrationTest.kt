package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.auth.authenticate
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.jwt.jwt
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import io.ktor.serialization.kotlinx.json.json
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import java.util.concurrent.CompletableFuture
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.Duration.Companion.seconds
import kotlinx.serialization.json.Json
import org.jooq.Record
import org.jooq.Result
import org.jooq.SelectJoinStep
import org.jooq.SelectOnConditionStep
import org.jooq.SelectWhereStep
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionIntegrationTest {

    private val metricsDSLContext = mockk<MetricsDSLContext>()
    private val dcConfigService = mockk<DcConfigService>()
    private val selectJoinStep = mockk<SelectJoinStep<Record>>()
    private val selectOnConditionStep = mockk<SelectOnConditionStep<Record>>()
    private val selectWhereStep = mockk<SelectWhereStep<Record>>()
    private val result = mockk<Result<Record>>()
    private val record1 = mockk<Record>()
    private val record2 = mockk<Record>()

    private lateinit var repository: IngredientDepletionRepository
    private lateinit var service: IngredientDepletionService

    private val dcConfigGB = DistributionCenterConfiguration.default("GB").copy(
        dcCode = "GB01",
        market = "GB",
        brands = listOf("hellofresh", "greenchef")
    )

    private val dcConfigDE = DistributionCenterConfiguration.default("DE").copy(
        dcCode = "DE01",
        market = "DE",
        brands = listOf("hellofresh")
    )

    @BeforeEach
    fun setUp() {
        repository = IngredientDepletionRepositoryImpl(metricsDSLContext)
        service = IngredientDepletionService(repository, dcConfigService)

        coEvery { dcConfigService.dcConfigurations } returns mapOf(
            "GB01" to dcConfigGB,
            "DE01" to dcConfigDE
        )
    }

    @Test
    fun `should handle full integration flow from API to database`() = testApplication {
        // Given
        val brand = "hellofresh"
        val dcCodes = "GB01,DE01"

        // Mock database query chain
        every { metricsDSLContext.withTagName("fetch-ingredient-summary") } returns metricsDSLContext
        every { metricsDSLContext.select(any(), any(), any(), any(), any()) } returns selectJoinStep
        every { selectJoinStep.from(any()) } returns selectJoinStep
        every { selectJoinStep.join(any()).on(any()) } returns selectOnConditionStep
        every { selectOnConditionStep.where(any()) } returns selectWhereStep
        every { selectWhereStep.fetchAsync() } returns CompletableFuture.completedFuture(result)

        // Mock database records
        every { record1.get(CALCULATION.DC_CODE, String::class.java) } returns "GB01"
        every { record1.get(SKU_SPECIFICATION_VIEW.CODE, String::class.java) } returns "SKU001"
        every { record1.get(SKU_SPECIFICATION_VIEW.NAME, String::class.java) } returns "Organic Tomatoes"
        every { record1.get(SKU_SPECIFICATION_VIEW.CATEGORY) } returns "Vegetables"
        every { record1.get(SKU_SPECIFICATION_VIEW.UOM) } returns Uom.UOM_KG

        every { record2.get(CALCULATION.DC_CODE, String::class.java) } returns "DE01"
        every { record2.get(SKU_SPECIFICATION_VIEW.CODE, String::class.java) } returns "SKU002"
        every { record2.get(SKU_SPECIFICATION_VIEW.NAME, String::class.java) } returns "Fresh Basil"
        every { record2.get(SKU_SPECIFICATION_VIEW.CATEGORY) } returns "Herbs"
        every { record2.get(SKU_SPECIFICATION_VIEW.UOM) } returns Uom.UOM_UNIT

        every { result.map(any<(Record) -> IngredientSummaryDto>()) } returns listOf(record1, record2).map { record ->
            IngredientSummaryDto(
                site = record.get(CALCULATION.DC_CODE, String::class.java),
                skuCode = record.get(SKU_SPECIFICATION_VIEW.CODE, String::class.java),
                skuName = record.get(SKU_SPECIFICATION_VIEW.NAME, String::class.java),
                category = record.get(SKU_SPECIFICATION_VIEW.CATEGORY),
                uom = IngredientDepletionRepositoryImpl.mapUomToSkuUOM(record.get(SKU_SPECIFICATION_VIEW.UOM))
            )
        }

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(service, 30.seconds.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(2, responseBody.ingredientSummary.size)

        // Verify first ingredient
        val firstIngredient = responseBody.ingredientSummary[0]
        assertEquals("GB01", firstIngredient.site)
        assertEquals("SKU001", firstIngredient.skuCode)
        assertEquals("Organic Tomatoes", firstIngredient.skuName)
        assertEquals("Vegetables", firstIngredient.category)
        assertEquals("UOM_KG", firstIngredient.uom)
        assertEquals(brand, firstIngredient.brand)

        // Verify second ingredient
        val secondIngredient = responseBody.ingredientSummary[1]
        assertEquals("DE01", secondIngredient.site)
        assertEquals("SKU002", secondIngredient.skuCode)
        assertEquals("Fresh Basil", secondIngredient.skuName)
        assertEquals("Herbs", secondIngredient.category)
        assertEquals("UOM_UNIT", secondIngredient.uom)
        assertEquals(brand, secondIngredient.brand)
    }

    @Test
    fun `should handle integration flow with brand filtering when no dc codes provided`() = testApplication {
        // Given
        val brand = "hellofresh"
        // Expected to filter to both GB01 and DE01 since both support hellofresh

        // Mock database query chain
        every { metricsDSLContext.withTagName("fetch-ingredient-summary") } returns metricsDSLContext
        every { metricsDSLContext.select(any(), any(), any(), any(), any()) } returns selectJoinStep
        every { selectJoinStep.from(any()) } returns selectJoinStep
        every { selectJoinStep.join(any()).on(any()) } returns selectOnConditionStep
        every { selectOnConditionStep.where(any()) } returns selectWhereStep
        every { selectWhereStep.fetchAsync() } returns CompletableFuture.completedFuture(result)

        // Mock single database record
        every { record1.get(CALCULATION.DC_CODE, String::class.java) } returns "GB01"
        every { record1.get(SKU_SPECIFICATION_VIEW.CODE, String::class.java) } returns "SKU003"
        every { record1.get(SKU_SPECIFICATION_VIEW.NAME, String::class.java) } returns "Premium Chicken"
        every { record1.get(SKU_SPECIFICATION_VIEW.CATEGORY) } returns "Protein"
        every { record1.get(SKU_SPECIFICATION_VIEW.UOM) } returns Uom.UOM_KG

        every { result.map(any<(Record) -> IngredientSummaryDto>()) } returns listOf(
            IngredientSummaryDto(
                site = "GB01",
                skuCode = "SKU003",
                skuName = "Premium Chicken",
                category = "Protein",
                uom = IngredientDepletionRepositoryImpl.mapUomToSkuUOM(Uom.UOM_KG)
            )
        )

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(service, 30.seconds.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(1, responseBody.ingredientSummary.size)

        val ingredient = responseBody.ingredientSummary[0]
        assertEquals("GB01", ingredient.site)
        assertEquals("SKU003", ingredient.skuCode)
        assertEquals("Premium Chicken", ingredient.skuName)
        assertEquals("Protein", ingredient.category)
        assertEquals("UOM_KG", ingredient.uom)
        assertEquals(brand, ingredient.brand)
    }

    @Test
    fun `should handle integration flow with empty results`() = testApplication {
        // Given
        val brand = "hellofresh"
        val dcCodes = "GB01"

        // Mock database query chain returning empty results
        every { metricsDSLContext.withTagName("fetch-ingredient-summary") } returns metricsDSLContext
        every { metricsDSLContext.select(any(), any(), any(), any(), any()) } returns selectJoinStep
        every { selectJoinStep.from(any()) } returns selectJoinStep
        every { selectJoinStep.join(any()).on(any()) } returns selectOnConditionStep
        every { selectOnConditionStep.where(any()) } returns selectWhereStep
        every { selectWhereStep.fetchAsync() } returns CompletableFuture.completedFuture(result)
        every { result.map(any<(Record) -> IngredientSummaryDto>()) } returns emptyList()

        application {
            configureTestApplication()
            routing {
                ingredientDepletion(service, 30.seconds.inWholeMilliseconds)
            }
        }

        // When
        val response = client.get("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes") {
            header(HttpHeaders.Authorization, "Bearer valid-token")
        }

        // Then
        assertEquals(HttpStatusCode.OK, response.status)

        val responseBody = Json.decodeFromString<IngredientDepletionResponse>(response.bodyAsText())
        assertEquals(0, responseBody.ingredientSummary.size)
    }

    private fun Application.configureTestApplication() {
        // Configure authentication for testing
        install(Authentication) {
            jwt {
                validate { credential ->
                    if (credential.payload.getClaim("sub").asString() != null) {
                        JWTPrincipal(credential.payload)
                    } else {
                        null
                    }
                }
            }
        }

        // Configure serialization
        install(ContentNegotiation) {
            json()
        }
    }
}
