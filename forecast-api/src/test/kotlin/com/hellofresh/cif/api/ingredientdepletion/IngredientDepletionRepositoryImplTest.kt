package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import io.mockk.mockk
import kotlin.test.Test
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class IngredientDepletionRepositoryImplTest {

    private val metricsDSLContext = mockk<MetricsDSLContext>()
    private lateinit var repository: IngredientDepletionRepositoryImpl

    @BeforeEach
    fun setUp() {
        repository = IngredientDepletionRepositoryImpl(metricsDSLContext)
    }

    // Focus on testing the UOM mapping function which is the core business logic
    @ParameterizedTest
    @EnumSource(Uom::class)
    fun `should map all UOM types correctly`(uom: Uom) {
        // When
        val result = IngredientDepletionRepositoryImpl.mapUomToSkuUOM(uom)

        // Then
        val expected = when (uom) {
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }
        assertEquals(expected, result)
    }

    @ParameterizedTest
    @EnumSource(Uom::class)
    fun `should map all UOM types correctly`(uom: Uom) {
        // When
        val result = IngredientDepletionRepositoryImpl.mapUomToSkuUOM(uom)

        // Then
        val expected = when (uom) {
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }
        assertEquals(expected, result)
    }

    @Test
    fun `should use correct metrics tag name`() = runBlocking {
        // Given
        val dcCodes = listOf("DC1")

        every { metricsDSLContext.withTagName("fetch-ingredient-summary") } returns metricsDSLContext
        every { metricsDSLContext.select(any(), any(), any(), any(), any()) } returns selectJoinStep
        every { selectJoinStep.from(any()) } returns selectJoinStep
        every { selectJoinStep.join(any()).on(any()) } returns selectOnConditionStep
        every { selectOnConditionStep.where(any()) } returns selectWhereStep
        every { selectWhereStep.fetchAsync() } returns CompletableFuture.completedFuture(result)
        every { result.map(any<(Record) -> IngredientSummaryDto>()) } returns emptyList()

        // When
        repository.getIngredientDepletionData(dcCodes)

        // Then
        verify { metricsDSLContext.withTagName("fetch-ingredient-summary") }
    }

    @Test
    fun `should join calculation table with sku_specification_view correctly`() = runBlocking {
        // Given
        val dcCodes = listOf("DC1")

        every { metricsDSLContext.withTagName("fetch-ingredient-summary") } returns metricsDSLContext
        every { metricsDSLContext.select(any(), any(), any(), any(), any()) } returns selectJoinStep
        every { selectJoinStep.from(any()) } returns selectJoinStep
        every { selectJoinStep.join(any()).on(any()) } returns selectOnConditionStep
        every { selectOnConditionStep.where(any()) } returns selectWhereStep
        every { selectWhereStep.fetchAsync() } returns CompletableFuture.completedFuture(result)
        every { result.map(any<(Record) -> IngredientSummaryDto>()) } returns emptyList()

        // When
        repository.getIngredientDepletionData(dcCodes)

        // Then
        verify { selectJoinStep.join(any()).on(any()) }
        verify { selectOnConditionStep.where(any()) }
    }
}
