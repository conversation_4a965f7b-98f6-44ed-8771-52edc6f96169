package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.models.SkuUOM
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class IngredientDepletionRepositoryImplTest {

    @ParameterizedTest
    @EnumSource(Uom::class)
    fun `should map all UOM types correctly`(uom: Uom) {
        // When
        val result = IngredientDepletionRepositoryImpl.mapUomToSkuUOM(uom)

        // Then
        val expected = when (uom) {
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }
        assertEquals(expected, result)
    }
}
