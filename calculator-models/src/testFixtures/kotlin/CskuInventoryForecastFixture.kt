import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.util.UUID
import kotlin.random.Random

private const val DEFAULT_TEST_STRATEGY = "ALGORITHM_FORECASTVARIANCE"
fun CskuInventoryForecastVal.Companion.random(
    productionWeek: String = "2024-W50",
    random: Random = Random(System.currentTimeMillis())
) =
    CskuInventoryForecastVal(
        productionWeek = productionWeek,
        expired = random.nextDouble(1000.0).toBigDecimal(),
        openingStock = random.nextDouble(1000.0).toBigDecimal(),
        demanded = random.nextDouble(1000.0).toBigDecimal(),
        present = random.nextDouble(1000.0).toBigDecimal(),
        closingStock = random.nextDouble(1000.0).toBigDecimal(),
        actualInbound = random.nextDouble(1000.0).toBigDecimal(),
        expectedInbound = random.nextDouble(1000.0).toBigDecimal(),
        dailyNeeds = random.nextDouble(1000.0).toBigDecimal(),
        actualConsumption = random.nextDouble(1000.0).toBigDecimal(),
        actualInboundPurchaseOrders = setOf(UUID.randomUUID().toString()),
        expectedInboundPurchaseOrders = setOf(UUID.randomUUID().toString()),
        productionWeekStartStock = random.nextDouble(1000.0).toBigDecimal(),
        safetyStock = random.nextDouble(1000.0).toBigDecimal(),
        strategy = DEFAULT_TEST_STRATEGY,
        safetyStockNeeds = random.nextDouble(1000.0).toBigDecimal(),
        storageStock = random.nextDouble(1000.0).toBigDecimal(),
        stagingStock = random.nextDouble(1000.0).toBigDecimal(),
        stockUpdate = if (random.nextBoolean()) random.nextDouble(1000.0).toBigDecimal() else null,
        purchaseOrderDueInForSuppliers = null,
        maxPurchaseOrderDueIn = null,
        netNeeds = random.nextDouble(1000.0).toBigDecimal(),
        unusableInventory = null,
        uom = SkuUOM.entries.random(),
        expectedInboundTransferOrders = setOf(UUID.randomUUID().toString(), UUID.randomUUID().toString()),
        expectedInboundTransferOrdersQuantity = random.nextDouble(100.0).toBigDecimal(),
        actualInboundTransferOrders = setOf(UUID.randomUUID().toString(), UUID.randomUUID().toString()),
        actualInboundTransferOrdersQuantity = random.nextDouble(100.0).toBigDecimal(),
        expectedOutboundTransferOrders = setOf(UUID.randomUUID().toString(), UUID.randomUUID().toString()),
        expectedOutboundTransferOrdersQuantity = random.nextDouble(100.0).toBigDecimal(),
    )

fun CskuInventoryForecastVal.Companion.default() =
    CskuInventoryForecastVal(
        BigDecimal.ZERO,
        BigDecimal.ONE,
        BigDecimal.TWO,
        BigDecimal(3),
        emptySet(),
        BigDecimal(4),
        emptySet(),
        BigDecimal(5),
        BigDecimal(6),
        BigDecimal(7),
        BigDecimal(8),
        "2024-W50",
        BigDecimal(9),
        BigDecimal(10),
        strategy = DEFAULT_TEST_STRATEGY,
        BigDecimal(11),
        BigDecimal(12),
        BigDecimal(13),
        netNeeds = BigDecimal(14),
        stockUpdate = BigDecimal(0),
        uom = SkuUOM.UOM_KG,
    )
