package com.hellofresh.cif.safetystock

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository.Companion.objectMapper
import com.hellofresh.cif.safetystocklib.schema.Tables.SAFETY_STOCKS
import com.hellofresh.cif.safetystocklib.schema.Tables.SAFETY_STOCK_CONF
import com.hellofresh.cif.safetystocklib.schema.tables.records.SafetyStockConfRecord
import com.hellofresh.cif.safetystocklib.schema.tables.records.SafetyStocksRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

const val SAFETY_STOCK_STRATEGY = "ALGORITHM_FORECASTVARIANCE"

open class FunctionalTest {

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SAFETY_STOCK_CONF).execute()
        dsl.deleteFrom(SAFETY_STOCKS).execute()
    }

    fun insertSafetyStock(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        date: LocalDate,
        skuId: UUID,
        safetyStock: Long,
        riskMultiplier: BigDecimal,
        skuRiskRating: SkuRiskRating
    ) = SafetyStocksRecord().apply {
        this.dcCode = distributionCenterConfiguration.dcCode
        this.skuId = skuId
        this.week = DcWeek(date, distributionCenterConfiguration.productionStart).toString()
        this.safetyStock = safetyStock
        this.strategy = SAFETY_STOCK_STRATEGY
        this.configuration = JSONB.valueOf(
            objectMapper.writeValueAsString(
                Configuration(riskMultiplier, skuRiskRating, BigDecimal.ZERO),
            ),
        )
    }.also {
        dsl.batchInsert(it).execute()
    }

    fun insertSafetyStockConfiguration(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        date: LocalDate,
        skuId: UUID,
        riskMultiplier: BigDecimal,
        skuRiskRating: SkuRiskRating
    ) = insertSafetyStockConfiguration(
        distributionCenterConfiguration.dcCode,
        distributionCenterConfiguration.productionStart,
        date,
        skuId,
        riskMultiplier,
        skuRiskRating,
    )

    fun insertSafetyStockConfiguration(
        dcCode: String,
        productionStart: DayOfWeek,
        date: LocalDate,
        skuId: UUID,
        riskMultiplier: BigDecimal,
        skuRiskRating: SkuRiskRating
    ) =
        SafetyStockConfRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.week = DcWeek(date, productionStart).toString()
            this.riskMultiplier = riskMultiplier
            this.skuRiskRating = com.hellofresh.cif.safetystocklib.schema.enums.SkuRiskRating.valueOf(skuRiskRating.name)
        }.also {
            dsl.batchInsert(it).execute()
        }

    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var safetyStockConfigRepository: SafetyStockConfigurationRepository
        lateinit var safetyStockRepository: SafetyStockRepository

        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)
        val meterRegistry = SimpleMeterRegistry()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newFixedThreadPool(2))
                }
            dsl = DSL.using(dbConfiguration).withMetrics(meterRegistry)

            safetyStockConfigRepository = SafetyStockConfigurationRepository(dsl)
            safetyStockRepository = SafetyStockRepository(dsl)
        }
    }
}
