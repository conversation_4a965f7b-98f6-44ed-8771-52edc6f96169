package com.hellofresh.cif.safetystock.repository

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.FunctionalTest
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystock.model.SkuRiskRating.CRITICAL
import com.hellofresh.cif.safetystock.model.SkuRiskRating.MEDIUM
import com.hellofresh.cif.safetystock.model.SkuRiskRating.MEDIUM_LOW
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository.Companion.objectMapper
import com.hellofresh.cif.safetystocklib.schema.tables.records.SafetyStocksRecord
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class SafetyStockRepositoryTest : FunctionalTest() {
    @Test
    fun `safety stocks are fetched greater than min week and the given dc codes`() {
        val distributionCenterConfiguration1 = createDc(TUESDAY, "VE")
        val distributionCenterConfiguration2 = createDc(WEDNESDAY, "DC")

        val today = LocalDate.now(UTC)
        val skuId1 = UUID.randomUUID()
        insertSafetyStock(
            distributionCenterConfiguration1,
            today,
            skuId1,
            Random.nextLong(),
            BigDecimal.TEN,
            SkuRiskRating.entries.random(),
        )
        val sku1SafetyStock2 =
            insertSafetyStock(
                distributionCenterConfiguration1,
                today.plusWeeks(1),
                skuId1,
                Random.nextLong(),
                BigDecimal.valueOf(Random.nextDouble()),
                SkuRiskRating.entries.random(),
            )

        val skuId2 = UUID.randomUUID()
        insertSafetyStock(
            distributionCenterConfiguration2,
            distributionCenterConfiguration2.getLatestProductionStart().minusWeeks(1),
            skuId2,
            Random.nextLong(),
            BigDecimal.valueOf(Random.nextDouble()),
            SkuRiskRating.entries.random(),
        )

        val safetyStocks =
            runBlocking {
                safetyStockRepository.fetchSafetyStocks(
                    DcWeek(today.plusWeeks(1), distributionCenterConfiguration1.productionStart).toString(),
                    setOf(
                        "VE",
                        "DC",
                    ),
                )
            }

        assertEquals(1, safetyStocks.size)
        assertEquals(
            sku1SafetyStock2.safetyStock,
            safetyStocks.first {
                it.toKey() ==
                    SafetyStockKey(
                        sku1SafetyStock2.dcCode,
                        DcWeek(sku1SafetyStock2.week),
                        sku1SafetyStock2.skuId,
                    )
            }.value,
        )
    }

    @Test
    fun `should return only safety stocks for the given dc code`() {
        val distributionCenterConfiguration1 = createDc(TUESDAY, "VE")
        val distributionCenterConfiguration2 = createDc(WEDNESDAY, "DC")

        val today = LocalDate.now(UTC)
        val skuId1 = UUID.randomUUID()
        val sku1SafetyStock1 =
            insertSafetyStock(
                distributionCenterConfiguration1,
                today,
                skuId1,
                Random.nextLong(),
                BigDecimal.TEN,
                CRITICAL,
            )

        val skuId2 = UUID.randomUUID()
        insertSafetyStock(
            distributionCenterConfiguration2,
            distributionCenterConfiguration2.getLatestProductionStart().minusWeeks(1),
            skuId2,
            Random.nextLong(),
            BigDecimal.valueOf(Random.nextDouble()),
            SkuRiskRating.entries.random(),
        )

        val safetyStocks =
            runBlocking {
                safetyStockRepository.fetchSafetyStocks(
                    DcWeek(today, distributionCenterConfiguration1.productionStart).toString(),
                    setOf(
                        "VE",
                    ),
                )
            }

        assertEquals(1, safetyStocks.size)
        assertEquals(
            sku1SafetyStock1.safetyStock,
            safetyStocks.first {
                it.toKey() ==
                    SafetyStockKey(
                        sku1SafetyStock1.dcCode,
                        DcWeek(sku1SafetyStock1.week),
                        sku1SafetyStock1.skuId,
                    )
            }.value,
        )
    }

    @Test
    fun `safety stock are fetched from latest production date`() {
        val distributionCenterConfiguration1 = createDc(TUESDAY, "VE")
        val distributionCenterConfiguration2 = createDc(WEDNESDAY, "DC")

        val today = LocalDate.now(UTC)
        val skuId1 = UUID.randomUUID()
        val sku1SafetyStock1 =
            insertSafetyStock(
                distributionCenterConfiguration1,
                today,
                skuId1,
                Random.nextLong(),
                BigDecimal.TEN,
                MEDIUM,
            )
        val sku1SafetyStock2 =
            insertSafetyStock(
                distributionCenterConfiguration1,
                today.plusWeeks(1),
                skuId1,
                Random.nextLong(),
                BigDecimal.valueOf(Random.nextDouble()),
                SkuRiskRating.entries.random(),
            )

        val skuId2 = UUID.randomUUID()
        val sku2SafetyStock1 =
            insertSafetyStock(
                distributionCenterConfiguration2,
                distributionCenterConfiguration2.getLatestProductionStart(),
                skuId2,
                Random.nextLong(),
                BigDecimal.valueOf(Random.nextDouble()),
                SkuRiskRating.entries.random(),
            )
        insertSafetyStock(
            distributionCenterConfiguration2,
            distributionCenterConfiguration2.getLatestProductionStart().minusWeeks(1),
            skuId2,
            Random.nextLong(),
            BigDecimal.valueOf(Random.nextDouble()),
            SkuRiskRating.entries.random(),
        )

        val safetyStocks =
            runBlocking {
                safetyStockRepository.fetchSafetyStocksFromLatestProductionWeek(
                    setOf(
                        distributionCenterConfiguration1,
                        distributionCenterConfiguration2,
                    ),
                )
            }

        assertEquals(3, safetyStocks.size)
        assertSafetyStock(sku1SafetyStock1, safetyStocks)
        assertSafetyStock(sku1SafetyStock2, safetyStocks)
        assertSafetyStock(sku2SafetyStock1, safetyStocks)
    }

    @Test
    fun `safety stock are fetched gor given weekd and dc`() {
        val distributionCenterConfiguration1 = createDc(TUESDAY, "VE")
        val distributionCenterConfiguration2 = createDc(WEDNESDAY, "DC")

        val today = LocalDate.now(UTC)
        val skuId1 = UUID.randomUUID()
        val sku1SafetyStock1 =
            insertSafetyStock(
                distributionCenterConfiguration1,
                today,
                skuId1,
                Random.nextLong(),
                BigDecimal.TEN,
                MEDIUM_LOW,
            )

        insertSafetyStock(
            distributionCenterConfiguration2,
            today,
            skuId1,
            Random.nextLong(),
            BigDecimal.valueOf(Random.nextDouble()),
            SkuRiskRating.entries.random(),
        )

        val skuId2 = UUID.randomUUID()
        val sku2SafetyStock1 =
            insertSafetyStock(
                distributionCenterConfiguration1,
                distributionCenterConfiguration1.getLatestProductionStart(),
                skuId2,
                Random.nextLong(),
                BigDecimal.valueOf(Random.nextDouble()),
                SkuRiskRating.entries.random(),
            )

        insertSafetyStock(
            distributionCenterConfiguration1,
            distributionCenterConfiguration1.getLatestProductionStart().plusWeeks(1),
            skuId2,
            Random.nextLong(),
            BigDecimal.valueOf(Random.nextDouble()),
            SkuRiskRating.entries.random(),
        )

        val safetyStocks =
            runBlocking {
                safetyStockRepository.fetchSafetyStock(
                    DcWeek(today, distributionCenterConfiguration1.productionStart).toString(),
                    distributionCenterConfiguration1.dcCode,
                )
            }

        assertEquals(2, safetyStocks.size)
        assertSafetyStock(sku1SafetyStock1, safetyStocks)
        assertSafetyStock(sku2SafetyStock1, safetyStocks)
    }

    private fun createDc(
        dayOfWeek: DayOfWeek,
        dcCode: String = "VE"
    ): DistributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()
        .copy(
            productionStart = dayOfWeek,
            zoneId = UTC,
            dcCode = dcCode,
        )

    private fun assertSafetyStock(expectedSafetyStock: SafetyStocksRecord, safetyStocks: List<SafetyStock>) {
        with(
            safetyStocks.first {
                it.skuId == expectedSafetyStock.skuId &&
                    it.dcCode == expectedSafetyStock.dcCode &&
                    it.week == expectedSafetyStock.week
            },
        ) {
            assertEquals(expectedSafetyStock.safetyStock, value)
            assertEquals(
                objectMapper.readValue<Configuration>(
                    expectedSafetyStock.configuration.data(),
                ),
                configuration,
            )
        }
    }
}
