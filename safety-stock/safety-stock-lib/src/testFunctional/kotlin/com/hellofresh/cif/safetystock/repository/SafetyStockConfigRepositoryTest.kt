package com.hellofresh.cif.safetystock.repository

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.safetystock.FunctionalTest
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystock.model.SkuRiskRating.LOW
import java.math.BigDecimal
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class SafetyStockConfigRepositoryTest : FunctionalTest() {

    @Test
    fun `safety stock configurations are fetched from latest production date`() {
        val distributionCenterConfiguration1 = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = TUESDAY,
                zoneId = UTC,
            )
        val distributionCenterConfiguration2 = DistributionCenterConfiguration.Companion.default().copy(
            dcCode = "DC",
            productionStart = WEDNESDAY,
            zoneId = UTC,
        )

        val today = LocalDate.now(UTC)
        val skuId1 = UUID.randomUUID()
        val sku1SafetyStock1 =
            insertSafetyStockConfiguration(distributionCenterConfiguration1, today, skuId1, BigDecimal.ONE, LOW)
        val sku1SafetyStock2 =
            insertSafetyStockConfiguration(
                distributionCenterConfiguration1,
                today.plusWeeks(1),
                skuId1,
                BigDecimal.valueOf(Random.nextDouble()),
                SkuRiskRating.entries.random(),
            )

        val skuId2 = UUID.randomUUID()
        val sku2SafetyStock1 =
            insertSafetyStockConfiguration(
                distributionCenterConfiguration2,
                distributionCenterConfiguration2.getLatestProductionStart(),
                skuId2,
                BigDecimal.valueOf(Random.nextDouble()),
                SkuRiskRating.entries.random(),
            )

        insertSafetyStockConfiguration(
            DistributionCenterConfiguration.Companion.default("XX"),
            today,
            skuId1,
            BigDecimal.ONE,
            SkuRiskRating.entries.random(),
        )

        val safetyStockConfigurations =
            runBlocking {
                safetyStockConfigRepository.fetchSafetyStockConfigurations(
                    setOf(
                        distributionCenterConfiguration1,
                        distributionCenterConfiguration2,
                    ),
                )
            }

        with(
            safetyStockConfigurations.getConfiguration(
                sku1SafetyStock1.dcCode,
                sku1SafetyStock1.skuId,
                ProductionWeek(
                    sku1SafetyStock1.week,
                    distributionCenterConfiguration1.productionStart,
                    distributionCenterConfiguration1.zoneId,
                ),
            ),
        ) {
            assertEquals(
                sku1SafetyStock1.riskMultiplier,
                riskMultiplier,
            )
            assertEquals(
                sku1SafetyStock1.skuRiskRating.name,
                skuRiskRating.name,
            )
        }

        with(
            safetyStockConfigurations.getConfiguration(
                sku1SafetyStock2.dcCode,
                sku1SafetyStock2.skuId,
                ProductionWeek(
                    sku1SafetyStock2.week,
                    distributionCenterConfiguration1.productionStart,
                    distributionCenterConfiguration1.zoneId,
                ),
            ),
        ) {
            assertEquals(
                sku1SafetyStock2.riskMultiplier,
                riskMultiplier,
            )
            assertEquals(
                sku1SafetyStock2.skuRiskRating.name,
                skuRiskRating.name,
            )
        }

        with(
            safetyStockConfigurations.getConfiguration(
                sku2SafetyStock1.dcCode,
                sku2SafetyStock1.skuId,
                ProductionWeek(
                    sku2SafetyStock1.week,
                    distributionCenterConfiguration2.productionStart,
                    distributionCenterConfiguration2.zoneId,
                ),
            ),
        ) {
            assertEquals(
                sku2SafetyStock1.riskMultiplier,
                riskMultiplier,
            )
            assertEquals(
                sku2SafetyStock1.skuRiskRating.name,
                skuRiskRating.name,
            )
        }
    }

    @Test
    fun `future safety stock configurations are fetched when there is no past weeks configurations`() {
        val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = TUESDAY,
                zoneId = UTC,
            )

        val today = LocalDate.now(UTC)
        val skuId = UUID.randomUUID()
        val sku1SafetyStock =
            insertSafetyStockConfiguration(
                distributionCenterConfiguration,
                today.plusWeeks(3),
                skuId,
                BigDecimal(32343),
                SkuRiskRating.entries.random(),
            )

        val safetyStockConfigurations =
            runBlocking {
                safetyStockConfigRepository.fetchSafetyStockConfigurations(
                    setOf(distributionCenterConfiguration),
                )
            }

        with(
            safetyStockConfigurations.getConfiguration(
                sku1SafetyStock.dcCode,
                sku1SafetyStock.skuId,
                ProductionWeek(
                    sku1SafetyStock.week,
                    distributionCenterConfiguration.productionStart,
                    distributionCenterConfiguration.zoneId,
                ),
            ),
        ) {
            assertEquals(
                sku1SafetyStock.riskMultiplier,
                riskMultiplier,
            )
            assertEquals(
                sku1SafetyStock.skuRiskRating.name,
                skuRiskRating.name,
            )
        }
    }
}
