plugins {
    id("com.hellofresh.cif.application-conventions")
    hellofresh.`test-integration`
    alias(libs.plugins.jooq)
    alias(libs.plugins.gradle.docker.compose)
}

group = "$group.calculator"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "calculation|pre_production_calculation|live_inventory_calculation|live_inventory_pre_production_calculation" +
                            "|dc_config|dc_config_weight_view|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = false
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(projects.demandModels)
    api(projects.demandLib)
    api(projects.inventory.inventoryLib)
    api(projects.distributionCenterModels)
    api(projects.calculatorModels)
    api(projects.skuModels)
    api(projects.lib.db)
    api(projects.purchaseOrder.purchaseOrderLib)
    api(libs.jackson.jsr310)
    implementation(projects.calculatorRule)
    implementation(projects.skuInputsLib)
    implementation(projects.lib)
    implementation(libs.ktor.core)
    implementation(libs.postgresql.driver)
    implementation(libs.hikaricp)
    implementation(libs.caffeine.core)
    implementation(projects.safetyStock.safetyStockLib)

    testImplementation(testFixtures(projects.calculatorModels))
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.lib.featureflags))
    testImplementation(testFixtures(projects.inventory.inventoryLib))
    testImplementation(libs.restassured.core)
    testImplementation(libs.mockk)

    testIntegrationRuntimeOnly(projects.inventoryDb)
    testIntegrationCompileOnly(projects.lib.logging)
    testIntegrationImplementation(projects.skuModels)
    testIntegrationImplementation(projects.demandModels)
    testIntegrationImplementation(projects.distributionCenterModels)
    testIntegrationImplementation(projects.calculatorRule)
    testIntegrationImplementation(projects.calculatorModels)
    testIntegrationImplementation(projects.libTests)
    testIntegrationImplementation(libs.coroutines.core)
    testIntegrationImplementation(libs.flyway.core)
    testIntegrationImplementation(libs.micrometer.core)
    testIntegrationImplementation(libs.hikaricp)
    testIntegrationImplementation(libs.jooq.core)
    testIntegrationImplementation(libs.postgresql.driver)
    testIntegrationImplementation(libs.testcontainers.postgresql)
    testIntegrationImplementation(libs.testcontainers.junit)

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
