package com.hellofresh.inventory.snapshot

import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotKey
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotValue
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.StockState
import java.math.BigDecimal
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.Optional
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME

fun generateTestData(
    i: Int,
    recordTimestampIncrement: Long = i.toLong()
): ConsumerRecord<InventorySnapshotKey, InventorySnapshotValue> {
    val time = OffsetDateTime.now(UTC).plusSeconds(recordTimestampIncrement)
    val key = InventorySnapshotKey(
        dcCode = "VE",
        skuCode = UUID.randomUUID().toString(),
    )

    val value = InventorySnapshotValue(
        snapshotId = UUID.randomUUID(),
        snapshotTime = time,
        stockState = StockState.STOCK_STATE_ACTIVE.name,
        quantity = BigDecimal(i),
        expirationTime = OffsetDateTime.now(UTC).plusSeconds(1000000),
        locationId = UUID.randomUUID().toString(),
        locationType = LocationType.LOCATION_TYPE_INBOUND,
        transportModuleId = UUID.randomUUID().toString(),
        messageCount = 1,
        poReference = UUID.randomUUID().toString(),
    )

    return ConsumerRecord(
        "test", 0, i.toLong(),
        Instant.now().toEpochMilli() + recordTimestampIncrement,
        CREATE_TIME, 1, 1, key, value,
        RecordHeaders(), Optional.empty(),
    )
}
