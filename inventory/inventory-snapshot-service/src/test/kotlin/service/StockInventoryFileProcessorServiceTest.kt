package com.hellofresh.inventory.snapshot.service

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.inventory.snapshot.schema.Tables
import com.hellofresh.cif.inventory.snapshot.schema.enums.FileUploadStatus
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventoryAllSnapshotsRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventoryProcessedSnapshotsRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.snapshot.service.StockInventoryFileProcessorService.Companion.Configuration
import com.hellofresh.sku.models.SkuSpecification
import io.mockk.coEvery
import io.mockk.mockk
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.ZoneId
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.jooq.Result
import org.junit.jupiter.api.Test

private const val BUCKET = "cif-stock-files-export"

class StockInventoryFileProcessorServiceTest : TestPrepare() {

    private val defaultMarket = "MRKT"
    private val expectedAuthorName = "author_name"
    private val expectedAuthorEmail = "author_email"
    private val s3Importer = mockk<S3Importer>()
    private val dcCodes = setOf("EU", "EX")
    private val zoneId = ZoneId.of("Europe/Berlin")

    private val stockInventoryFileProcessorService = StockInventoryFileProcessorService(
        s3Importer,
        skuSpecificationService,
        fileUploadRepository,
        inventorySnapshotRepository,
        dcConfigService,
        Configuration(allowedDcs = dcCodes, "test"),
    )

    @Test
    fun `should import inventory data`() {
        dcCodes.forEach { dcCode ->
            persistDcConfig(dcCode = dcCode, market = defaultMarket, zoneId = zoneId)
        }

        val key = "stock_inventory/stock_inventory.csv"
        val fileContentInByteArray = readFileContent(key)
        coEvery {
            s3Importer.fetchObjectContent(BUCKET, key)
        } returns ByteArrayInputStream(fileContentInByteArray)
        coEvery {
            s3Importer.fetchObjectMetadata(BUCKET, key)
        } returns mapOf(
            MARKET_METADATA to defaultMarket,
            AUTHOR_NAME_METADATA to expectedAuthorName,
            AUTHOR_EMAIL_METADATA to expectedAuthorEmail
        )

        insertSkuSpecification(
            listOf(
                TestSkuItem("PRO-00-90000-1", defaultMarket),
                TestSkuItem("PRO-00-90000-2", defaultMarket),
                TestSkuItem("PRO-00-90000-3", defaultMarket),
            ),
        )

        runBlocking { stockInventoryFileProcessorService.process(S3File(BUCKET, key)) }

        val inventoryProcessedRecords = dsl.selectFrom(Tables.INVENTORY_PROCESSED_SNAPSHOTS).fetch()
        val inventoryAllSnapshotRecords = dsl.selectFrom(Tables.INVENTORY_ALL_SNAPSHOTS).fetch()
        val inventorySnapshotRecords = dsl.selectFrom(Tables.INVENTORY_SNAPSHOT).fetch()
        val fileUploadRecords = dsl.selectFrom(Tables.FILE_UPLOADS).fetch()

        assertEquals(2, inventoryProcessedRecords.count())
        assertEquals(5, inventoryAllSnapshotRecords.count())
        assertEquals(5, inventorySnapshotRecords.count())
        assertEquals(1, fileUploadRecords.count())

        // Expected values in test resource CSV file
        assertSnapshot(
            mapOf(
                "PRO-00-90000-1" to listOf(ExpectedSkuInventory(600, LocalDate.of(2025, 10, 1))),
                "PRO-00-90000-2" to listOf(
                    ExpectedSkuInventory(250, null),
                    ExpectedSkuInventory(75, LocalDate.of(2025, 5, 1))
                ),
            ),
            "EU",
            LocalDate.of(2025, 4, 16),
            inventoryProcessedRecords,
            inventoryAllSnapshotRecords,
            inventorySnapshotRecords,
        )

        assertSnapshot(
            mapOf(
                "PRO-00-90000-1" to listOf(ExpectedSkuInventory(100, LocalDate.of(2025, 10, 1))),
                "PRO-00-90000-2" to listOf(ExpectedSkuInventory(200, null)),
                "PRO-00-90000-3" to listOf(ExpectedSkuInventory(300, LocalDate.of(2025, 5, 3))),
            ),
            "EX",
            LocalDate.of(2025, 4, 17),
            inventoryProcessedRecords,
            inventoryAllSnapshotRecords,
            inventorySnapshotRecords,
        )

        with(fileUploadRecords.first()) {
            assertTrue(key.contains(fileName))
            assertEquals(dcCodes, dcs.toSet())
            assertEquals(defaultMarket, market)
            assertEquals(expectedAuthorName, authorName)
            assertEquals(expectedAuthorEmail, authorEmail)
            assertEquals(FileUploadStatus.IMPORTED, status)
        }
    }

    private fun assertSnapshot(
        expectedSkus: Map<String, List<ExpectedSkuInventory>>,
        dcCode: String,
        inventoryDate: LocalDate,
        inventoryProcessedRecords: Result<InventoryProcessedSnapshotsRecord>,
        inventoryAllSnapshotRecords: Result<InventoryAllSnapshotsRecord>,
        inventorySnapshotRecords: Result<InventorySnapshotRecord>
    ) {
        val processedSnapshotRecord = inventoryProcessedRecords.first {
            it.snapshotTime.atZoneSameInstant(zoneId).toLocalDate() == inventoryDate
        }
        assertEquals(dcCode, processedSnapshotRecord.dcCode)

        val skuAllSnapshots = inventoryAllSnapshotRecords.filter { it.snapshotId == processedSnapshotRecord.snapshotId }
        val skuSnapshotRecords =
            inventorySnapshotRecords.filter {
                it.snapshotId == processedSnapshotRecord.snapshotId && it.dcCode == processedSnapshotRecord.dcCode
            }
        assertEquals(expectedSkus.size, skuSnapshotRecords.size)

        expectedSkus.forEach { (skuCode, expectedInventory) ->
            val (skuId, sku) = skuSpecificationService.specifications.entries.first { it.value.skuCode == skuCode }

            assertSkuInventories(expectedInventory, sku, skuAllSnapshots.first { it.skuId == skuId }.value)
            assertSkuInventories(expectedInventory, sku, skuSnapshotRecords.first { it.skuId == skuId }.value)
        }
    }

    private fun assertSkuInventories(
        expectedInventory: List<ExpectedSkuInventory>,
        sku: SkuSpecification,
        dbValue: JSONB
    ) {
        val skuInventories = objectMapper.readValue<InventoryValue>(dbValue.data()).inventory
        assertEquals(expectedInventory.size, skuInventories.size)
        expectedInventory.forEach { expected ->
            val inventory = skuInventories.first { it.expiryDate == expected.expiryDate }
            assertEquals(SkuQuantity.fromLong(expected.qty, sku.uom), inventory.qty)
            assertTrue(inventory.location.type.isUsable())
        }
    }

    data class ExpectedSkuInventory(val qty: Long, val expiryDate: LocalDate?)
}
