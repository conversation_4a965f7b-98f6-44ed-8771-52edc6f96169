package com.hellofresh.inventory.snapshot.service

import InfraPreparation
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.distribution_center_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.inventory.snapshot.schema.Tables
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepository
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.inventory.snapshot.repository.FileUploadRepository
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotRepository
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.io.File
import java.nio.file.Files
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class TestPrepare {

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        refreshSkuView()
        dsl.deleteFrom(Tables.INVENTORY_PROCESSED_SNAPSHOTS).execute()
        dsl.deleteFrom(Tables.INVENTORY_ALL_SNAPSHOTS).execute()
        dsl.deleteFrom(Tables.INVENTORY_SNAPSHOT).execute()
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
        dcConfigService.fetchOnDemand()
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    fun persistDcConfig(
        dcCode: String = "VE",
        market: String = "DACH",
        zoneId: ZoneId = ZoneId.of("Europe/Berlin"),
        productionStartDay: DayOfWeek = MONDAY
    ) {
        val dcConfig = DcConfigRecord(
            dcCode, market, productionStartDay.name, "FRIDAY", zoneId.id,
            true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
            emptyArray()
        )
        dsl.batchInsert(dcConfig).execute()
        dcConfigService.fetchOnDemand()
    }

    fun insertSkuSpecification(skuCodes: List<TestSkuItem>) {
        skuCodes.forEach { skuItem ->
            dsl.insertInto(
                SKU_SPECIFICATION,
                SKU_SPECIFICATION.ID,
                SKU_SPECIFICATION.PARENT_ID,
                SKU_SPECIFICATION.CATEGORY,
                SKU_SPECIFICATION.CODE,
                SKU_SPECIFICATION.NAME,
                SKU_SPECIFICATION.COOLING_TYPE,
                SKU_SPECIFICATION.PACKAGING,
                SKU_SPECIFICATION.ACCEPTABLE_CODE_LIFE,
                SKU_SPECIFICATION.MARKET,
            )
                .values(
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    "PRO",
                    skuItem.skuCode,
                    "${skuItem.skuCode}-name",
                    "coolingType",
                    "packaging",
                    0,
                    skuItem.market,
                )
                .execute()
        }.also {
            refreshSkuView()
        }
    }

    data class TestSkuItem(
        val skuCode: String,
        val market: String
    )

    protected fun readFileContent(fileName: String): ByteArray =
        with(this::class.java.classLoader) {
            File(getResource(fileName)!!.toURI())
        }.let {
            Files.readAllBytes(it.toPath())
        }

    companion object {

        val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        private val dataSource = InfraPreparation.getMigratedDataSource(nestedFolderCount = 2)
        lateinit var dsl: MetricsDSLContext
        lateinit var dcConfigRepository: DcRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var skuSpecificationService: SkuSpecificationService
        lateinit var skuSpecificationsRepo: SkuSpecificationRepository
        lateinit var inventorySnapshotRepository: InventorySnapshotRepository
        lateinit var fileUploadRepository: FileUploadRepository

        @BeforeAll
        @JvmStatic
        fun setUp() {
            val defaultConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(
                defaultConfiguration,
            ).withMetrics(SimpleMeterRegistry())
            dcConfigRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(
                SimpleMeterRegistry(),
                repo = dcConfigRepository,
            )
            skuSpecificationsRepo = SkuSpecificationRepositoryImpl(dsl)
            inventorySnapshotRepository = InventorySnapshotRepository(dsl)
            fileUploadRepository = FileUploadRepository(dsl)
            skuSpecificationService = SkuSpecificationService(SimpleMeterRegistry(), repo = SkuSpecificationRepositoryImpl(dsl))
        }
    }
}
