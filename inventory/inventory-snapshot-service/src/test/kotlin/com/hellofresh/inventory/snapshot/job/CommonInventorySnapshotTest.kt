package com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRawRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRawSkuRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRecord
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.snapshot.repository.RawInventorySnapshotRepository
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import org.jooq.DSLContext

object CommonInventorySnapshotTest {

    private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

    const val defaultDcCode = "DCX"
    val defaultDc = DistributionCenterConfiguration.default(defaultDcCode)

    @Suppress("LongParameterList")
    fun DSLContext.insertInventoryRaw(
        snapshotId: UUID,
        snapshotTime: OffsetDateTime,
        skuCode: String,
        messageCount: Int? = null,
        expirationDate: LocalDate? = null,
        inventorySnapshotSkuRecordBlock: InventorySnapshotRawSkuRecord.() -> Unit = {}
    ) {
        batchInsert(
            createInventoryRawRecord(snapshotId, snapshotTime, messageCount),
            createSkuInventoryRawRecord(
                snapshotId,
                snapshotTime,
                skuCode,
                expirationDate,
                inventorySnapshotSkuRecordBlock,
            ),

        ).execute()
    }

    fun createInventoryRawRecord(
        snapshotId: UUID,
        snapshotTime: OffsetDateTime,
        messageCount: Int? = null,
        applyBlock: InventorySnapshotRawRecord.() -> Unit = {}
    ) = InventorySnapshotRawRecord().apply {
        this.dcCode = defaultDcCode
        this.snapshotId = snapshotId
        this.snapshotTime = snapshotTime
        this.messageCount = messageCount
        applyBlock()
    }

    fun createSkuInventoryRawRecord(
        snapshotId: UUID,
        snapshotTime: OffsetDateTime,
        skuCode: String,
        expirationDate: LocalDate? = null,
        applyBlock: InventorySnapshotRawSkuRecord.() -> Unit = {}
    ) = InventorySnapshotRawSkuRecord().apply {
        this.dcCode = defaultDcCode
        this.snapshotId = snapshotId
        this.skuCode = skuCode
        this.expirationTimestamp = expirationDate?.atStartOfDay()?.atZone(UTC)?.toOffsetDateTime()
        this.state = RawInventorySnapshotRepository.stockStateActive
        this.locationId = "loc-id"
        this.locationType = LOCATION_TYPE_PRODUCTION.name
        applyBlock()
        this.hashMessage = snapshotId.toString() + snapshotTime + skuCode + locationType + state
        this.quantity = getExpectedQuantity(LocationType.valueOf(locationType))
    }

    fun assertInventory(
        snapshotId: UUID,
        snapshotTime: OffsetDateTime,
        inventorySnapshotRecord: InventorySnapshotRecord,
        expectedQuantity: BigDecimal? = null,
        assertBlock: (InventoryValue) -> Unit = { }
    ) {
        assertEquals(snapshotId, inventorySnapshotRecord.snapshotId)
        assertEquals(snapshotTime.toLocalDate(), inventorySnapshotRecord.date)
        assertEquals(defaultDcCode, inventorySnapshotRecord.dcCode)
        val inventoryValue = objectMapper.readValue<InventoryValue>(inventorySnapshotRecord.value.data())
        inventoryValue.inventory.forEach { inventory ->
            assertEquals(
                expectedQuantity?.stripTrailingZeros()?.toPlainString()?.toBigDecimal()
                    ?: getExpectedQuantity(inventory.location.type).stripTrailingZeros().toPlainString().toBigDecimal(),
                inventory.qty.getValue()
            )
        }
        assertBlock(inventoryValue)
    }

    fun getExpectedQuantity(locationType: LocationType?): BigDecimal =
        locationType?.let {
            when (locationType) {
                LOCATION_TYPE_STAGING -> BigDecimal(100)
                LOCATION_TYPE_PRODUCTION -> BigDecimal(200)
                LOCATION_TYPE_STORAGE -> BigDecimal(300)
                LOCATION_TYPE_QUARANTINE -> BigDecimal(1000)
                else -> BigDecimal(10)
            }
        } ?: BigDecimal.ZERO

    fun getExpirationOffsetDateTime(originalState: String, offsetDateTime: OffsetDateTime = OffsetDateTime.now(UTC)) =
        LocationType.parse(originalState).let { offsetDateTime.plusDays(it.ordinal.toLong()) }
}
