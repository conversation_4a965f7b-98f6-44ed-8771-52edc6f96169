package com.hellofresh.inventory.snapshot.job

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.Context.CATEGORY
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.DisableExpiryUnusable
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_INBOUND
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.StockState.STOCK_STATE_ACTIVE
import com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job.CommonInventorySnapshotTest.defaultDc
import com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job.CommonInventorySnapshotTest.defaultDcCode
import com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job.CommonInventorySnapshotTest.getExpectedQuantity
import com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job.CommonInventorySnapshotTest.getExpirationOffsetDateTime
import com.hellofresh.inventory.snapshot.job.InventorySnapshotJob.MarketSkuCodeKey
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotInfo
import com.hellofresh.inventory.snapshot.repository.SkuInventoryRawSnapshot
import com.hellofresh.inventory.snapshot.repository.SkuInventorySnapshot
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test

private const val DEFAULT_CATEGORY = "PHF"
private const val DEFAULT_DC = "IE"

class InventorySnapshotMapperTest {

    private val inventorySnapshotMapper = InventorySnapshotMapper(SimpleMeterRegistry())

    @Test
    fun `inventory raw is converted to itinerary snapshot`() {
        val statsigFeatureFlagClient = statsigTestFeatureFlagClient(DEFAULT_DC, DEFAULT_CATEGORY)
        val poReference = UUID.randomUUID().toString()
        val skuLookUpMap = (1..2).associate {
            MarketSkuCodeKey(defaultDc.market, UUID.randomUUID().toString()) to SkuDetails(UUID.randomUUID(), UUID.randomUUID().toString(), SkuUOM.UOM_UNIT)
        }
        val currentTime = OffsetDateTime.now(UTC)
        val inventorySnapshotInfo = InventorySnapshotInfo(UUID.randomUUID(), currentTime, true)
        val inventoryRawSnapshots = DcInventoryRawSnapshot(
            inventorySnapshotInfo.id,
            currentTime,
            inventorySnapshotInfo.isLatest,
            defaultDcCode,
            skuLookUpMap.flatMap { (key, _) ->
                listOf(
                    createSkuInventoryRawSnapshot(
                        key.skuCode,
                        LOCATION_TYPE_PRODUCTION,
                        getExpectedQuantity(LOCATION_TYPE_PRODUCTION),
                        poReference = poReference,
                    ),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_STAGING),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_STORAGE),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_QUARANTINE),
                )
            },
        )

        val inventorySnapshots =
            inventorySnapshotMapper.toInventorySnapshot(
                defaultDc,
                inventoryRawSnapshots,
                skuLookUpMap,
                statsigFeatureFlagClient,
            )

        skuLookUpMap.forEach { (_, skuInfo) ->
            with(inventorySnapshots.first { it.skuId == skuInfo.skuId }) {
                assertEquals(4, this.inventory.size)
                assertInventory(inventoryRawSnapshots.skuInventorySnapshots, this)
            }
        }
    }

    @Test
    fun `inventory raw should not be included in itinerary snapshot if the dc code is SY and po ref contains WA`() {
        val dcCode = "SY"
        val statsigFeatureFlagClient = statsigTestFeatureFlagClient(dcCode, DEFAULT_CATEGORY)
        val poReference = "2441WA473435"
        val skuLookUpMap = (1..2).associate {
            MarketSkuCodeKey(defaultDc.market, UUID.randomUUID().toString()) to SkuDetails(UUID.randomUUID(), UUID.randomUUID().toString(), SkuUOM.UOM_UNIT)
        }
        val currentTime = OffsetDateTime.now(UTC)
        val inventorySnapshotInfo = InventorySnapshotInfo(UUID.randomUUID(), currentTime, true)
        val inventoryRawSnapshots = DcInventoryRawSnapshot(
            inventorySnapshotInfo.id,
            currentTime,
            inventorySnapshotInfo.isLatest,
            dcCode,
            skuLookUpMap.flatMap { (key, _) ->
                listOf(
                    createSkuInventoryRawSnapshot(
                        key.skuCode,
                        LOCATION_TYPE_PRODUCTION,
                        getExpectedQuantity(LOCATION_TYPE_PRODUCTION),
                        poReference = poReference,
                    ),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_STAGING),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_STORAGE),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_QUARANTINE),
                )
            },
        )

        val inventorySnapshots =
            inventorySnapshotMapper.toInventorySnapshot(
                DistributionCenterConfiguration.default(dcCode),
                inventoryRawSnapshots,
                skuLookUpMap,
                statsigFeatureFlagClient,
            )

        skuLookUpMap.forEach { (_, skuInfo) ->
            with(inventorySnapshots.first { it.skuId == skuInfo.skuId }) {
                assertEquals(3, this.inventory.size)
                assertInventory(
                    inventoryRawSnapshots.skuInventorySnapshots.filter {
                        it.poReference != poReference
                    },
                    this
                )
            }
        }
    }

    @Test
    fun `inventory INBOUND raw is converted to storage snapshot`() {
        val statsigFeatureFlagClient = statsigTestFeatureFlagClient(DEFAULT_DC, DEFAULT_CATEGORY)
        val skuLookUpMap = (1..2).associate {
            MarketSkuCodeKey(defaultDc.market, UUID.randomUUID().toString()) to SkuDetails(UUID.randomUUID(), UUID.randomUUID().toString(), SkuUOM.UOM_UNIT)
        }
        val currentTime = OffsetDateTime.now(UTC)
        val inventorySnapshotInfo = InventorySnapshotInfo(UUID.randomUUID(), currentTime, true)
        val inventoryRawSnapshots = DcInventoryRawSnapshot(
            inventorySnapshotInfo.id,
            currentTime,
            inventorySnapshotInfo.isLatest,
            defaultDcCode,
            skuLookUpMap.flatMap { (key, _) ->
                listOf(
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_INBOUND),
                )
            },
        )

        val inventorySnapshots =
            inventorySnapshotMapper.toInventorySnapshot(
                defaultDc,
                inventoryRawSnapshots,
                skuLookUpMap,
                statsigFeatureFlagClient,
            )

        skuLookUpMap.forEach { (_, skuInfo) ->
            with(inventorySnapshots.first { it.skuId == skuInfo.skuId }) {
                assertEquals(1, this.inventory.size)
                assertInventory(inventoryRawSnapshots.skuInventorySnapshots, this)
            }
        }
    }

    @Test
    fun `inventory raw nullify expiryDate for Ireland DC and Sku Category PHF`() {
        val featureFlagClient = statsigTestFeatureFlagClient(DEFAULT_DC, DEFAULT_CATEGORY)
        val currentTime = OffsetDateTime.now(UTC)
        val validSkuId = UUID.randomUUID()

        val inventorySnapshots = createInventorySnapshot(featureFlagClient, "IE", currentTime, validSkuId)

        val inventorySnapshot = inventorySnapshots.last()
        assertNull(inventorySnapshots.first().inventory.first().expiryDate)
        assertEquals(validSkuId, inventorySnapshot.skuId)
        assertEquals(4, inventorySnapshot.inventory.size)
        with(
            inventorySnapshot.inventory.first {
                it.location.type in listOf(
                    LOCATION_TYPE_PRODUCTION,
                    LOCATION_TYPE_STAGING,
                    LOCATION_TYPE_STORAGE,
                    LOCATION_TYPE_QUARANTINE,
                )
            },
        ) {
            assertNotNull(this.expiryDate)
            assertTrue(this.expiryDate!!.isAfter(currentTime.toLocalDate()))
        }
    }

    @Test
    fun `return expiryDate for Ireland DC and Sku Category PHF when feature flag false`() {
        val featureFlagClient = statsigTestFeatureFlagClient("VE", "PHF")
        val validSkuId = UUID.randomUUID()
        val currentTime = OffsetDateTime.now(UTC)

        val inventorySnapshots = createInventorySnapshot(featureFlagClient, "IE", currentTime, validSkuId)

        assertNotNull(inventorySnapshots.first().inventory.first().expiryDate)
    }

    private fun createSkuInventoryRawSnapshotWithoutExpiry(
        skuCode: String,
        locationType: LocationType,
        expectedQuantity: BigDecimal? = null
    ) = SkuInventoryRawSnapshot(
        skuCode,
        expectedQuantity ?: getExpectedQuantity(locationType),
        null,
        "loc-id",
        locationType,
        null,
        STOCK_STATE_ACTIVE,
        null,
    )

    private fun createSkuInventoryRawSnapshot(
        skuCode: String,
        locationType: LocationType,
        expectedQuantity: BigDecimal? = null,
        poReference: String? = null
    ) =
        SkuInventoryRawSnapshot(
            skuCode,
            expectedQuantity ?: getExpectedQuantity(locationType),
            getExpirationOffsetDateTime(locationType.name),
            "loc-id",
            locationType,
            null,
            STOCK_STATE_ACTIVE,
            poReference,
        )

    private fun assertInventory(
        skuInventorySnapshots: List<SkuInventoryRawSnapshot>,
        inventorySnapshot: SkuInventorySnapshot
    ) {
        skuInventorySnapshots.forEach { rawInventory ->

            val inventory = inventorySnapshot.inventory.first { it.location.type == rawInventory.locationType }

            assertEquals(
                rawInventory.quantity.stripTrailingZeros().toPlainString().toBigDecimal(),
                inventory.qty.getValue()
            )
            assertEquals(rawInventory.expirationTimestamp?.toLocalDate(), inventory.expiryDate)
            assertEquals(rawInventory.locationId, inventory.location.id)
            assertEquals(rawInventory.locationType, inventory.location.type)
            assertEquals(rawInventory.transportModuleId, inventory.location.transportModuleId)
            assertEquals(rawInventory.poReference, inventory.poReference)
        }
    }

    private fun statsigTestFeatureFlagClient(dc: String, category: String) =
        StatsigTestFeatureFlagClient(
            setOf(DisableExpiryUnusable(setOf(ContextData(DC, dc), ContextData(CATEGORY, category))))
        )

    private fun createInventorySnapshot(
        statsigFeatureFlagClient: StatsigFeatureFlagClient,
        market: String,
        currentTime: OffsetDateTime,
        validSkuId: UUID,
    ): List<SkuInventorySnapshot> {
        val skuLookUpMap = mapOf(
            MarketSkuCodeKey("IE", "PHF-00-00000-01") to SkuDetails(UUID.randomUUID(), "PHF", SkuUOM.UOM_UNIT),
            MarketSkuCodeKey("IE", UUID.randomUUID().toString()) to SkuDetails(validSkuId, "TEST", SkuUOM.UOM_UNIT),
        )
        val inventorySnapshotInfo = InventorySnapshotInfo(UUID.randomUUID(), currentTime, true)
        val inventoryRawSnapshots = DcInventoryRawSnapshot(
            inventorySnapshotInfo.id,
            currentTime,
            inventorySnapshotInfo.isLatest,
            "IE",
            skuLookUpMap.flatMap { (key, _) ->
                listOf(
                    createSkuInventoryRawSnapshot(
                        key.skuCode,
                        LOCATION_TYPE_PRODUCTION,
                        getExpectedQuantity(LOCATION_TYPE_PRODUCTION),
                    ),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_STAGING),
                    createSkuInventoryRawSnapshot(key.skuCode, LOCATION_TYPE_STORAGE),
                    createSkuInventoryRawSnapshotWithoutExpiry(key.skuCode, LOCATION_TYPE_QUARANTINE),
                )
            },
        )

        return inventorySnapshotMapper.toInventorySnapshot(
            defaultDc.copy(market = market, dcCode = "IE"),
            inventoryRawSnapshots,
            skuLookUpMap,
            statsigFeatureFlagClient,
        )
    }
}
