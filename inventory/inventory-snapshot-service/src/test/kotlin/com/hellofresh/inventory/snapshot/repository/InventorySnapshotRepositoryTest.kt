package com.hellofresh.inventory.snapshot.repository

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_ALL_SNAPSHOTS
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_PROCESSED_SNAPSHOTS
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_SNAPSHOT
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventoryAllSnapshotsRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventoryProcessedSnapshotsRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_LITRE
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.mockk
import io.mockk.verify
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNull
import kotlinx.coroutines.runBlocking
import org.jooq.Result
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class InventorySnapshotRepositoryTest {

    @Test
    fun `inventory snapshot job skips the upsert if there is no snapshot`() {
        val mockContext = mockk<MetricsDSLContext>()
        val emptySnapshot = DcInventorySnapshot(
            "ve",
            UUID.randomUUID(),
            OffsetDateTime.now(UTC),
            true,
            LocalDate.now(UTC),
            emptyList(),
        )
        runBlocking { InventorySnapshotRepository(mockContext).save(emptySnapshot) }
        verify(exactly = 0) { mockContext.withTagName(any()) }
    }

    @Test
    fun `inventory snapshots are persisted`() {
        val snapshot1 = DcInventorySnapshot.Companion.default()
            .let {
                it.copy(skusSnapshot = it.skusSnapshot + it.skusSnapshot.first().copy(skuId = UUID.randomUUID()))
            }

        val snapshot2 = DcInventorySnapshot.Companion.default()
            .copy(
                snapshotTime = snapshot1.snapshotTime.plusDays(1),
                aggregatedDcSnapshotDate = snapshot1.aggregatedDcSnapshotDate.plusDays(1),
                skusSnapshot = snapshot1.skusSnapshot.take(1),
            )

        val expectedInventorySnapshots = listOf(snapshot1, snapshot2)

        expectedInventorySnapshots.forEach { snapshot ->
            runBlocking {
                inventorySnapshotRepository.save(snapshot)
            }
        }

        assertSnapshots(expectedInventorySnapshots)
    }

    @Test
    fun `inventory snapshots are NOT persisted if there is already inventory processed snapshots exist for the dc`() {
        val snapshot = DcInventorySnapshot.Companion.default()
            .let {
                it.copy(
                    skusSnapshot = it.skusSnapshot + it.skusSnapshot.first().copy(skuId = UUID.randomUUID()),
                    isLatest = false,
                )
            }

        val inventoryProcessedSnapshotRecord = InventoryProcessedSnapshotsRecord(
            snapshot.dcCode,
            UUID.randomUUID(),
            snapshot.snapshotTime.plusMinutes(60),
            OffsetDateTime.now(),
        )

        dsl.batchInsert(
            inventoryProcessedSnapshotRecord,
        ).execute()

        runBlocking {
            inventorySnapshotRepository.save(snapshot)
        }

        val inventorySnapshotRecords = dsl.selectFrom(INVENTORY_SNAPSHOT).fetch()
        assertNull(inventorySnapshotRecords.find { it.snapshotId == snapshot.snapshotId })
    }

    @Test
    fun `inventory snapshots are persisted with sku unit of measure`() {
        val snapshot = DcInventorySnapshot.Companion.default()
            .let {
                it.copy(
                    skusSnapshot = it.skusSnapshot + it.skusSnapshot.first().copy(
                        skuId = UUID.randomUUID(),
                        inventory = listOf(
                            Inventory(
                                qty = SkuQuantity.fromBigDecimal(BigDecimal(100L), UOM_LITRE),
                                expiryDate = LocalDate.now(UTC).plusDays(10),
                                location = Location("", LOCATION_TYPE_STORAGE, null),
                            ),
                        ),
                    ),
                )
            }

        val expectedInventorySnapshots = listOf(snapshot)

        expectedInventorySnapshots.forEach { expectedInventorySnapshot ->
            runBlocking {
                inventorySnapshotRepository.save(expectedInventorySnapshot)
            }
        }

        assertSnapshots(expectedInventorySnapshots)
    }

    @Test
    fun `inventory aggregated snapshot are deleted if exists and has same snapshotId, dc and sku`() {
        val inventorySnapshot = DcInventorySnapshot.Companion.default()

        runBlocking {
            inventorySnapshotRepository.save(inventorySnapshot)
        }

        val inventorySnapshotUpsert = inventorySnapshot.copy(
            skusSnapshot = inventorySnapshot.skusSnapshot.map {
                it.copy(inventory = emptyList())
            },
        )

        runBlocking {
            inventorySnapshotRepository.save(inventorySnapshotUpsert)
        }

        val inventorySnapshotRecords = dsl.selectFrom(INVENTORY_SNAPSHOT).fetch()

        assertEquals(1, inventorySnapshotRecords.size)
        inventorySnapshotRecords.first().apply {
            assertEquals(inventorySnapshotUpsert.dcCode, dcCode)
            assertEquals(inventorySnapshotUpsert.aggregatedDcSnapshotDate, date)
            assertEquals(inventorySnapshotUpsert.snapshotId, snapshotId)
            assertEquals(inventorySnapshotUpsert.skusSnapshot.first().skuId, skuId)
        }
    }

    @Test
    fun `inventory aggregated snapshot is updated if exists and has different snapshotId, dc and sku`() {
        val inventorySnapshot = DcInventorySnapshot.Companion.default()

        runBlocking {
            inventorySnapshotRepository.save(inventorySnapshot)
        }

        val (inventorySnapshotRecords, _) = assertSnapshots(listOf(inventorySnapshot))

        val inventorySnapshotUpsert = inventorySnapshot.copy(
            snapshotId = UUID.randomUUID(),
            skusSnapshot = inventorySnapshot.skusSnapshot.map {
                it.copy(
                    inventory = listOf(
                        it.inventory.first().copy(
                            qty = SkuQuantity.fromBigDecimal(BigDecimal(2000)),
                            LocalDate.now(UTC),
                        ),
                    ),
                )
            },
            snapshotTime = OffsetDateTime.now().plusMinutes(10),
        )
        runBlocking {
            inventorySnapshotRepository.save(inventorySnapshotUpsert)
        }

        val (inventorySnapshotRecordsAfterUpsert, _) = assertSnapshots(
            listOf(inventorySnapshotUpsert),
            listOf(inventorySnapshot, inventorySnapshotUpsert),
        )

        assertNotEquals(inventorySnapshotRecords, inventorySnapshotRecordsAfterUpsert)
    }

    private fun assertSnapshots(
        aggregatedDcInventorySnapshots: List<DcInventorySnapshot>,
        dcInventorySnapshots: List<DcInventorySnapshot> = aggregatedDcInventorySnapshots
    ): Pair<Result<InventorySnapshotRecord>, Result<InventoryAllSnapshotsRecord>> {
        val inventorySnapshotRecords = dsl.selectFrom(INVENTORY_SNAPSHOT).fetch()
        assertAggregatedSnapshots(aggregatedDcInventorySnapshots, inventorySnapshotRecords)
        val inventoryAllSnapshotRecords = dsl.selectFrom(INVENTORY_ALL_SNAPSHOTS).fetch()
        assertAllSnapshots(dcInventorySnapshots, inventoryAllSnapshotRecords)
        val inventoryProcessedSnapshotRecords = dsl.selectFrom(INVENTORY_PROCESSED_SNAPSHOTS).fetch()
        assertInventoryProcessedSnapshots(dcInventorySnapshots, inventoryProcessedSnapshotRecords)
        return inventorySnapshotRecords to inventoryAllSnapshotRecords
    }

    private fun assertAggregatedSnapshots(
        dcInventorySnapshots: List<DcInventorySnapshot>,
        inventorySnapshotRecords: List<InventorySnapshotRecord>
    ) {
        dcInventorySnapshots
            .forEach { inventorySnapshots ->
                inventorySnapshots.skusSnapshot.forEach { inventorySnapshot ->
                    val inventorySnapshotRecord = inventorySnapshotRecords.first {
                        it.skuId == inventorySnapshot.skuId &&
                            it.date == inventorySnapshots.aggregatedDcSnapshotDate &&
                            it.dcCode == inventorySnapshots.dcCode &&
                            it.snapshotId == inventorySnapshots.snapshotId
                    }
                    assertAggregatedInventoryRecord(inventorySnapshots, inventorySnapshot, inventorySnapshotRecord)
                }
            }
    }

    private fun assertAggregatedInventoryRecord(
        dcInventorySnapshot: DcInventorySnapshot,
        inventorySnapshot: SkuInventorySnapshot,
        inventorySnapshotRecord: InventorySnapshotRecord,
        assertBlock: SkuInventorySnapshot.(InventorySnapshotRecord) -> Unit = {}
    ) {
        assertEquals(dcInventorySnapshot.snapshotId, inventorySnapshotRecord.snapshotId)
        assertEquals(dcInventorySnapshot.aggregatedDcSnapshotDate, inventorySnapshotRecord.date)
        assertEquals(dcInventorySnapshot.dcCode, inventorySnapshotRecord.dcCode)
        assertEquals(inventorySnapshot.skuId, inventorySnapshotRecord.skuId)
        assertEquals(
            inventorySnapshot.inventory,
            objectMapper.readValue<InventoryValue>(inventorySnapshotRecord.value.data()).inventory,
        )

        inventorySnapshot.assertBlock(inventorySnapshotRecord)
    }

    private fun assertInventoryProcessedSnapshots(
        dcInventorySnapshots: List<DcInventorySnapshot>,
        inventoryProcessedSnapshotsRecords: List<InventoryProcessedSnapshotsRecord>
    ) {
        dcInventorySnapshots
            .forEach { inventorySnapshots ->
                val inventoryAllSnapshotRecord = inventoryProcessedSnapshotsRecords.first {
                    it.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS) ==
                        inventorySnapshots.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS) &&
                        it.dcCode == inventorySnapshots.dcCode &&
                        it.snapshotId == inventorySnapshots.snapshotId
                }
                assertInventoryProcessedSnapshotRecord(inventorySnapshots, inventoryAllSnapshotRecord)
            }
    }

    private fun assertAllSnapshots(
        dcInventorySnapshots: List<DcInventorySnapshot>,
        inventoryAllSnapshotsRecords: List<InventoryAllSnapshotsRecord>
    ) {
        dcInventorySnapshots
            .forEach { inventorySnapshots ->
                inventorySnapshots.skusSnapshot.forEach { inventorySnapshot ->
                    val inventoryAllSnapshotRecord = inventoryAllSnapshotsRecords.first {
                        it.skuId == inventorySnapshot.skuId &&
                            it.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS) ==
                            inventorySnapshots.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS) &&
                            it.dcCode == inventorySnapshots.dcCode &&
                            it.snapshotId == inventorySnapshots.snapshotId
                    }
                    assertInventoryAllRecord(inventorySnapshots, inventorySnapshot, inventoryAllSnapshotRecord)
                }
            }
    }

    private fun assertInventoryAllRecord(
        dcInventorySnapshot: DcInventorySnapshot,
        inventorySnapshot: SkuInventorySnapshot,
        inventoryAllSnapshotsRecord: InventoryAllSnapshotsRecord,
        assertBlock: SkuInventorySnapshot.(InventoryAllSnapshotsRecord) -> Unit = {}
    ) {
        assertEquals(dcInventorySnapshot.snapshotId, inventoryAllSnapshotsRecord.snapshotId)
        assertEquals(
            dcInventorySnapshot.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
            inventoryAllSnapshotsRecord.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
        )
        assertEquals(dcInventorySnapshot.dcCode, inventoryAllSnapshotsRecord.dcCode)
        assertEquals(inventorySnapshot.skuId, inventoryAllSnapshotsRecord.skuId)
        assertEquals(
            inventorySnapshot.inventory,
            objectMapper.readValue<InventoryValue>(inventoryAllSnapshotsRecord.value.data()).inventory,
        )

        inventorySnapshot.assertBlock(inventoryAllSnapshotsRecord)
    }

    private fun assertInventoryProcessedSnapshotRecord(
        dcInventorySnapshot: DcInventorySnapshot,
        inventoryProcessedSnapshotsRecord: InventoryProcessedSnapshotsRecord
    ) {
        assertEquals(dcInventorySnapshot.dcCode, inventoryProcessedSnapshotsRecord.dcCode)
        assertEquals(dcInventorySnapshot.snapshotId, inventoryProcessedSnapshotsRecord.snapshotId)
        assertEquals(
            dcInventorySnapshot.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
            inventoryProcessedSnapshotsRecord.snapshotTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
        )
    }

    @AfterEach
    fun cleanup() {
        dsl.truncate(INVENTORY_SNAPSHOT).execute()
        dsl.deleteFrom(INVENTORY_PROCESSED_SNAPSHOTS).execute()
        dsl.deleteFrom(INVENTORY_SNAPSHOT).execute()
    }

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        private lateinit var dsl: MetricsDSLContext
        private lateinit var inventorySnapshotRepository: InventorySnapshotRepository

        @JvmStatic
        @BeforeAll
        fun setUp() {
            dsl = DSL.using(
                DefaultConfiguration().apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                },
            ).withMetrics(SimpleMeterRegistry())
            inventorySnapshotRepository = InventorySnapshotRepository(dsl)
        }
    }
}
