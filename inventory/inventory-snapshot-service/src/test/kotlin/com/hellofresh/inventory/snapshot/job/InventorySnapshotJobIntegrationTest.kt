package com.hellofresh.inventory.snapshot.job

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.snapshot.schema.Tables
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_ALL_SNAPSHOTS
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_PROCESSED_SNAPSHOTS
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.StockState
import com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job.CommonInventorySnapshotTest.assertInventory
import com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job.CommonInventorySnapshotTest.createInventoryRawRecord
import com.hellofresh.inventory.snapshot.com.hellofresh.inventory.snapshot.job.CommonInventorySnapshotTest.createSkuInventoryRawRecord
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotRepository
import com.hellofresh.inventory.snapshot.repository.RawInventorySnapshotRepository
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class InventorySnapshotJobIntegrationTest {

    private val defaultDcCode = "DCX"
    private val defaultMarket = "DCXMARKET"

    @Test
    fun `inventory snapshot job calculates inventory snapshot from raw using latest completed`() {
        val sku1 = createSkuSpecRecords()
        val sku2 = createSkuSpecRecords()
        val sku3 = createSkuSpecRecords()
        createDcRecord()

        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime1 = OffsetDateTime.now(UTC)
        val snapshotID2 = UUID.randomUUID()
        val snapshotIdTime2 = snapshotIdTime1.minusMinutes(10)

        dsl.batchInsert(
            // LAST SNAPSHOT -- all messages
            createInventoryRawRecord(snapshotID1, snapshotIdTime1) {
                messageCount = 5
            },
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku1.code) {
                locationType = LOCATION_TYPE_STAGING.name
            },
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku1.code) {
                locationType = LOCATION_TYPE_PRODUCTION.name
            },
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku1.code) {
                locationType = LOCATION_TYPE_STORAGE.name
            },
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku2.code) {
                locationType = LOCATION_TYPE_PRODUCTION.name
            },
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku2.code) {
                locationType = LOCATION_TYPE_QUARANTINE.name
            },
            // SNAPSHOT-1 - 2 skus
            createInventoryRawRecord(snapshotID2, snapshotIdTime2),
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku1.code),
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku2.code),
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku3.code),
        ).execute()

        runBlocking { inventorySnapshotJob.run() }

        val inventorySnapshotRecords = dsl.selectFrom(Tables.INVENTORY_SNAPSHOT).fetch()

        assertEquals(2, inventorySnapshotRecords.size)

        // Sku1
        assertInventory(snapshotID1, snapshotIdTime1, inventorySnapshotRecords.first { it.skuId == sku1.id })
        assertInventory(snapshotID1, snapshotIdTime1, inventorySnapshotRecords.first { it.skuId == sku2.id })
    }

    @Test
    fun `inventory snapshot job calculates inventory snapshot in order`() {
        val sku1 = createSkuSpecRecords()
        createDcRecord()

        val firstSnapshotID = UUID.randomUUID()
        val firstSnapshotIdTime = OffsetDateTime.now(UTC).minusMinutes(30)
        val secondSnapshotID = UUID.randomUUID()
        val secondSnapshotIdTime = firstSnapshotIdTime.minusMinutes(10)
        val latestSnapshotID = UUID.randomUUID()
        val latestSnapshotIdTime = OffsetDateTime.now(UTC)

        dsl.batchInsert(
            createInventoryRawRecord(firstSnapshotID, firstSnapshotIdTime) {
                messageCount = 3
            },
            createSkuInventoryRawRecord(firstSnapshotID, firstSnapshotIdTime, sku1.code) {
                locationType = LOCATION_TYPE_STAGING.name
            },
            createSkuInventoryRawRecord(firstSnapshotID, firstSnapshotIdTime, sku1.code) {
                locationType = LOCATION_TYPE_PRODUCTION.name
            },
            createSkuInventoryRawRecord(firstSnapshotID, firstSnapshotIdTime, sku1.code) {
                locationType = LOCATION_TYPE_STORAGE.name
            },
            // SNAPSHOT-1 - 2 skus
            createInventoryRawRecord(secondSnapshotID, secondSnapshotIdTime, messageCount = 1),
            createSkuInventoryRawRecord(secondSnapshotID, secondSnapshotIdTime, sku1.code),

            createInventoryRawRecord(latestSnapshotID, latestSnapshotIdTime, messageCount = 1),
            createSkuInventoryRawRecord(latestSnapshotID, latestSnapshotIdTime, sku1.code),
        ).execute()

        runBlocking { inventorySnapshotJob.run() }

        val inventorySnapshotRecords = dsl.selectFrom(Tables.INVENTORY_SNAPSHOT).fetch()
        assertEquals(1, inventorySnapshotRecords.size)
        assertInventory(latestSnapshotID, latestSnapshotIdTime, inventorySnapshotRecords.first { it.skuId == sku1.id })

        val inventoryAllSnapshotRecords = dsl.selectFrom(INVENTORY_ALL_SNAPSHOTS).fetch()
        assertEquals(3, inventoryAllSnapshotRecords.size)

        val inventoryProcessedSnapshotRecords = dsl.selectFrom(INVENTORY_PROCESSED_SNAPSHOTS).fetch()
        assertEquals(3, inventoryProcessedSnapshotRecords.size)
    }

    @Test
    fun `inventory snapshot job calculates inventory snapshot from raw skipping latest`() {
        val sku1 = createSkuSpecRecords()
        val sku2 = createSkuSpecRecords()
        val sku3 = createSkuSpecRecords()
        createDcRecord()

        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime = OffsetDateTime.now(UTC)
        val snapshotID2 = UUID.randomUUID()
        val snapshotIdTime2 = snapshotIdTime.minusMinutes(10)
        val snapshotID3 = UUID.randomUUID()
        val snapshotIdTime3 = snapshotIdTime.minusMinutes(20)

        val sku2ExpectedExpirationDate = snapshotIdTime.plusDays(5).toLocalDate()

        dsl.batchInsert(
            // LAST SNAPSHOT -- Skip
            createInventoryRawRecord(snapshotID1, snapshotIdTime),
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime, sku1.code),
            // SNAPSHOT-1 - 2 skus
            createInventoryRawRecord(snapshotID2, snapshotIdTime2, messageCount = 5),
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku1.code) {
                locationType = LOCATION_TYPE_STAGING.name
            },
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku1.code) {
                locationType = LOCATION_TYPE_PRODUCTION.name
            },
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku1.code) {
                locationType = LOCATION_TYPE_STORAGE.name
            },
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku2.code, sku2ExpectedExpirationDate) {
                locationType = LOCATION_TYPE_PRODUCTION.name
            },
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku2.code) {
                locationType = LOCATION_TYPE_QUARANTINE.name
            },
            // SNAPSHOT-3 - 1 extra sku
            createInventoryRawRecord(snapshotID3, snapshotIdTime3, messageCount = 5),
            createSkuInventoryRawRecord(snapshotID3, snapshotIdTime3, sku1.code),
            createSkuInventoryRawRecord(snapshotID3, snapshotIdTime3, sku2.code),
            createSkuInventoryRawRecord(snapshotID3, snapshotIdTime3, sku3.code),
        ).execute()

        runBlocking { inventorySnapshotJob.run() }

        val inventorySnapshotRecords = dsl.selectFrom(Tables.INVENTORY_SNAPSHOT).fetch()

        assertEquals(2, inventorySnapshotRecords.size)

        // Sku1
        assertInventory(snapshotID2, snapshotIdTime2, inventorySnapshotRecords.first { it.skuId == sku1.id })
        assertInventory(
            snapshotID2,
            snapshotIdTime2,
            inventorySnapshotRecords.first { it.skuId == sku2.id },
        ) { inventory ->
            assertEquals(
                sku2ExpectedExpirationDate,
                inventory.inventory.first { it.location.type.isUsable() }.expiryDate,
            )
        }
    }

    @Test
    fun `inventory snapshot job calculates inventory snapshot from active raw inventory only`() {
        val sku1 = createSkuSpecRecords()
        val sku2 = createSkuSpecRecords()
        val sku3 = createSkuSpecRecords()
        createDcRecord()

        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime1 = OffsetDateTime.now(UTC)
        val snapshotID2 = UUID.randomUUID()
        val snapshotIdTime2 = snapshotIdTime1.minusMinutes(10)

        dsl.batchInsert(
            // LAST SNAPSHOT -- all messages
            createInventoryRawRecord(snapshotID1, snapshotIdTime1, messageCount = 3),
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku1.code) {
                locationType = LOCATION_TYPE_STAGING.name
            },
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku2.code) {
                locationType = LOCATION_TYPE_PRODUCTION.name
                state = StockState.STOCK_STATE_GONE.name
            },
            createSkuInventoryRawRecord(snapshotID1, snapshotIdTime1, sku3.code) {
                locationType = LOCATION_TYPE_STORAGE.name
                state = StockState.STOCK_STATE_LOST.name
            },
            // SNAPSHOT-1 - 2 skus
            createInventoryRawRecord(snapshotID2, snapshotIdTime2),
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku1.code),
            createSkuInventoryRawRecord(
                snapshotID2,
                snapshotIdTime2,
                sku2.code,
            ).apply { state = StockState.STOCK_STATE_GONE.name },
            createSkuInventoryRawRecord(snapshotID2, snapshotIdTime2, sku3.code).apply {
                state = StockState.STOCK_STATE_UNSPECIFIED.name
            },
        ).execute()

        runBlocking { inventorySnapshotJob.run() }

        val inventorySnapshotRecords = dsl.selectFrom(Tables.INVENTORY_SNAPSHOT).fetch()

        assertEquals(1, inventorySnapshotRecords.size)

        // Sku1
        assertInventory(snapshotID1, snapshotIdTime1, inventorySnapshotRecords.first { it.skuId == sku1.id })
    }

    @Test
    fun `inventory snapshot job is not executed if there arent any raw snapshots recorded`() {
        runBlocking { inventorySnapshotJob.run() }
        val inventorySnapshotRecords = dsl.selectFrom(Tables.INVENTORY_SNAPSHOT).fetch()
        assertEquals(0, inventorySnapshotRecords.size)
    }

    private fun createDcRecord() = DcConfigRecord().apply {
        this.dcCode = defaultDcCode
        this.market = defaultMarket
        productionStart = "MONDAY"
        cleardown = "FRIDAY"
        zoneId = UTC.id
        enabled = true
        hasCleardown = true
        recordTimestamp_ = LocalDateTime.now()
    }.also {
        dsl.batchInsert(it).execute()
    }

    private fun createSkuSpecRecords(skuId: UUID = UUID.randomUUID(), skuCode: String = UUID.randomUUID().toString()) = SkuSpecificationRecord().apply {
        id = skuId
        this.market = defaultMarket
        name = "Sku$id"
        code = skuCode
        category = ""
        acceptableCodeLife = 0
        coolingType = ""
        packaging = ""
        uom = UOM_LBS
    }.also {
        dsl.batchInsert(it).execute()
        refreshSkuView()
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(INVENTORY_PROCESSED_SNAPSHOTS).execute()
        dsl.deleteFrom(Tables.INVENTORY_SNAPSHOT_RAW).execute()
        dsl.truncate(Tables.INVENTORY_SNAPSHOT).execute()
        dsl.truncate(Tables.DC_CONFIG).execute()
        dsl.truncate(SKU_SPECIFICATION).execute()
    }

    companion object {

        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        private lateinit var dsl: MetricsDSLContext
        private lateinit var dcRepository: DcRepository
        private lateinit var dcConfigService: DcConfigService
        private lateinit var skuInputDataRepository: SkuInputDataRepository
        private lateinit var rawInventorySnapshotRepository: RawInventorySnapshotRepository
        private lateinit var inventorySnapshotRepository: InventorySnapshotRepository

        private lateinit var inventorySnapshotJob: InventorySnapshotJob
        private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

        @JvmStatic
        @BeforeAll
        fun setUp() {
            dsl = DSL.using(
                DefaultConfiguration().apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                },
            ).withMetrics(SimpleMeterRegistry())
            dcRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(SimpleMeterRegistry(), dcRepository)
            skuInputDataRepository = SkuInputDataRepositoryImpl(dsl, dcConfigService)
            rawInventorySnapshotRepository = RawInventorySnapshotRepository(dsl)
            inventorySnapshotRepository = InventorySnapshotRepository(dsl)

            inventorySnapshotJob = InventorySnapshotJob(
                dcConfigService,
                skuInputDataRepository,
                rawInventorySnapshotRepository,
                inventorySnapshotRepository,
                statsigFeatureFlagClient,
                SimpleMeterRegistry(),
            )
        }
    }
}
