package com.hellofresh.inventory.snapshot.repository

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.inventory.snapshot.schema.Tables
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventoryProcessedSnapshotsRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRawRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRawSkuRecord
import com.hellofresh.inventory.models.LocationType
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class RawInventorySnapshotRepositoryTest {

    @Test
    fun `returns latest available dc snapshot info`() {
        val now = OffsetDateTime.now(UTC)

        val records = (1..3).flatMap { dcCount ->
            (1..6).map { snapshotCount ->
                val uuid = UUID.randomUUID()
                InventorySnapshotRawRecord().apply {
                    this.dcCode = "dc$dcCount"
                    this.snapshotId = uuid
                    this.snapshotTime = now.minusMinutes(snapshotCount * 15L)
                    this.messageCount = 0
                }
            }
        }
        dsl.batchInsert(records).execute()

        val snapshotInfos = runBlocking { rawInventorySnapshotRepository.fetchLatestNonProcessedInventorySnapshots() }

        assertEquals(3, snapshotInfos.size)
        snapshotInfos.forEach { dcSnapshots ->
            assertEquals(
                records.asSequence().filter { it.dcCode == dcSnapshots.dcCode }
                    .groupBy { it.snapshotId }
                    .mapValues { (_, values) -> values.maxOf { it.snapshotTime } }
                    .asSequence()
                    .map { (k, v) -> k to v }
                    .sortedByDescending { it.second }
                    .take(6)
                    .map { it.first }.toSet(),
                dcSnapshots.pendingInventorySnapshotsInfo.map { it.id }.toSet(),
            )
            assertTrue(dcSnapshots.pendingInventorySnapshotsInfo.all { it.isLatest })
        }
    }

    @Test
    fun `returns dc inventory snapshots data using the inventory processed snapshots`() {
        val now = OffsetDateTime.now(UTC)
        val dcCode1 = "dcCode1"
        val dcCode2 = "dcCode2"

        val snapshotId1 = UUID.randomUUID()
        val snapshotId2 = UUID.randomUUID()
        val dcCodeToSnapshotIds = mapOf(dcCode1 to snapshotId1, dcCode2 to snapshotId2)
        val messageCount = 1

        val snapshotRecords = dcCodeToSnapshotIds
            .map { (dcCode, id) ->
                InventorySnapshotRawRecord().apply {
                    this.dcCode = dcCode
                    this.snapshotId = id
                    this.snapshotTime = now.plusMinutes(15L)
                    this.messageCount = messageCount
                }
            }.groupBy { it.snapshotId }

        val skuRecords = dcCodeToSnapshotIds.map { (dcCode, snapshotId) ->
            InventorySnapshotRawSkuRecord().apply {
                this.dcCode = dcCode
                this.snapshotId = snapshotId
                this.hashMessage = dcCode + snapshotId.toString() + LocalDateTime.now()
                this.skuCode = "skuCode" + UUID.randomUUID()
                this.quantity = BigDecimal("10")
                this.state = RawInventorySnapshotRepository.stockStateActive
                this.locationId = "loc-id"
                this.locationType = "locationType"
            }
        }.groupBy { it.snapshotId }

        dsl.batchInsert(snapshotRecords.values.flatten()).execute()
        dsl.batchInsert(skuRecords.values.flatten()).execute()

        dcCodeToSnapshotIds.map { (dcCode, snapshotId) ->
            val dcSnapshotInfos = runBlocking {
                rawInventorySnapshotRepository.fetchLatestNonProcessedInventorySnapshots()
            }
            assertEquals(2, dcSnapshotInfos.size)
            assertEquals(1, dcSnapshotInfos.find { it.dcCode == dcCode }?.pendingInventorySnapshotsInfo?.size)
            assertEquals(
                snapshotId,
                dcSnapshotInfos.find { it.dcCode == dcCode }?.pendingInventorySnapshotsInfo?.first()?.id,
            )
            assertTrue(dcSnapshotInfos.find { it.dcCode == dcCode }?.pendingInventorySnapshotsInfo?.first()?.isLatest!!)
        }
    }

    @Test
    fun `returns empty dc inventory snapshots data where inventory snapshots already exist in inventory processed snapshots`() {
        val now = OffsetDateTime.now(UTC)
        val dcCode1 = "dcCode1"

        val snapshotId1 = UUID.randomUUID()
        val dcCodeToSnapshotIds = mapOf(dcCode1 to snapshotId1)
        val messageCount = 1

        val inventoryProcessedSnapshots = dcCodeToSnapshotIds
            .map { (dcCode, id) ->
                InventoryProcessedSnapshotsRecord().apply {
                    this.dcCode = dcCode
                    this.snapshotId = id
                    this.snapshotTime = now.plusMinutes(15L)
                }
            }.groupBy { it.snapshotId }

        val snapshotRecords = dcCodeToSnapshotIds
            .map { (dcCode, id) ->
                InventorySnapshotRawRecord().apply {
                    this.dcCode = dcCode
                    this.snapshotId = id
                    this.snapshotTime = now.plusMinutes(15L)
                    this.messageCount = messageCount
                }
            }.groupBy { it.snapshotId }

        val skuRecords = dcCodeToSnapshotIds.map { (dcCode, snapshotId) ->
            InventorySnapshotRawSkuRecord().apply {
                this.dcCode = dcCode
                this.snapshotId = snapshotId
                this.hashMessage = dcCode + snapshotId.toString() + LocalDateTime.now()
                this.skuCode = "skuCode" + UUID.randomUUID()
                this.quantity = BigDecimal("10")
                this.state = RawInventorySnapshotRepository.stockStateActive
                this.locationId = "loc-id"
                this.locationType = "locationType"
            }
        }.groupBy { it.snapshotId }

        dsl.batchInsert(inventoryProcessedSnapshots.values.flatten()).execute()
        dsl.batchInsert(snapshotRecords.values.flatten()).execute()
        dsl.batchInsert(skuRecords.values.flatten()).execute()

        val dcSnapshotInfos = runBlocking {
            rawInventorySnapshotRepository.fetchLatestNonProcessedInventorySnapshots()
        }
        assertEquals(0, dcSnapshotInfos.size)
    }

    @Test
    fun `returns dc inventory snapshots data where inventory snapshots already DOES NOT exist in inventory processed snapshots`() {
        val now = OffsetDateTime.now(UTC)
        val dcCode = "dcCode1"

        val snapshotId1 = UUID.randomUUID()
        val dcCodeToSnapshotIds = mapOf(dcCode to snapshotId1)
        val messageCount = 1

        val inventoryProcessedSnapshots = dcCodeToSnapshotIds
            .map { (dcCode, _) ->
                InventoryProcessedSnapshotsRecord().apply {
                    this.dcCode = dcCode
                    this.snapshotId = UUID.randomUUID()
                    this.snapshotTime = now.plusMinutes(15L)
                }
            }.groupBy { it.snapshotId }

        val snapshotRecords = dcCodeToSnapshotIds
            .map { (dcCode, id) ->
                InventorySnapshotRawRecord().apply {
                    this.dcCode = dcCode
                    this.snapshotId = id
                    this.snapshotTime = now.plusMinutes(15L)
                    this.messageCount = messageCount
                }
            }.groupBy { it.snapshotId }

        val skuRecords = dcCodeToSnapshotIds.map { (dcCode, snapshotId) ->
            InventorySnapshotRawSkuRecord().apply {
                this.dcCode = dcCode
                this.snapshotId = snapshotId
                this.hashMessage = dcCode + snapshotId.toString() + LocalDateTime.now()
                this.skuCode = "skuCode" + UUID.randomUUID()
                this.quantity = BigDecimal("10")
                this.state = RawInventorySnapshotRepository.stockStateActive
                this.locationId = "loc-id"
                this.locationType = "locationType"
            }
        }.groupBy { it.snapshotId }

        dsl.batchInsert(inventoryProcessedSnapshots.values.flatten()).execute()
        dsl.batchInsert(snapshotRecords.values.flatten()).execute()
        dsl.batchInsert(skuRecords.values.flatten()).execute()

        val dcSnapshotInfos = runBlocking {
            rawInventorySnapshotRepository.fetchLatestNonProcessedInventorySnapshots()
        }
        assertEquals(1, dcSnapshotInfos.size)
        assertEquals(1, dcSnapshotInfos.find { it.dcCode == dcCode }?.pendingInventorySnapshotsInfo?.size)
        assertFalse(dcSnapshotInfos.find { it.dcCode == dcCode }?.pendingInventorySnapshotsInfo?.first()?.isLatest!!)
    }

    @Test
    fun `returns inventory raw data given a snapshot id`() {
        val now = OffsetDateTime.now(UTC)
        val dcCode1 = "dcCode1"
        val dcCode2 = "dcCode2"

        val snapshotId1 = UUID.randomUUID()
        val snapshotId2 = UUID.randomUUID()
        val expectedSnapshotIds = setOf(snapshotId1, snapshotId2)
        val expectedDcCodes = setOf(dcCode1, dcCode2)
        val messageCount = LocationType.values().size * 2

        val snapshotRecords = expectedSnapshotIds
            .flatMapIndexed { snapshotCount, snapshotId ->
                expectedDcCodes.map { dc ->
                    InventorySnapshotRawRecord().apply {
                        this.dcCode = dc
                        this.snapshotId = snapshotId
                        this.snapshotTime = now.plusMinutes(snapshotCount * 15L)
                        this.messageCount = messageCount
                    }
                }
            }.groupBy { it.snapshotId }

        val skuRecords = snapshotRecords.flatMap { (_, snapshots) ->
            snapshots.flatMap { snapshot ->
                LocationType.values()
                    .map { locationType ->
                        InventorySnapshotRawSkuRecord().apply {
                            this.dcCode = snapshot.dcCode
                            this.snapshotId = snapshot.snapshotId
                            this.hashMessage = snapshot.dcCode + snapshotId.toString() + locationType.ordinal
                            this.skuCode = "skuCode$locationType"
                            this.quantity = BigDecimal(locationType.ordinal)
                            this.state = RawInventorySnapshotRepository.stockStateActive
                            this.locationId = "loc-id"
                            this.locationType = locationType.toString()
                        }
                    }
            }
        }.groupBy { it.snapshotId }

        dsl.batchInsert(snapshotRecords.values.flatten()).execute()
        dsl.batchInsert(skuRecords.values.flatten()).execute()

        expectedSnapshotIds.forEach { snapshotId ->
            val inventoryRawSnapshots = runBlocking {
                rawInventorySnapshotRepository.fetchInventoryRawSnapshot(
                    snapshotId,
                )
            }!!

            assertEquals(messageCount, inventoryRawSnapshots.availableSkuSnapshotCount())
            assertEquals(messageCount, inventoryRawSnapshots.expectedCount)

            assertEquals(expectedDcCodes, inventoryRawSnapshots.dcSnapshots.keys)

            expectedDcCodes.forEach { dcCode ->
                skuRecords[snapshotId]!!.filter { it.dcCode == dcCode }.forEach { record ->
                    assertInventoryRawSnapshot(
                        record,
                        inventoryRawSnapshots.dcSnapshots[dcCode]!!.first {
                            it.skuCode == record.skuCode
                        },
                    )
                }
            }
        }
    }

    @Test
    fun `returns null when no snapshot id is found`() {
        val snapshotId = UUID.randomUUID()

        val record = InventorySnapshotRawRecord().apply {
            this.dcCode = "dc"
            this.snapshotId = snapshotId
            this.snapshotTime = OffsetDateTime.now(UTC)
            this.messageCount = 1
        }
        val skuRecord = InventorySnapshotRawSkuRecord().apply {
            this.dcCode = record.dcCode
            this.snapshotId = record.snapshotId
            this.hashMessage = record.dcCode + record.snapshotId.toString()
            this.skuCode = "skuCode"
            this.quantity = BigDecimal.ONE
            this.state = RawInventorySnapshotRepository.stockStateActive
            this.locationId = "loc-id"
            this.locationType = LocationType.LOCATION_TYPE_PRODUCTION.name
        }

        dsl.batchInsert(record, skuRecord).execute()

        assertNotNull(runBlocking { rawInventorySnapshotRepository.fetchInventoryRawSnapshot(snapshotId) })
        assertNull(runBlocking { rawInventorySnapshotRepository.fetchInventoryRawSnapshot(UUID.randomUUID()) })
    }

    private fun assertInventoryRawSnapshot(
        record: InventorySnapshotRawSkuRecord,
        skuInventoryRawSnapshot: SkuInventoryRawSnapshot
    ) {
        assertEquals(record.skuCode, skuInventoryRawSnapshot.skuCode)
        assertEquals(record.quantity, skuInventoryRawSnapshot.quantity)
        assertEquals(record.expirationTimestamp, skuInventoryRawSnapshot.expirationTimestamp)
        assertEquals(record.locationId, skuInventoryRawSnapshot.locationId)
        assertEquals(record.locationType, skuInventoryRawSnapshot.locationType.toString())
        assertEquals(record.transportModuleId, skuInventoryRawSnapshot.transportModuleId)
    }

    @Test
    fun `raw location type is unknown when error parsing`() {
        assertEquals(LocationType.UNKNOWN, LocationType.parse(UUID.randomUUID().toString()))
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(Tables.INVENTORY_SNAPSHOT_RAW).execute()
        dsl.deleteFrom(Tables.INVENTORY_PROCESSED_SNAPSHOTS).execute()
    }

    companion object {
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        private lateinit var dsl: MetricsDSLContext
        private lateinit var rawInventorySnapshotRepository: RawInventorySnapshotRepository

        @JvmStatic
        @BeforeAll
        fun setUp() {
            dsl = DSL.using(
                DefaultConfiguration().apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                },
            ).withMetrics(SimpleMeterRegistry())
            rawInventorySnapshotRepository = RawInventorySnapshotRepository(dsl)
        }
    }
}
