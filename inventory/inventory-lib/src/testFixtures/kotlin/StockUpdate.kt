package com.hellofresh.cif.inventory

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random

fun StockUpdate.Companion.random() = with(Random(System.nanoTime())) {
    StockUpdate(
        skuId = UUID.randomUUID(),
        dcCode = UUID.randomUUID().toString(),
        date = LocalDate.now().plusDays(nextLong(10)),
        quantity = SkuQuantity.fromDouble(nextDouble(10.0, 10000.0), SkuUOM.entries.random()),
        reason = UUID.randomUUID().toString(),
        reasonDetail = UUID.randomUUID().toString(),
        authorName = UUID.randomUUID().toString(),
        authorEmail = UUID.randomUUID().toString(),
        version = nextInt(1, 5),
        deleted = false,
        createdAt = LocalDateTime.now(UTC),
        week = "2024-W${nextInt(11, 52)}",
    )
}
