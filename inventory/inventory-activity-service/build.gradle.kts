plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
    hellofresh.`test-fixtures`
}

description = "Dumps the inventory activity data"
group = "$group.inventory.activity"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "dc_config|inventory_activity|inventory_activity_type"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.inventoryModels)
    implementation(projects.distributionCenterLib)
    implementation(projects.skuInputsLib)
    implementation(projects.lib.db)
    implementation(projects.dateUtilModels)
    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)
    implementation(libs.jackson.kotlin)
    implementation(projects.purchaseOrder.purchaseOrderModels)

    testImplementation(libs.testcontainers.core)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.testcontainers.kafka)
    testImplementation(libs.testcontainers.junit)
    testImplementation(projects.libTests)
    testImplementation(libs.mockk)
    testImplementation(projects.dateUtilModels)
    testImplementation(testFixtures(projects.inventoryModels))
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.skuModels))
}
