---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

deployments:
  app:
    resources:
      requests:
        cpu: 1
        memory: 1Gi
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    hpa:
      enabled: false
    replicaCount: 1
    minAvailable: 0
    deploymentStrategy:
      type: Recreate
    containerPorts:
      http: 8080
    repository: '@dockerRepository@'
    command: [ "sh" ]
    args:
      - -c
      - echo "$HF_KAFKA_SSL_CA_PEM" > /tmp/ca.crt && exec /benthos -c /etc/benthos/config.yaml -r "/etc/benthos/resources-*.yaml" streams /etc/benthos/streams
    pullPolicy: IfNotPresent
    env:
      LOG_LEVEL: INFO
      HF_TIER: '@tier@'
      BENTHOS_PORT: '8080'
      METRICS_PREFIX: '@projectKey@<EMAIL>@'
      KAFKA_TLS_ENABLED: 'true'
      KAFKA_ROOT_CA: '/tmp/ca.crt'
      KAFKA_USERNAME: '@projectName@'
      HF_KAFKA_SASL_MECHANISM: 'PLAIN'
      KAFKA_CLIENT_ID: '@projectKey@-@applicationName@'
      KAFKA_SASL_MECHANISM: PLAIN
      INVENTORY_DB_URL: 'inventory-db000.@<EMAIL>/inventory'
      DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_MASTER_HOST'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_KAFKA_SASL_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_KAFKA_BOOTSTRAP_SERVERS: 'vault:@tier@/key-value/data/kafka#KAFKA_BOOTSTRAP_SERVERS'
      HF_KAFKA_SSL_CA_PEM: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#ca'
    livenessProbe:
      httpGet:
        path: /ping
        port: http
    readinessProbe:
      httpGet:
        path: /ready
        port: http

services:
  app:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080

configMap:
  SKU_SPECIFICATION_PROCESSOR_GROUP_ID: '@projectKey@-@applicationName@-sku-specification.v4'
  SKU_SPECIFICATION_TOPIC: 'csku-inventory-forecast.intermediate.sku-specification'
  SKU_SPECIFICATION_TOPIC_VERSION: '@csku-inventory-forecast.intermediate.sku-specification@'

