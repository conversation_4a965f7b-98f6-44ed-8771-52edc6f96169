{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Sku Specification", "type": "object", "properties": {"parent_id": {"type": ["string", "null"], "description": "Parent sku Id"}, "category": {"type": "string", "description": "Sku category"}, "sku_code": {"type": "string", "description": "Sku Code"}, "name": {"type": "string", "description": "Sku name"}, "uom": {"type": ["string", "null"], "description": "Units of measurement"}}}