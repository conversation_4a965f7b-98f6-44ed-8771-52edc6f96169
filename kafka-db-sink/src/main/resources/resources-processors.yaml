---
processor_resources:
  - label: json_decode_sku_spec
    json_schema:
      schema_path: "file:///etc/benthos/sku-specification-schema.json"

  - label: log_tombstone_messagelog_tombstone_message
    log:
      level: DEBUG
      message: "Tombstone message received"
      fields_mapping: |
        root.meta = meta()

  - label: log_failed_message_decode
    log:
      level: WARN
      message: "Message decode failed"
      fields_mapping: |
        root.meta = meta()
        root.error_message = error()

  - label: log_successful_message_decode
    log:
      level: DEBUG
      message: "Message decoded"
      fields_mapping: |
        root.meta = meta()
        root.message_content = this
