package com.hellofresh.dateUtil.models

import com.google.type.DateTime
import com.google.type.TimeZone
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class TimeTest {

    @Test fun `OffsetDateTime and Proto Timestamp conversion is consistent bidirectionally`() {
        val time = OffsetDateTime.now(UTC).plusNanos(1000)
        assertEquals(time, time.toProtoTimestamp().toOffsetDateTime())
    }

    @Test fun `DateTime to LocalDate works as expected`() {
        val zoneId = ZoneId.of("America/New_York")
        val timeInUS = ZonedDateTime.now(zoneId)
        val timeInUTC = timeInUS.withZoneSameInstant(UTC)

        val protoDateTime = DateTime.newBuilder().apply {
            timeZone = TimeZone.newBuilder().setId("UTC").build()
            year = timeInUTC.year
            month = timeInUTC.month.value
            day = timeInUTC.dayOfMonth
            hours = timeInUTC.hour
            seconds = timeInUTC.second
        }.build()

        val actual = protoDateTime.toLocalDate(zoneId)
        assertEquals(timeInUS.toLocalDate(), actual)
    }
}
