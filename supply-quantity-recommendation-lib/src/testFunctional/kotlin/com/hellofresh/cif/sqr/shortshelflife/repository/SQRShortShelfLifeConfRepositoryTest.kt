package com.hellofresh.cif.sqr.shortshelflife.repository

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.sqr.FunctionalTest
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConf
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConfigurations.Companion.default
import com.hellofresh.cif.sqrlib.schema.tables.records.SqrShortShelfLifeConfRecord
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.time.DayOfWeek.TUESDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class SQRShortShelfLifeConfRepositoryTest : FunctionalTest() {

    private val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()
        .copy(
            productionStart = TUESDAY,
            zoneId = UTC,
        )

    private val dcCode = distributionCenterConfiguration.dcCode

    @Test
    fun `sqr ssl are fetched from latest available date`() {
        val (currentStartDate, currentEndDate) = distributionCenterConfiguration.getCurrentProductionDateRange()

        val skuid1 = UUID.randomUUID()
        val sqrSsl1_1 = insertSQRShortShelfLifeConfiguration(dcCode, currentStartDate.plusDays(1), skuid1, ONE, TEN)
        val sqrSsl1_2 = insertSQRShortShelfLifeConfiguration(dcCode, currentEndDate, skuid1, ONE + ONE, TEN + TEN)

        val skuid2 = UUID.randomUUID()
        val sqrSsl2_1 = insertSQRShortShelfLifeConfiguration(dcCode, currentStartDate.minusWeeks(1), skuid2, ONE, TEN)
        // This record will be ignored
        insertSQRShortShelfLifeConfiguration(dcCode, currentStartDate.minusWeeks(2), skuid2, ONE + ONE, TEN + TEN)

        val configurations = runBlocking {
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromCurrentWeek(
                distributionCenterConfiguration,
            )
        }

        // Current Week
        assertEquals(
            default(dcCode, skuid1, currentStartDate),
            configurations.getConfiguration(dcCode, skuid1, currentStartDate),
        )
        assertConfigurations(sqrSsl1_1, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_1.date))
        assertConfigurations(sqrSsl1_2, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_2.date))
        // Next Week Same Result
        assertEquals(
            default(dcCode, skuid1, currentStartDate.plusWeeks(1)),
            configurations.getConfiguration(dcCode, skuid1, currentStartDate.plusWeeks(1)),
        )
        assertConfigurations(sqrSsl1_1, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_1.date.plusWeeks(1)))
        assertConfigurations(sqrSsl1_2, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_2.date.plusWeeks(1)))

        // Matching just previous week
        assertEquals(
            default(dcCode, skuid2, currentStartDate.minusWeeks(1).plusDays(1)),
            configurations.getConfiguration(dcCode, skuid2, currentStartDate.minusWeeks(1).plusDays(1)),
        )
        assertConfigurations(sqrSsl2_1, configurations.getConfiguration(dcCode, skuid2, currentStartDate))
        assertEquals(
            default(dcCode, skuid2, currentEndDate),
            configurations.getConfiguration(dcCode, skuid2, currentEndDate),
        )
    }

    @Test
    fun `future sqr ssl are included from latest available date`() {
        val (_, currentEndDate) = distributionCenterConfiguration.getCurrentProductionDateRange()

        val skuid1 = UUID.randomUUID()
        val sqrSsl1_1 = insertSQRShortShelfLifeConfiguration(dcCode, currentEndDate, skuid1, ONE, TEN)
        val sqrSsl1_2 = insertSQRShortShelfLifeConfiguration(
            dcCode,
            currentEndDate.plusWeeks(2),
            skuid1,
            ONE + ONE,
            TEN + TEN,
        )

        val configurations = runBlocking {
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromCurrentWeek(
                distributionCenterConfiguration,
            )
        }

        assertConfigurations(sqrSsl1_1, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_1.date))
        assertConfigurations(sqrSsl1_2, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_2.date))
        assertConfigurations(sqrSsl1_2, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_2.date.plusWeeks(1)))
    }

    @Test
    fun `future sqr ssl are included when there is no data for current week`() {
        val (currentStartDate, currentEndDate) = distributionCenterConfiguration.getCurrentProductionDateRange()

        val skuid1 = UUID.randomUUID()
        val sqrSsl1_1 = insertSQRShortShelfLifeConfiguration(dcCode, currentStartDate.plusWeeks(1), skuid1, ONE, TEN)
        val sqrSsl1_2 = insertSQRShortShelfLifeConfiguration(
            dcCode,
            currentEndDate.plusWeeks(2),
            skuid1,
            ONE + ONE,
            TEN + TEN,
        )

        val configurations = runBlocking {
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromCurrentWeek(
                distributionCenterConfiguration,
            )
        }

        assertConfigurations(sqrSsl1_1, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_1.date))
        assertEquals(
            default(dcCode, skuid1, sqrSsl1_1.date.plusWeeks(1)),
            configurations.getConfiguration(dcCode, skuid1, sqrSsl1_1.date.plusWeeks(1)),
        )
        assertConfigurations(sqrSsl1_2, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_2.date))
        assertConfigurations(sqrSsl1_2, configurations.getConfiguration(dcCode, skuid1, sqrSsl1_2.date.plusWeeks(1)))
    }

    @Test
    fun `latest available date is properly calculated based on latest week day found`() {
        val today = LocalDate.now(UTC)
        val skuId = UUID.randomUUID()

        val dc = distributionCenterConfiguration.copy(productionStart = today.dayOfWeek)

        val expectedDateRange = dc.getProductionDateRange(today)

        val expectedStart = insertSQRShortShelfLifeConfiguration(dcCode, expectedDateRange.start, skuId, ONE, TEN)
        val latestAvailable = insertSQRShortShelfLifeConfiguration(
            dcCode,
            expectedDateRange.start.plusDays(1),
            skuId,
            ONE,
            TEN,
        )

        var configurations = runBlocking {
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromCurrentWeek(
                dc,
            )
        }

        assertConfigurations(expectedStart, configurations.getConfiguration(dcCode, skuId, expectedDateRange.start))
        assertConfigurations(latestAvailable, configurations.getConfiguration(dcCode, skuId, latestAvailable.date))
        assertEquals(
            default(dcCode, skuId, expectedDateRange.endInclusive),
            configurations.getConfiguration(dcCode, skuId, expectedDateRange.endInclusive),
        )

        val newLatestAvailable = insertSQRShortShelfLifeConfiguration(
            dcCode,
            expectedDateRange.endInclusive.minusDays(1),
            skuId,
            ONE,
            TEN,
        )

        configurations = runBlocking { sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromCurrentWeek(dc) }

        assertConfigurations(expectedStart, configurations.getConfiguration(dcCode, skuId, expectedDateRange.start))
        assertConfigurations(latestAvailable, configurations.getConfiguration(dcCode, skuId, latestAvailable.date))
        assertConfigurations(
            newLatestAvailable,
            configurations.getConfiguration(dcCode, skuId, newLatestAvailable.date),
        )
        assertEquals(
            default(dcCode, skuId, expectedDateRange.endInclusive),
            configurations.getConfiguration(dcCode, skuId, expectedDateRange.endInclusive),
        )
    }

    @Test
    fun `sqr ssl are fetched from given week`() {
        val today = LocalDate.now(distributionCenterConfiguration.zoneId)
        val futureDay = LocalDate.now(distributionCenterConfiguration.zoneId).plusWeeks(2)
        val futureDcWeek = DcWeek(futureDay, distributionCenterConfiguration.productionStart)

        val skuid1 = UUID.randomUUID()
        val sqrSslFuture = insertSQRShortShelfLifeConfiguration(dcCode, futureDay, skuid1, ONE, TEN)
        val sqrSslPast = insertSQRShortShelfLifeConfiguration(
            dcCode,
            today,
            skuid1,
            ONE + ONE,
            TEN + TEN,
        )

        val configurations = runBlocking {
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromWeek(
                futureDcWeek.value,
                distributionCenterConfiguration
            )
        }

        assertConfigurations(sqrSslFuture, configurations.getConfiguration(dcCode, skuid1, sqrSslFuture.date))
        assertConfigurations(
            sqrSslFuture,
            configurations.getConfiguration(dcCode, skuid1, sqrSslFuture.date.plusWeeks(1))
        )
        assertEquals(
            default(dcCode, skuid1, sqrSslPast.date),
            configurations.getConfiguration(dcCode, skuid1, sqrSslPast.date)
        )
    }

    private fun assertConfigurations(sqrShortShelfLifeConfRecord: SqrShortShelfLifeConfRecord, sqrShortShelfLifeConf: SQRShortShelfLifeConf) {
        assertEquals(sqrShortShelfLifeConfRecord.dcCode, sqrShortShelfLifeConf.dcCode)
        assertEquals(sqrShortShelfLifeConfRecord.skuId, sqrShortShelfLifeConf.skuId)
        assertEquals(sqrShortShelfLifeConfRecord.date, sqrShortShelfLifeConf.date)
        assertEquals(sqrShortShelfLifeConfRecord.bufferPercentage, sqrShortShelfLifeConf.bufferPercentage)
        assertEquals(sqrShortShelfLifeConfRecord.bufferAdditional, sqrShortShelfLifeConf.bufferAdditional)
    }

    @SuppressWarnings("LongParameterList")
    fun insertSQRShortShelfLifeConfiguration(
        dcCode: String,
        date: LocalDate,
        skuId: UUID,
        bufferPercentage: BigDecimal,
        bufferAdditional: BigDecimal,
    ) =
        SqrShortShelfLifeConfRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.date = date
            this.bufferPercentage = bufferPercentage
            this.bufferAdditional = bufferAdditional
        }.also {
            dsl.batchInsert(it).execute()
        }
}
