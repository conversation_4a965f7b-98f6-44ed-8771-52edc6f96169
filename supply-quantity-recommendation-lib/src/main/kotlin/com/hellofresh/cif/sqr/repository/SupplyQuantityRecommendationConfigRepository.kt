package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.sqr.SQRConfiguration
import com.hellofresh.cif.sqr.SQRConfigurations
import com.hellofresh.cif.sqrlib.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.future.asDeferred
import org.jooq.impl.DSL

class SupplyQuantityRecommendationConfigRepository(private val metricsDSLContext: MetricsDSLContext) {

    suspend fun fetchSupplyQuantityRecommendationConfigurations(distributionCenters: Set<DistributionCenterConfiguration>): SQRConfigurations {
        if (distributionCenters.isEmpty()) {
            return SQRConfigurations(emptyList())
        }

        val configurations = distributionCenters.groupBy {
            DcWeek(
                it.getLatestProductionStart(),
                it.productionStart,
            ).toString()
        }
            .map { (week, distributionCenters) ->
                fetchSupplyQuantityRecommendationConfigurationsFromWeek(week, distributionCenters)
            }.awaitAll()
            .flatten()

        return SQRConfigurations(configurations)
    }

    suspend fun fetchSupplyQuantityRecommendationConfigurations(
        week: String,
        distributionCenter: DistributionCenterConfiguration
    ): SQRConfigurations =
        SQRConfigurations(
            fetchSupplyQuantityRecommendationConfigurationsFromWeek(week, listOf(distributionCenter)).await(),
        )

    suspend fun fetchSupplyQuantityRecommendationConfigurations(
        week: String,
        distributionCenters: List<DistributionCenterConfiguration>
    ): SQRConfigurations =
        SQRConfigurations(
            fetchSupplyQuantityRecommendationConfigurationsFromWeek(week, distributionCenters).await(),
        )

    private fun fetchSupplyQuantityRecommendationConfigurationsFromWeek(
        week: String,
        distributionCenters: List<DistributionCenterConfiguration>
    ): Deferred<List<SQRConfiguration>> {
        val dcMap = distributionCenters.associateBy { it.dcCode }

        val sqrMinWeek = sqrMinFromWeekTable(dcMap.keys, week)

        return metricsDSLContext.withTagName("fetch-supply-quantity-recommendation-config")
            .select(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.RECOMMENDATION_ENABLED,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.MULTI_WEEK_ENABLED,
            )
            .from(SUPPLY_QUANTITY_RECOMMENDATION_CONF)
            .leftJoin(sqrMinWeek).on(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE.eq(
                    sqrMinWeek.field(SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE),
                ),
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID.eq(
                    sqrMinWeek.field(SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID),
                ),
            )
            .where(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE.`in`(dcMap.keys)
                    .and(
                        SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK.ge(
                            DSL.coalesce(
                                sqrMinWeek.field(SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK),
                                week,
                            ),
                        ),
                    ),
            )
            .fetchAsync()
            .thenApply { results ->
                results.mapNotNull { record ->
                    val dcCode = record[SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE]
                    dcMap[dcCode]?.let { dc ->
                        SQRConfiguration(
                            dcCode = dcCode,
                            skuId = record[SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID],
                            productionWeek = ProductionWeek(
                                record[SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK],
                                dc.productionStart,
                                dc.zoneId,
                            ),
                            recommendationEnabled = record[SUPPLY_QUANTITY_RECOMMENDATION_CONF.RECOMMENDATION_ENABLED],
                            multiWeekEnabled = record[SUPPLY_QUANTITY_RECOMMENDATION_CONF.MULTI_WEEK_ENABLED],
                        )
                    }
                }
            }.asDeferred()
    }

    private fun sqrMinFromWeekTable(dcCodes: Set<String>, requestedFromWeek: String) =
        metricsDSLContext.select(
            SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE,
            SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID,
            SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK,
        ).distinctOn(
            SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE,
            SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID,
        ).from(SUPPLY_QUANTITY_RECOMMENDATION_CONF)
            .where(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE.`in`(dcCodes)
                    .and(
                        SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK.le(requestedFromWeek),
                    ),
            ).asTable("sqr_min_from_week")
}
