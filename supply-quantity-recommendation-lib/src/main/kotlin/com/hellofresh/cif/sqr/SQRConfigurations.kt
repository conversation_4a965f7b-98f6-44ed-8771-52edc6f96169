package com.hellofresh.cif.sqr

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import java.util.UUID

class SQRConfigurations(
    sqrConfigurations: List<SQRConfiguration>,
) {

    private val dcConfigurations = sqrConfigurations
        .groupBy { it.dcCode }
        .mapValues { (_, skus) ->
            skus.groupBy { it.skuId }
                .mapValues { (_, weeks) -> weeks.associateBy { it.productionWeek } }
        }

    fun getConfiguration(dcCode: String, skuId: UUID, productionWeek: ProductionWeek): SQRConfiguration =
        dcConfigurations[dcCode]
            ?.get(skuId)
            ?.let { weekConfiguration ->
                weekConfiguration[productionWeek]
                    ?: (
                        weekConfiguration
                            .filter { (week, _) -> week < productionWeek }
                            .maxByOrNull { (week, _) -> week }
                            ?.value
                        )
            }
            ?: SQRConfiguration(
                dcCode, skuId, productionWeek,
                SQRConfiguration.DEFAULT_RECOMMENDATION_ENABLED,
                SQRConfiguration.DEFAULT_MULTI_WEEK_ENABLED,
            )

    fun hasAnyRecommendationEnabled(dcCode: String, skuId: UUID): Boolean =
        dcConfigurations[dcCode]
            ?.get(skuId)
            ?.any { (_, sqrConfiguration) -> sqrConfiguration.recommendationEnabled }
            ?: SQRConfiguration.DEFAULT_RECOMMENDATION_ENABLED

    fun getRecommendationEnabledSkus(dcCode: String): Set<UUID> =
        dcConfigurations[dcCode]
            ?.filter { (skuId) -> hasAnyRecommendationEnabled(dcCode, skuId) }
            ?.keys
            ?: emptySet<UUID>()
}

data class SQRConfiguration(
    val dcCode: String,
    val skuId: UUID,
    val productionWeek: ProductionWeek,
    val recommendationEnabled: Boolean,
    val multiWeekEnabled: Boolean
) {
    companion object {
        const val DEFAULT_RECOMMENDATION_ENABLED = false
        const val DEFAULT_MULTI_WEEK_ENABLED = false
    }
}
