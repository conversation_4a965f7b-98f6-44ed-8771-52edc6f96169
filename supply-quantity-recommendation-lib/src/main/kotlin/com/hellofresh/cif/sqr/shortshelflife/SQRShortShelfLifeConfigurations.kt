package com.hellofresh.cif.sqr.shortshelflife

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class SQRShortShelfLifeConfigurations(
    configurations: List<SQRShortShelfLifeConf>,
    distributionCenterConfigurations: Set<DistributionCenterConfiguration>
) {

    private val configurations =
        configurations.groupBy { it.dcCode to it.skuId }
            .mapValues { (_, confs) -> confs.associateBy { it.date } }

    private val dcs = distributionCenterConfigurations.associateBy { it.dcCode }

    fun getConfiguration(dcCode: String, skuId: UUID, date: LocalDate): SQRShortShelfLifeConf {
        var defaultFlag = false
        return configurations[dcCode to skuId]
            ?.let { confsByDate ->
                confsByDate[date]
                    ?: run {
                        // No exact match, check latest available week conf
                        val latestWeekMatch = getLatestWeekMatch(confsByDate.values, dcCode, date)

                        if (latestWeekMatch == null) {
                            defaultFlag =
                                getTouchlessOrderingFlagForRequestedWeek(confsByDate.values, dcCode, date) ?: false
                        }

                        latestWeekMatch
                    }
            }
            ?: default(dcCode, skuId, date, defaultFlag)
    }

    private fun getLatestWeekMatch(skuConfs: Collection<SQRShortShelfLifeConf>, dcCode: String, date: LocalDate) =
        dcs[dcCode]?.let { dc ->
            val requestedWeek = DcWeek(date, dc.productionStart).value
            // Fetch latest week same day
            skuConfs.groupBy {
                DcWeek(it.date, dc.productionStart).value
            }.filter { (week, _) -> week <= requestedWeek }
                .maxByOrNull { (week, _) -> week }
                ?.let { (_, sqrSslConfs) ->
                    sqrSslConfs.firstOrNull { it.date.dayOfWeek == date.dayOfWeek }
                }
        }

    private fun getTouchlessOrderingFlagForRequestedWeek(
        skuConfs: Collection<SQRShortShelfLifeConf>,
        dcCode: String,
        date: LocalDate
    ) =
        dcs[dcCode]?.let { dc ->
            val requestedWeek = DcWeek(date, dc.productionStart).value
            val groupedByDcWeek = skuConfs.groupBy { DcWeek(it.date, dc.productionStart).value }
            groupedByDcWeek[requestedWeek]?.first()?.touchlessOrderingEnabled
                ?: groupedByDcWeek.filter { (week, _) -> week <= requestedWeek }
                    .maxByOrNull { (week, _) -> week }?.value?.firstOrNull()?.touchlessOrderingEnabled
        }

    companion object {

        fun default(dcCode: String, skuId: UUID, date: LocalDate, touchlessOrderingEnabled: Boolean = false) =
            SQRShortShelfLifeConf(dcCode, skuId, date, BigDecimal.ZERO, BigDecimal.ZERO, touchlessOrderingEnabled)
    }
}

data class SQRShortShelfLifeConf(
    val dcCode: String,
    val skuId: UUID,
    val date: LocalDate,
    val bufferPercentage: BigDecimal,
    val bufferAdditional: BigDecimal,
    val touchlessOrderingEnabled: Boolean,
) {
    companion object
}
