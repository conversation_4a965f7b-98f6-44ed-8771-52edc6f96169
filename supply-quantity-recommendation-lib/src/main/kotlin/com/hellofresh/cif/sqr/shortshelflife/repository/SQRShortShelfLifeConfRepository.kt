package com.hellofresh.cif.sqr.shortshelflife.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConf
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConfigurations
import com.hellofresh.cif.sqrlib.schema.Tables.SQR_SHORT_SHELF_LIFE_CONF
import com.hellofresh.cif.sqrlib.schema.tables.records.SqrShortShelfLifeConfRecord
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.future.asDeferred
import kotlinx.coroutines.future.await
import org.jooq.DatePart
import org.jooq.impl.DSL
import org.jooq.impl.SQLDataType

private const val DAYS_IN_WEEK = 7L

class SQRShortShelfLifeConfRepository(private val dslContext: MetricsDSLContext) {

    suspend fun fetchSQRShortShelfLifeConfigurationFromCurrentWeek(
        distributionCenters: Set<DistributionCenterConfiguration>
    ): SQRShortShelfLifeConfigurations {
        if (distributionCenters.isEmpty()) {
            return SQRShortShelfLifeConfigurations(emptyList(), emptySet())
        }

        val configurations = distributionCenters.groupBy {
            getCurrentWeekDateRange(it)
        }
            .map { (weekDateRange, distributionCenters) ->
                fetchSQRShortShelfLifeConfigurationFromDateRangeWeek(
                    weekDateRange,
                    distributionCenters.map { it.dcCode }.toSet(),
                )
            }.awaitAll()
            .flatten()

        return SQRShortShelfLifeConfigurations(configurations, distributionCenters)
    }

    private fun getCurrentWeekDateRange(distributionCenterConfiguration: DistributionCenterConfiguration) =
        distributionCenterConfiguration.getProductionDateRange(LocalDate.now(distributionCenterConfiguration.zoneId))

    suspend fun fetchSQRShortShelfLifeConfigurationFromCurrentWeek(
        distributionCenterConfiguration: DistributionCenterConfiguration
    ) = SQRShortShelfLifeConfigurations(
        fetchSQRShortShelfLifeConfigurationFromDateRangeWeek(
            getCurrentWeekDateRange(distributionCenterConfiguration),
            setOf(distributionCenterConfiguration.dcCode),
        ).await(),
        setOf(distributionCenterConfiguration),
    )

    suspend fun fetchSQRShortShelfLifeConfigurationFromWeek(
        week: String,
        distributionCenterConfiguration: DistributionCenterConfiguration
    ) = SQRShortShelfLifeConfigurations(
        fetchSQRShortShelfLifeConfigurationFromDateRangeWeek(
            DcWeek(week).let {
                DateRange(
                    fromDate = it.getStartDateInDcWeek(
                        distributionCenterConfiguration.productionStart,
                        distributionCenterConfiguration.zoneId
                    ),
                    toDate = it.getLastDateInDcWeek(
                        distributionCenterConfiguration.productionStart,
                        distributionCenterConfiguration.zoneId
                    ),
                )
            },
            setOf(distributionCenterConfiguration.dcCode),
        ).await(),
        setOf(distributionCenterConfiguration),
    )

    fun insertShortShelfLifeConfigs(shelfLifeConfs: List<SQRShortShelfLifeConf>) =
        with(SQR_SHORT_SHELF_LIFE_CONF) {
            val queries = shelfLifeConfs.map { config ->
                dslContext.insertInto(this)
                    .columns(DATE, SKU_ID, DC_CODE, BUFFER_ADDITIONAL, BUFFER_PERCENTAGE, TOUCHLESS_ORDERING_ENABLED)
                    .values(
                        config.date,
                        config.skuId,
                        config.dcCode,
                        config.bufferAdditional,
                        config.bufferPercentage,
                        config.touchlessOrderingEnabled,
                    )
                    .onDuplicateKeyUpdate()
                    .set(BUFFER_ADDITIONAL, config.bufferAdditional)
                    .set(BUFFER_PERCENTAGE, config.bufferPercentage)
                    .set(TOUCHLESS_ORDERING_ENABLED, config.touchlessOrderingEnabled)
            }

            dslContext
                .batch(queries)
                .execute()
        }

    suspend fun fetchSQRShortShelfLifeConfigurationFromDateRange(
        skuId: UUID,
        dcCode: String,
        weekDateRange: DateRange,
    ): List<SQRShortShelfLifeConf> =
        dslContext.withTagName("fetch-short-shelf-life-conf-for-date-range")
            .select(
                SQR_SHORT_SHELF_LIFE_CONF.DC_CODE,
                SQR_SHORT_SHELF_LIFE_CONF.DATE,
                SQR_SHORT_SHELF_LIFE_CONF.SKU_ID,
                SQR_SHORT_SHELF_LIFE_CONF.BUFFER_PERCENTAGE,
                SQR_SHORT_SHELF_LIFE_CONF.BUFFER_ADDITIONAL,
                SQR_SHORT_SHELF_LIFE_CONF.TOUCHLESS_ORDERING_ENABLED,
            )
            .from(SQR_SHORT_SHELF_LIFE_CONF)
            .where(
                SQR_SHORT_SHELF_LIFE_CONF.DATE.ge(weekDateRange.fromDate)
                    .and(
                        SQR_SHORT_SHELF_LIFE_CONF.SKU_ID.eq(skuId)
                    )
                    .and(
                        SQR_SHORT_SHELF_LIFE_CONF.DC_CODE.eq(dcCode)
                    )
            )
            .fetchAsync()
            .thenApply { result ->
                result.map {
                    toSQRShortShelfLife(it.into(SqrShortShelfLifeConfRecord()))
                }
            }
            .await()

    private fun fetchSQRShortShelfLifeConfigurationFromDateRangeWeek(
        weekDateRange: DateRange,
        dcs: Set<String>
    ): Deferred<List<SQRShortShelfLifeConf>> {
        val (startDate, endDate) = weekDateRange
        val latestAvailableTable = latestAvailableConfigurationDate(dcs, endDate).asTable("latest_available_date")

        val latestAvailableDcField = latestAvailableTable.field(SQR_SHORT_SHELF_LIFE_CONF.DC_CODE)!!
        val latestAvailableSkuIdField = latestAvailableTable.field(SQR_SHORT_SHELF_LIFE_CONF.SKU_ID)!!
        val latestAvailableDateField = latestAvailableTable.field(SQR_SHORT_SHELF_LIFE_CONF.DATE)!!

        return dslContext.withTagName("fetch-sqr-short-shelf-life-config-dates")
            .select(
                SQR_SHORT_SHELF_LIFE_CONF.DC_CODE,
                SQR_SHORT_SHELF_LIFE_CONF.SKU_ID,
                SQR_SHORT_SHELF_LIFE_CONF.DATE,
                SQR_SHORT_SHELF_LIFE_CONF.BUFFER_PERCENTAGE,
                SQR_SHORT_SHELF_LIFE_CONF.BUFFER_ADDITIONAL,
                SQR_SHORT_SHELF_LIFE_CONF.TOUCHLESS_ORDERING_ENABLED,
            )
            .from(SQR_SHORT_SHELF_LIFE_CONF)
            .leftJoin(latestAvailableTable)
            .on(
                SQR_SHORT_SHELF_LIFE_CONF.DC_CODE.eq(latestAvailableDcField)
                    .and(
                        SQR_SHORT_SHELF_LIFE_CONF.SKU_ID.eq(latestAvailableSkuIdField),
                    ),
            )
            .where(SQR_SHORT_SHELF_LIFE_CONF.DC_CODE.`in`(dcs))
            .and(
                SQR_SHORT_SHELF_LIFE_CONF.DATE.greaterOrEqual(
                    DSL.case_()
                        // No Latest Day Found, use current week start Date
                        .`when`(latestAvailableDateField.isNull, startDate)
                        // Return production start date for latest week date found shifting X Days
                        .`when`(
                            // Production Start Day Monday(0), LatestAvailDate(Wednesday(2)) = LatestAvailDate - 2
                            DSL.extract(startDate, DatePart.DAY_OF_WEEK)
                                .lessOrEqual(DSL.extract(latestAvailableDateField, DatePart.DAY_OF_WEEK)),

                            DSL.localDateSub(
                                latestAvailableDateField,
                                DSL.cast(
                                    DSL.extract(latestAvailableDateField, DatePart.DAY_OF_WEEK)
                                        .minus(DSL.extract(startDate, DatePart.DAY_OF_WEEK)),
                                    SQLDataType.INTEGER,
                                ),
                            ),
                        ).else_(
                            // Production Start Day Friday(4), LatestAvailDate(Wednesday(2)) = LatestAvailDate - (2 +(7-4))
                            DSL.localDateSub(
                                latestAvailableDateField,
                                DSL.cast(
                                    DSL.extract(latestAvailableDateField, DatePart.DAY_OF_WEEK).plus(
                                        DSL.value(DAYS_IN_WEEK).minus(DSL.extract(startDate, DatePart.DAY_OF_WEEK)),
                                    ),
                                    SQLDataType.INTEGER,
                                ),
                            ),
                        ),
                ),
            ).fetchAsync()
            .thenApply { result ->
                result.map {
                    toSQRShortShelfLife(it.into(SqrShortShelfLifeConfRecord()))
                }
            }.asDeferred()
    }

    private fun latestAvailableConfigurationDate(dcs: Set<String>, endDate: LocalDate) =
        dslContext.select(
            SQR_SHORT_SHELF_LIFE_CONF.DC_CODE,
            SQR_SHORT_SHELF_LIFE_CONF.SKU_ID,
            SQR_SHORT_SHELF_LIFE_CONF.DATE,
        ).distinctOn(
            SQR_SHORT_SHELF_LIFE_CONF.DC_CODE,
            SQR_SHORT_SHELF_LIFE_CONF.SKU_ID,
        ).from(SQR_SHORT_SHELF_LIFE_CONF)
            .where(
                SQR_SHORT_SHELF_LIFE_CONF.DATE.lessOrEqual(endDate)
                    .and(SQR_SHORT_SHELF_LIFE_CONF.DC_CODE.`in`(dcs)),
            )
            .orderBy(
                SQR_SHORT_SHELF_LIFE_CONF.DC_CODE,
                SQR_SHORT_SHELF_LIFE_CONF.SKU_ID,
                SQR_SHORT_SHELF_LIFE_CONF.DATE.desc(),
            )

    private fun toSQRShortShelfLife(record: SqrShortShelfLifeConfRecord) =
        SQRShortShelfLifeConf(
            dcCode = record.dcCode,
            date = record.date,
            skuId = record.skuId,
            bufferPercentage = record.bufferPercentage,
            bufferAdditional = record.bufferAdditional,
            touchlessOrderingEnabled = record.touchlessOrderingEnabled,
        )
}
