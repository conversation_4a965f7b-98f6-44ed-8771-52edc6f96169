package com.hellofresh.cif.sqr.shortshelflife

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.math.RoundingMode

object SQRShortShelfLifeCalc {

    fun applyFormula(
        openingStock: SkuQuantity,
        consumption: SkuQuantity,
        stockUpdates: SkuQuantity?,
        bufferPercentageParam: BigDecimal,
        bufferAdditionalParam: BigDecimal,
    ): SkuQuantity {
        val zero = SkuQuantity.fromLong(0, openingStock.unitOfMeasure)
        val bufferPercentage = bufferPercentageParam
        val bufferAdditional = bufferAdditionalParam
        val sqr = SkuQuantity.max(
            zero,
            (
                consumption +
                    (consumption.multiply(bufferPercentage.movePointLeft(2))) +
                    SkuQuantity.fromBigDecimal(bufferAdditional, consumption.unitOfMeasure)
                ) - (openingStock + (stockUpdates ?: zero)),
        )
        return if (sqr.unitOfMeasure == SkuUOM.UOM_UNIT) {
            SkuQuantity.fromBigDecimal(sqr.getValue().setScale(0, RoundingMode.DOWN), sqr.unitOfMeasure)
        } else {
            sqr
        }
    }
}
