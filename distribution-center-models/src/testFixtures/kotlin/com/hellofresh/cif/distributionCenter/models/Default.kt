package com.hellofresh.cif.distributionCenter.models

import java.time.DayOfWeek.FRIDAY
import java.time.ZoneId

val EuBerlinZoneId: ZoneId = ZoneId.of("Europe/Berlin")

fun DistributionCenterConfiguration.Companion.default(dcCode: String = "VE") = DistributionCenterConfiguration(
    dcCode,
    FRIDAY,
    FRIDAY,
    "DACH",
    EuBerlinZoneId,
    enabled = true,
    wmsType = WmsSystem.WMS_SYSTEM_FCMS,
    brands = emptyList()
)
