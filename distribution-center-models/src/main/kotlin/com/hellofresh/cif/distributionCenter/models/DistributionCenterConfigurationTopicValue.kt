package com.hellofresh.cif.distributionCenter.models

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.time.DayOfWeek
import java.time.ZoneId

@JsonNaming(SnakeCaseStrategy::class)
data class DistributionCenterConfigurationTopicValue(
    val productionStart: DayOfWeek,
    val cleardown: DayOfWeek?,
    val market: String,
    val zoneId: ZoneId,
    val globalDc: String?,
    val enabled: Boolean,
    val hasCleardown: Boolean,
    @JsonInclude(JsonInclude.Include.NON_NULL)
    val scheduledClearDownTime: String? = null,
    val wmsType: WmsSystem,
    @JsonInclude(JsonInclude.Include.NON_NULL)
    val poCutoffTime: String,
    val brands: List<String> = emptyList(),
)
