plugins {
    id("com.hellofresh.cif.common-conventions")
    `test-functional`
    alias(libs.plugins.jooq)
}

description = "Distribution center management lib"
group = "$group.distribution-center-lib"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "dc_config"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(projects.distributionCenterModels)
    implementation(projects.lib)
    implementation(projects.lib.db)

    testImplementation(libs.flyway.core)
    testImplementation(libs.mockk)
    testImplementation(projects.libTests)
    testImplementation(testFixtures(projects.distributionCenterModels))
}
