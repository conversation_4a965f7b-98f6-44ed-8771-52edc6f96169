package com.hellofresh.cif.distributionCenterLib.repo

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distribution_center_lib.schema.Tables
import com.hellofresh.cif.distribution_center_lib.schema.tables.records.DcConfigRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.valueOf
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration

class DcRepositoryImplTest {
    private val dcConfigRepo = DcRepositoryImpl(dsl)

    @BeforeTest
    fun cleanupDb() {
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
    }

    @Test
    fun `should fetch all DC Configurations`() {
        assertEquals(0, runBlocking { dcConfigRepo.fetchDcConfigurations().size })
        // given
        val expected = (0..3).map {
            DcConfigRecord().apply {
                dcCode = UUID.randomUUID().toString()
                cleardown = MONDAY.name
                productionStart = FRIDAY.name
                market = UUID.randomUUID().toString()
                zoneId = ZoneOffset.UTC.id
                hasCleardown = true
                enabled = true
                createdAt = LocalDateTime.now(ZoneOffset.UTC)
                updatedAt = LocalDateTime.now(ZoneOffset.UTC)
                recordTimestamp_ = LocalDateTime.now(ZoneOffset.UTC)
                poCutoffTime = LocalTime.now(ZoneOffset.UTC).withNano(0)
            }
        }
        dsl.batchInsert(expected).execute()
        // when
        val actual = runBlocking { dcConfigRepo.fetchDcConfigurations() }

        // then
        assertEquals(
            expected.associateBy { it.dcCode }
                .mapValues {
                    with(it.value) {
                        DistributionCenterConfiguration(
                            dcCode,
                            valueOf(productionStart),
                            valueOf(cleardown),
                            market,
                            ZoneId.of(zoneId),
                            enabled,
                            hasCleardown,
                            wmsType = WmsSystem.UNRECOGNIZED,
                            poCutoffTime,
                            brands = emptyList(),
                        )
                    }
                },
            actual.associateBy { it.dcCode },
        )
    }

    companion object {
        private val dataSource = getMigratedDataSource()
        private val dbConfiguration = DefaultConfiguration()
            .apply {
                setSQLDialect(SQLDialect.POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
        private val dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
    }
}
