import com.google.cloud.tools.jib.gradle.JibTask

plugins {
    id("com.hellofresh.cif.application-conventions")
    hellofresh.`test-integration`
}

description = "Sinks inventory forecast produced by the calculator to a database"
group = "$group.forecastapidb"

tasks.withType<JibTask>().configureEach {
    enabled = false
}

dependencies {
    testIntegrationRuntimeOnly(libs.postgresql.driver)
    testIntegrationRuntimeOnly(libs.slf4j.simple)

    testIntegrationImplementation(projects.libTests)
    testIntegrationImplementation(libs.jackson.kotlin)
    testIntegrationImplementation(libs.jackson.databind)
    testIntegrationImplementation(libs.jackson.jsr310)
    testIntegrationImplementation(libs.awaitility)
    testIntegrationImplementation(libs.flyway.core)
    testIntegrationImplementation(libs.kafka.clients)
    testIntegrationImplementation(libs.testcontainers.core)
    testIntegrationImplementation(libs.testcontainers.kafka)
    testIntegrationImplementation(libs.testcontainers.postgresql)
    testIntegrationImplementation(projects.calculatorModels)
    testIntegrationImplementation(testFixtures(projects.calculatorModels))

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
