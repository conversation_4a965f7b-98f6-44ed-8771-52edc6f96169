package com.hellofresh.cif.demand

import com.hellofresh.cif.models.DateRange
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.Demand
import java.util.UUID

interface DemandRepository {
    suspend fun findDemands(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID? = null): List<Demand>
    suspend fun findDemandConsumptionDetails(
        dcCodes: Set<String>,
        dateRange: DateRange,
        skuId: UUID? = null
    ): Map<com.hellofresh.demand.models.DemandKey, ConsumptionDetails>
}
