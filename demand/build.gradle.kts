plugins {
    id("com.hellofresh.cif.application-conventions")
    `test-functional`
    alias(libs.plugins.jooq)
    alias(libs.plugins.gradle.docker.compose)
}

description = "Demand application"
group = "$group.demand"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "demand|subbed_type|us_demand|uom|actual_consumption"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }
                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.demandModels)
    implementation(projects.lib)
    implementation(projects.lib.db)
    implementation(projects.distributionCenterLib)

    implementation(projects.dateUtilModels)
    implementation(projects.skuSpecificationLib)
    implementation(libs.kafka.kafka213)

    implementation(projects.lib.fileConsumer)
    implementation(projects.lib.sqs)
    implementation(projects.lib.s3)
    implementation(projects.skuInputsLib)
    implementation(libs.protobuf.grpc)

    testFunctionalImplementation(libs.hellofresh.schemaregistry)
    testFunctionalImplementation(projects.lib)
    testFunctionalImplementation(libs.flyway.core)
    testFunctionalImplementation(libs.jooq.core)
    testFunctionalImplementation(libs.testcontainers.core)
    testFunctionalImplementation(libs.testcontainers.postgresql)
    testFunctionalImplementation(libs.testcontainers.junit)
    testFunctionalImplementation(projects.libTests)
    testFunctionalImplementation(libs.hikaricp)
    testFunctionalImplementation(libs.postgresql.driver)
    testFunctionalImplementation(libs.coroutines.jdk8)
    testFunctionalImplementation(libs.kafka.kafka213)
    testFunctionalImplementation(libs.mockk)
}
