package com.hellofresh.cif.demand.deserializer

import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.assertThrows

class ProtobufDeserializerTest {
    @Test fun `SkuDemandForecastKey and SkuDemandForecastValue are deserialized`() {
        val keySerde = SkuDemandForecastKeySerde()
        val valSerde = SkuDemandForecastValSerde()
        val recordKey = SkuDemandForecastKey.newBuilder().build()
        val recordValue = SkuDemandForecastVal.newBuilder().build()
        val key = keySerde.serializer().serialize("", recordKey)
        val value = valSerde.serializer().serialize("", recordValue)
        assertEquals(recordKey, keySerde.deserializer().deserialize("", key))
        assertEquals(recordValue, valSerde.deserializer().deserialize("", value))
    }

    @Test fun `SkuDemandForecastKey can not be null`() {
        assertThrows<IllegalStateException> {
            SkuDemandForecastKeySerde().deserializer().deserialize("", null)
        }
    }

    @Test fun `null SkuDemandForecastVal is deserialized as null`() {
        assertNull(SkuDemandForecastValSerde().deserializer().deserialize("", null))
    }
}
