package com.hellofresh.cif.demand

import com.google.type.Decimal
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.CrossDocking
import com.hellofresh.demand.models.CrossDockingAction
import com.hellofresh.demand.models.DemandTypeMapper
import com.hellofresh.demand.models.RecipeBreakdown
import com.hellofresh.demand.models.UNKNOWN_BRAND
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_REPLACE
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_UNSPECIFIED
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockings
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.DemandType.DEMAND_TYPE_FUMIGATED
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Prekitting
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Prekittings
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.RecipesBreakdown
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitution
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitutions
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertContentEquals
import kotlin.test.assertEquals

class ConsumptionDetailsTest {

    @Test fun `factory method correctly creates the object`() {
        val prekittingOut = listOf(1, 2)
        val prekittingIn = listOf(2, 3)
        val subInQty = 20
        val subOutQty = 50
        val crossDockingIn = CrossDocking(UUID.randomUUID().toString(), CrossDockingAction.REPLACE, 76)
        val crossDockingOut = CrossDocking(UUID.randomUUID().toString(), CrossDockingAction.UNKNOWN, 35)

        val value = SkuDemandForecastVal.newBuilder().apply {
            addRecipesBreakdown(
                RecipesBreakdown.newBuilder().apply {
                    brand = UNKNOWN_BRAND
                    recipeIndex = "1"
                    qty = Decimal.newBuilder().setValue("30").build()
                    demandType = DEMAND_TYPE_FUMIGATED
                },
            )

            addRecipesBreakdown(
                RecipesBreakdown.newBuilder().apply {
                    brand = "GC"
                    recipeIndex = "1"
                    qty = Decimal.newBuilder().setValue("30").build()
                    demandType = DEMAND_TYPE_FUMIGATED
                },
            )
            addRecipesBreakdown(
                RecipesBreakdown.newBuilder().apply {
                    brand = "HF"
                    recipeIndex = "7"
                    qty = Decimal.newBuilder().setValue("40").build()
                    demandType = DEMAND_TYPE_FUMIGATED
                },
            )

            preKittings = Prekittings.newBuilder().apply {
                prekittingIn.forEach {
                    addPrekittingIn(
                        Prekitting.newBuilder().setQty(Decimal.newBuilder().setValue(it.toString())),
                    )
                }

                prekittingOut.forEach {
                    addPrekittingOut(
                        Prekitting.newBuilder().setQty(Decimal.newBuilder().setValue(it.toString())),
                    )
                }
            }.build()

            substitutions = Substitutions.newBuilder().apply {
                addSubIn(
                    Substitution.newBuilder().apply {
                        brand = UNKNOWN_BRAND
                        recipeIndex = "1"
                        qty = Decimal.newBuilder().setValue(subInQty.toString()).build()
                        demandType = DEMAND_TYPE_FUMIGATED
                    },
                )

                addSubOut(
                    Substitution.newBuilder().apply {
                        brand = UNKNOWN_BRAND
                        recipeIndex = "2"
                        qty = Decimal.newBuilder().setValue(subOutQty.toString()).build()
                        demandType = DEMAND_TYPE_FUMIGATED
                    },
                )
            }.build()

            crossDockings = CrossDockings.newBuilder().apply {
                addCrossdockingIn(
                    SkuDemandForecastVal.CrossDocking.newBuilder().apply {
                        distributionCenterCode = crossDockingIn.dcCode
                        action = CROSS_DOCKING_ACTION_REPLACE
                        qty = Decimal.newBuilder().setValue(crossDockingIn.quantity.toString()).build()
                    },
                )

                addCrossdockingOut(
                    SkuDemandForecastVal.CrossDocking.newBuilder().apply {
                        distributionCenterCode = crossDockingOut.dcCode
                        action = CROSS_DOCKING_ACTION_UNSPECIFIED
                        qty = Decimal.newBuilder().setValue(crossDockingOut.quantity.toString()).build()
                    },
                )
            }.build()
        }.build()

        assertConsumptionDetailsDemand(
            value,
            prekittingOut,
            prekittingIn,
            subOutQty,
            subInQty,
            crossDockingIn,
            crossDockingOut,
        )
    }

    @Test fun `factory method correctly creates empty details`() {
        val value = SkuDemandForecastVal.newBuilder().build()

        assertEquals(ConsumptionDetails.empty, value.consumptionDetails())
    }

    private fun assertConsumptionDetailsDemand(
        value: SkuDemandForecastVal,
        prekittingOut: List<Int>,
        prekittingIn: List<Int>,
        subOutQty: Int,
        subInQty: Int,
        crossDockingIn: CrossDocking,
        crossDockingOut: CrossDocking
    ) {
        with(value.consumptionDetails()) {
            assertEquals(
                listOf(
                    RecipeBreakdown("GC", "1", 30, DemandTypeMapper.mapDemandType(DEMAND_TYPE_FUMIGATED)),
                    RecipeBreakdown("HF", "7", 40, DemandTypeMapper.mapDemandType(DEMAND_TYPE_FUMIGATED)),
                    RecipeBreakdown(UNKNOWN_BRAND, "1", 30, DemandTypeMapper.mapDemandType(DEMAND_TYPE_FUMIGATED)),
                ).toSet(),
                this.recipeBreakdowns.toSet(),
            )

            assertContentEquals(
                prekittingOut,
                this.prekitting?.out?.map { it.qty.toInt() },
            )
            assertContentEquals(
                prekittingIn,
                this.prekitting?.`in`?.map { it.qty.toInt() },
            )

            assertEquals(
                com.hellofresh.demand.models.Substitution(
                    UNKNOWN_BRAND,
                    "2",
                    subOutQty.toLong(),
                    DemandTypeMapper.mapDemandType(DEMAND_TYPE_FUMIGATED),
                ),
                this.substitutions?.out?.first(),
            )

            assertEquals(
                com.hellofresh.demand.models.Substitution(
                    UNKNOWN_BRAND,
                    "1",
                    subInQty.toLong(),
                    DemandTypeMapper.mapDemandType(DEMAND_TYPE_FUMIGATED),
                ),
                this.substitutions?.`in`?.first(),
            )
            assertEquals(
                crossDockingIn,
                this.crossDockings?.`in`?.first(),
            )
            assertEquals(
                crossDockingOut,
                this.crossDockings?.out?.first(),
            )
        }
    }
}
