{{- range $key, $value := .Values.statefulsets }}
{{- if or ($value.minAvailable) ($value.maxUnavailable) }}
{{- if lt $value.minAvailable $value.replicaCount }}
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ template "cif-supply-quantity-recommendation-job.fullname" $ }}-{{ $key }}
  labels:
    {{- include "cif-supply-quantity-recommendation-job.labels" $ | nindent 4 }}
    app: {{ template "cif-supply-quantity-recommendation-job.name" $ }}
spec:
  {{- with $value.minAvailable }}
  minAvailable: {{ . }}
  {{- end }}
  {{- with $value.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "cif-supply-quantity-recommendation-job.selectorLabels" $ | nindent 6 }}
      app: {{ template "cif-supply-quantity-recommendation-job.name" $ }}-{{ $key }}
{{- end }}
{{- end }}
{{- end }}
