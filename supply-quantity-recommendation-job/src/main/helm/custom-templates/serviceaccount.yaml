{{- range $key, $value := .Values.statefulsets }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "cif-supply-quantity-recommendation-job.fullname" $ }}-{{ $key }}
  labels:
    {{- include "cif-supply-quantity-recommendation-job.labels" $ | nindent 4 }}
    app: {{ template "cif-supply-quantity-recommendation-job.name" $ }}-{{ $key }}
  {{- with $.Values.serviceAccountAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}

