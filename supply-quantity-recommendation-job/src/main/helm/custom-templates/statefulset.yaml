{{- range $key, $value := .Values.statefulsets }}
{{ $version := coalesce $value.tag $.Values.tag $.Chart.AppVersion | trunc 63 | trimSuffix "-" }}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "cif-supply-quantity-recommendation-job.fullname" $ }}-{{ $key }}
  labels:
    {{- include "cif-supply-quantity-recommendation-job.labels" $ | nindent 4 }}
    app: {{ template "cif-supply-quantity-recommendation-job.name" $ }}-{{ $key }}
    version: {{ $version | squote }}
    {{- if $value.spotInstance }}
    {{- if $value.spotInstance.preferred }}
    spot: "true"
    {{- end }}
    {{- end }}
spec:
  replicas: {{ $value.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "cif-supply-quantity-recommendation-job.name" $ }}-{{ $key }}
      {{- include "cif-supply-quantity-recommendation-job.selectorLabels" $ | nindent 6 }}
  serviceName: {{ template "<CHARTNAME>.fullname" $ }}-k8s
  template:
    metadata:
      labels:
        {{- include "cif-supply-quantity-recommendation-job.labels" $ | nindent 8 }}
        app: {{ template "cif-supply-quantity-recommendation-job.name" $ }}-{{ $key }}
        version: {{ $version | squote }}
        {{- if $value.spotInstance }}
        {{- if $value.spotInstance.preferred }}
        spot: "true"
        {{- end }}
        {{- end }}
      annotations:
        {{- include "<CHARTNAME>.vaultAnnotations" $ | nindent 8 }}
      {{- if or ($.Values.podAnnotations) ($value.podAnnotations) }}
        {{- with $.Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with $value.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
    spec:
      serviceAccountName: {{ if $value.serviceAccountName }}{{ $value.serviceAccountName }}{{ else }}{{ template "cif-supply-quantity-recommendation-job.fullname" $ }}-{{ $key }}{{ end }}
      dnsConfig:
        options:
          - name: ndots
            value: "1"
      {{- with $value.podSecurityContext }}
      securityContext: {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name:  {{ $.Chart.Name }}-{{ $key }}
          image: "{{ $value.repository }}:{{ coalesce $value.tag $.Values.tag $.Chart.AppVersion }}"
          imagePullPolicy: {{ $value.pullPolicy }}
          {{- if $value.command }}
          command: {{ $value.command }}
          {{- end }}
          {{- if $value.args }}
          args: {{- toYaml $value.args | nindent 12 }}
          {{- end }}
          env:
          - name: HF_REPLICA_COUNT
            value: {{ quote $value.replicaCount }}
          - name: HF_POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: JAEGER_AGENT_HOST
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: OTEL_EXPORTER_JAEGER_AGENT_HOST
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          - name: OTEL_EXPORTER_JAEGER_AGENT_PORT
            value: "6831"
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          {{- if $.Chart.AppVersion }}
          - name: VERSION
            value: "{{ $.Chart.AppVersion }}"
          {{- end }}
          {{- range $envName, $envValue := $value.env }}
          - name: {{ $envName }}
            {{- if eq (printf "%T" $envValue) "map[string]interface {}" }}
            valueFrom:
              {{ $envValue.type }}:
                {{- if $envValue.prefixFullName }}
                name: {{ template "cif-supply-quantity-recommendation-job.fullname" $ }}{{ $envValue.name }}
                {{- else }}
                name: {{ $envValue.name }}
                {{- end }}
                key: {{ $envValue.key }}
            {{- else }}
            value: {{ $envValue | quote }}
            {{- end }}
          {{- end }}
          {{- if or ($.Values.configMap) ($.Values.secrets) }}
          envFrom:
          {{- if $.Values.configMap }}
            - configMapRef:
                name: {{ template "cif-supply-quantity-recommendation-job.fullname" $ }}
          {{- end }}
          {{- if $.Values.secrets }}
            - secretRef:
                name: {{ template "cif-supply-quantity-recommendation-job.fullname" $ }}
          {{- end }}
          {{- end }}
          {{- with $value.volumeMounts }}
          volumeMounts: {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
          {{- range $label, $port := $value.containerPorts }}
            - name: {{ $label }}
              containerPort: {{ $port }}
              protocol: TCP
          {{- end }}
          {{- with $value.startupProbe }}
          startupProbe: {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $value.livenessProbe }}
          livenessProbe: {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $value.readinessProbe }}
          readinessProbe: {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with $value.securityContext }}
          securityContext: {{- toYaml . | nindent 12 }}
          {{- end }}
          resources: {{- toYaml $value.resources | nindent 12 }}
    {{- if $value.volumeMounts }}
      volumes:
      {{- range $volumeKey, $volumeValue := $value.volumeMounts }}
      - name: {{ $volumeValue.name }}
        configMap:
          name: {{ template "cif-supply-quantity-recommendation-job.fullname" $ }}-v
      {{- end }}
    {{- end }}
    {{- with $value.nodeSelector }}
      nodeSelector: {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- if $value.affinity }}
      {{- with $value.affinity }}
      affinity: {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- else }}
      {{- if $value.spotInstance }}
        {{- if $value.spotInstance.preferred }}
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: eks.amazonaws.com/capacityType
                operator: In
                values:
                - SPOT
        {{- end }}
      {{- end }}
    {{- end }}
    {{- if $value.tolerations }}
      {{- with $value.tolerations }}
      tolerations: {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- else }}
      {{- if $value.spotInstance }}
        {{- if $value.spotInstance.preferred }}
      tolerations:
      - key: "spotInstance"
        operator: "Exists"
        {{- end}}
      {{- end}}
    {{- end }}
{{- end }}
