package com.hellofresh.cif.sqr

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.cif.sqr.service.BNLBufferCalculationService
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.cif.sqs.SQSClientBuilder
import com.hellofresh.cif.sqs.SQSListener
import com.hellofresh.cif.sqs.SQSMessageProxy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import software.amazon.awssdk.services.sqs.SqsClient

object BNLBufferCalculationSQSListener {
    private val sqsClient: SqsClient =
        SQSClientBuilder.getSqsClient(getAssumeRoleArn(), "bnl-buffer-calculation-session")

    private fun getAssumeRoleArn() = ConfigurationLoader.getStringOrFail("assume.role.arn")
    private fun getBNLBufferCalculationSqsUrl() = ConfigurationLoader.getStringOrFail("aws.sqs.url")

    fun launch(
        meterRegistry: HelloFreshMeterRegistry,
        readMetricsDSLContext: MetricsDSLContext,
        readWriteMetricsDSLContext: MetricsDSLContext,
    ) {
        val s3Importer = S3Importer(getAssumeRoleArn(), "bnl-buffer-session")
        val s3EventMessageParser = S3EventMessageParser()
        val shortShelfLifeConfRepository = SQRShortShelfLifeConfRepository(readWriteMetricsDSLContext)
        val dcRepository = DcRepositoryImpl(readMetricsDSLContext)
        val dcConfigService = DcConfigService(meterRegistry, dcRepository)
        val skuInputDataRepository = SkuInputDataRepositoryImpl(readMetricsDSLContext, dcConfigService)
        val skuInputService = SkuInputService(skuInputDataRepository)

        val bnlBufferCalculationService =
            BNLBufferCalculationService(
                s3Importer,
                shortShelfLifeConfRepository,
                skuInputService,
                readMetricsDSLContext
            )

        val bnlBufferCalculationSqsService = SQSMessageProxy(
            bnlBufferCalculationService,
            s3EventMessageParser,
        )

        val bnlBufferCalculationSqsListener = shutdownNeeded {
            SQSListener(
                bnlBufferCalculationSqsService,
                sqsClient,
                getBNLBufferCalculationSqsUrl(),
            )
        }

        CoroutineScope(Dispatchers.IO).launch {
            bnlBufferCalculationSqsListener.run()
        }
    }
}
