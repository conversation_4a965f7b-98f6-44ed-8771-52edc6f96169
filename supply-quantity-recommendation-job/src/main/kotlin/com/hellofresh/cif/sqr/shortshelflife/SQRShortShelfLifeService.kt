package com.hellofresh.cif.sqr.shortshelflife

import com.google.type.Decimal
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.lib.kafka.serde.serializer
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.DailyCalculation
import com.hellofresh.cif.sqr.shortshelflife.repository.DailyCalculationsRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.PoData
import com.hellofresh.cif.sqr.shortshelflife.repository.PoDetails
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLife
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeRepository.upsertSqrShortShelfLifeRecommendations
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.UOM
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal.PONumber
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal.ProductionWeek
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal.PurchaseOrderData
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal.Quantity
import java.math.BigDecimal
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.VisibleForTesting

const val SSL_SQR_TOPIC_NAME = "public.ordering.supply-quantity-recommendation-daily.v1"
const val BATCH_SIZE = 5000

@Suppress("LongParameterList", "TooManyFunctions")
class SQRShortShelfLifeService(
    private val sqrShortShelfLifeParams: SqrShortShelfLifeParams,
    private val readWriteDslContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
    private val dailyCalculationsRepository: DailyCalculationsRepository,
    private val sqrShortShelfLifeConfRepository: SQRShortShelfLifeConfRepository,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val producer: Producer<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal>,
) {

    suspend fun run(dcCodes: Set<String>) {
        val distributionCenters = dcCodes.mapNotNull {
            dcConfigService.dcConfigurations[it]
                ?: run {
                    logger.warn("No DC configuration found for $it")
                    null
                }
        }.filter { sqrShortShelfLifeParams.marketParam.contains(it.market) }.toSet()

        if (distributionCenters.isNotEmpty()) {
            val dcMap = distributionCenters.associateBy { it.dcCode }
            val configurations = sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromCurrentWeek(
                distributionCenters,
            )
            val updatedRecommendationsCount = dailyCalculationsRepository.fetchDailyCalculations(distributionCenters)
                .map { dailyCalculations ->
                    val recommendations = prepareSupplyQuantityRecommendations(dailyCalculations, dcMap, configurations)
                    val recommendationsWithPoDetails = if (recommendations.isNotEmpty()) {
                        fillSQRShortShelfLifeWithPODetails(recommendations)
                    } else {
                        emptyList()
                    }

                    if (recommendationsWithPoDetails.isNotEmpty()) {
                        logger.info(
                            "SQR recommendations With Po Details prepared, upserting the recommendations" +
                                " count = ${recommendationsWithPoDetails.size}",
                        )
                        saveAndSend(recommendationsWithPoDetails)
                    }
                }.toList().count()
            logger.info("Updated $updatedRecommendationsCount short shelf life recommendations")
        } else {
            logger.info(
                "There are no configured dcs for processing short shelf life recommendations, " +
                    "input dc codes = $dcCodes",
            )
        }
    }

    private fun prepareSupplyQuantityRecommendations(
        dailyCalculations: List<DailyCalculation>,
        dcMap: Map<String, DistributionCenterConfiguration>,
        configurations: SQRShortShelfLifeConfigurations
    ) = dailyCalculations
        .groupBy { it.productionWeek }
        .flatMap { (_, dailyCalculationsByWeek) ->
            val isDemandExistForTheEntireWeek = dailyCalculationsByWeek.any { it.consumption.getValue() != BigDecimal.ZERO }
            dailyCalculationsByWeek
                .filter { isValid(it, dcMap) }
                .map { calculation ->
                    val configuration = configurations.getConfiguration(
                        calculation.dcCode,
                        calculation.skuId,
                        calculation.date,
                    )

                    val sqrValue = if (isDemandExistForTheEntireWeek) {
                        SQRShortShelfLifeCalc.applyFormula(
                            calculation.openingStock,
                            calculation.consumption,
                            calculation.stockUpdate,
                            configuration.bufferPercentage,
                            configuration.bufferAdditional,
                        )
                    } else {
                        SkuQuantity.fromBigDecimal(BigDecimal.ZERO, calculation.openingStock.unitOfMeasure)
                    }

                    SQRShortShelfLife(
                        dcCode = calculation.dcCode,
                        date = calculation.date,
                        skuId = calculation.skuId,
                        openingStock = calculation.openingStock,
                        unusable = calculation.unusable,
                        consumption = calculation.consumption,
                        stockUpdate = calculation.stockUpdate,
                        bufferPercentage = configuration.bufferPercentage,
                        bufferAdditional = configuration.bufferAdditional,
                        sqr = sqrValue,
                        touchlessOrderingEnabled = configuration.touchlessOrderingEnabled,
                        productionWeek = calculation.productionWeek,
                        poData = PoData(
                            poDetails = emptyList(),
                            inboundDetails = emptyList(),
                        ),
                    )
                }
        }

    private fun createRecord(
        supplyQuantityRecommendation: SQRShortShelfLife
    ) =
        with(supplyQuantityRecommendation) {
            val dcWeek = DcWeek(productionWeek)
            val date = com.google.type.Date.newBuilder()
                .setYear(date.year)
                .setMonth(date.monthValue)
                .setDay(date.dayOfMonth)
                .build()
            val key = SupplyQuantityRecommendationDailyKey.newBuilder()
                .setDcCode(dcCode)
                .setSkuId(skuId.toString())
                .setDate(
                    date,
                )

            val purchaseOrdersValue = getPurchaseOrdersValue(supplyQuantityRecommendation)
            val inboundsValue = getInboundsValue(supplyQuantityRecommendation)

            val value = SupplyQuantityRecommendationDailyVal.newBuilder()
                .setDcCode(key.dcCode)
                .setSkuId(key.skuId)
                .setDate(date)
                .setSupplyQuantityRecommendation(
                    Quantity.newBuilder().setQty(supplyQuantityRecommendation.sqr.getValue().toProtoDecimal())
                        .build(),
                )
                .setInventoryRollover(
                    Quantity.newBuilder().setQty(
                        supplyQuantityRecommendation.openingStock.getValue().toProtoDecimal(),
                    ).build(),
                )
                .setForecastedDemanded(
                    Quantity.newBuilder().setQty(
                        supplyQuantityRecommendation.consumption.getValue().toProtoDecimal(),
                    ).build(),
                )
                .setBufferDetails(
                    Quantity.newBuilder().setQty(
                        (
                            supplyQuantityRecommendation.bufferAdditional +
                                (
                                    supplyQuantityRecommendation.bufferPercentage * supplyQuantityRecommendation.consumption.getValue()
                                    ).movePointLeft(2)
                            ).toProtoDecimal(),
                    ).build(),
                )
                .setPurchaseOrder(purchaseOrdersValue)
                .setInbounds(inboundsValue)
                .setTouchlessOrderingEnabled(supplyQuantityRecommendation.touchlessOrderingEnabled)
                .setUnit(mapToUom(uom))
                .setProductionWeek(
                    ProductionWeek.newBuilder()
                        .setWeek(dcWeek.week)
                        .setYear(dcWeek.year),
                )

            ProducerRecord(SSL_SQR_TOPIC_NAME, null, key.build(), value.build())
        }

    private fun getInboundsValue(supplyQuantityRecommendation: SQRShortShelfLife): PurchaseOrderData.Builder =
        PurchaseOrderData.newBuilder()
            .apply {
                addAllPoNumbers(
                    supplyQuantityRecommendation.poData.inboundDetails.map { poDetail ->
                        PONumber.newBuilder()
                            .setPoNumber(poDetail.poNumber)
                            .setQty(poDetail.qty.toProtoDecimal())
                            .build()
                    },
                )
            }

    private fun getPurchaseOrdersValue(supplyQuantityRecommendation: SQRShortShelfLife): PurchaseOrderData.Builder =
        PurchaseOrderData.newBuilder()
            .apply {
                addAllPoNumbers(
                    supplyQuantityRecommendation.poData.poDetails.map { poDetail ->
                        PONumber.newBuilder()
                            .setPoNumber(poDetail.poNumber)
                            .setQty(poDetail.qty.toProtoDecimal())
                            .build()
                    },
                )
            }

    @VisibleForTesting
    fun isValid(dailyCalculation: DailyCalculation, dcs: Map<String, DistributionCenterConfiguration>) =
        dcs[dailyCalculation.dcCode]?.let { dc ->
            sqrShortShelfLifeParams.marketParam[dc.market]
                ?.let { dcParam -> isValidCategory(dailyCalculation.skuCategory, dcParam) }
        } == true

    private fun isValidCategory(skuCategory: String, sqrShortShelfLifeDcParam: SqrShortShelfLifeDcParam) =
        (sqrShortShelfLifeDcParam.skuCategoriesIncluded.isEmpty() || sqrShortShelfLifeDcParam.skuCategoriesIncluded.contains(skuCategory)) &&
            (sqrShortShelfLifeDcParam.skuCategoriesExcluded.isEmpty() || !sqrShortShelfLifeDcParam.skuCategoriesExcluded.contains(skuCategory))

    @VisibleForTesting
    suspend fun fillSQRShortShelfLifeWithPODetails(
        sqrShortShelfLifes: List<SQRShortShelfLife>,
    ): List<SQRShortShelfLife> {
        logger.info("filling sqr short shelf life recommendations with po details, ${sqrShortShelfLifes.size}")

        val purchaseOrders = getPurchaseOrders(sqrShortShelfLifes)
        val inbounds = PurchaseOrderInbounds(purchaseOrders)

        return sqrShortShelfLifes.map { sqrShortShelfLife ->
            val dcConfig = dcConfigService.dcConfigurations[sqrShortShelfLife.dcCode]
            require(dcConfig != null) {
                "DC not found ${sqrShortShelfLife.dcCode}"
            }
            val dcPoCutoffTime = dcConfig.poCutoffTime
            val inbound = inbounds.inbounds(sqrShortShelfLife.skuId, dcConfig.dcCode, sqrShortShelfLife.date)
            val posCutOff = dcPoCutoffTime?.let { inbounds.purchaseOrdersCutOff(inbound.pos, it) }
                ?: emptyList()
            val posCutOffQty = inbounds.purchaseOrdersCutOffQuantity(posCutOff)

            val poDetails = if (dcPoCutoffTime != null) {
                posCutOff
            } else {
                inbound.pos
            }

            sqrShortShelfLife.copy(
                sqr = if (sqrShortShelfLife.sqr.unitOfMeasure == posCutOffQty.unitOfMeasure) {
                    SkuQuantity.max(SkuQuantity.ZERO, sqrShortShelfLife.sqr.minus(posCutOffQty))
                } else {
                    sqrShortShelfLife.sqr
                },
                poData = PoData(
                    poDetails = poDetails.map {
                        PoDetails(
                            it.poQuantity?.getValue() ?: BigDecimal.ZERO,
                            it.expectedPoNumber ?: "",
                        )
                    },
                    inboundDetails = poDetails.flatMap { po ->
                        po.deliveries.map { PoDetails(it.actualQuantity.getValue(), it.sourcePo.number) }
                    },
                ),
            )
        }
    }

    private suspend fun getPurchaseOrders(
        sqrShortShelfLifeList: List<SQRShortShelfLife>
    ): List<PurchaseOrder> {
        if (sqrShortShelfLifeList.isEmpty()) return emptyList()

        val minDate = sqrShortShelfLifeList.minOf { it.date }
        val maxDate = sqrShortShelfLifeList.maxOf { it.date }

        val dateRange = DateRange(minDate, maxDate)

        val dcCodes = sqrShortShelfLifeList.map { it.dcCode }.toSet()

        return purchaseOrderRepository.findPurchaseOrders(dcCodes, dateRange)
    }

    private suspend fun saveAndSend(
        sqrShortShelfLifes: List<SQRShortShelfLife>,
    ): List<SQRShortShelfLife> {
        logger.info("Saving batch ${sqrShortShelfLifes.size} sqr short shelf life recommendations")
        val updatedSQRShortShelfLifes = readWriteDslContext.withTagName(
            "save-all-short-shelf-life-recommendations",
        )
            .transactionResultAsync { conf ->
                val txDsl = readWriteDslContext.withMeteredConfiguration(conf)
                val recommendationsWithPoDetails = sqrShortShelfLifes
                    .chunked(BATCH_SIZE)
                    .flatMap { batch -> txDsl.upsertSqrShortShelfLifeRecommendations(batch) }
                recommendationsWithPoDetails.forEach {
                    logger.info(
                        "starting to publish dcCode = ${it.dcCode}, skuId = ${it.skuId}, date = ${it.date}," +
                            "production week = ${it.productionWeek}",
                    )
                    producer.send(
                        createRecord(it),
                    )
                }
                producer.flush()
                recommendationsWithPoDetails
            }.await()

        logger.info("Updated ${updatedSQRShortShelfLifes.size} supplyRecommendations")
        return updatedSQRShortShelfLifes
    }

    private fun mapToUom(skuUOM: SkuUOM) =
        when (skuUOM) {
            SkuUOM.UOM_UNSPECIFIED, SkuUOM.UOM_UNRECOGNIZED -> UOM.UOM_UNSPECIFIED
            SkuUOM.UOM_UNIT -> UOM.UOM_UNIT
            SkuUOM.UOM_KG -> UOM.UOM_KG
            SkuUOM.UOM_LBS -> UOM.UOM_LBS
            SkuUOM.UOM_GAL -> UOM.UOM_GAL
            SkuUOM.UOM_LITRE -> UOM.UOM_LITRE
            SkuUOM.UOM_OZ -> UOM.UOM_OZ
        }

    companion object : Logging {
        private fun BigDecimal.toProtoDecimal() = Decimal.newBuilder().setValue(
            this.stripTrailingZeros().toPlainString(),
        ).build()

        fun createProducer(kafkaProducerConfiguration: Map<String, String>):
            Producer<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal> =

            KafkaProducer(
                kafkaProducerConfiguration +
                    mapOf(
                        ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION to 1,
                        ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG to true,
                        ProducerConfig.ACKS_CONFIG to "all",
                    ),
                serializer<SupplyQuantityRecommendationDailyKey>(),
                serializer<SupplyQuantityRecommendationDailyVal>(),
            )
    }
}

data class SqrShortShelfLifeParams(val marketParam: Map<String, SqrShortShelfLifeDcParam>)

data class SqrShortShelfLifeDcParam(
    val skuCategoriesIncluded: Set<String>,
    val skuCategoriesExcluded: Set<String>,
)
