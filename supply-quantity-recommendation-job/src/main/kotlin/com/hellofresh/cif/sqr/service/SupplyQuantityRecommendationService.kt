package com.hellofresh.cif.sqr.service

import com.google.type.Decimal
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.lib.kafka.serde.serializer
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuQuantity.Companion.max
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.sqr.SQRConfigurations
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendation
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationRepository.upsertSupplyQuantityRecommendations
import com.hellofresh.cif.sqr.repository.WeeklyCalculation
import com.hellofresh.cif.sqr.repository.WeeklyCalculationsRepository
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.UOM
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationVal
import java.math.BigDecimal
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.logging.log4j.kotlin.Logging

const val SQR_TOPIC_NAME = "public.ordering.supply-quantity-recommendation.v1"

const val BATCH_SIZE = 10000

@Suppress("LongParameterList")
class SupplyQuantityRecommendationService(
    private val dcConfigService: DcConfigService,
    private val weeklyCalculationsRepository: WeeklyCalculationsRepository,
    private val supplyQuantityRecommendationConfigRepository: SupplyQuantityRecommendationConfigRepository,
    private val safetyStockRepository: SafetyStockRepository,
    private val readWriteMetricsDSLContext: MetricsDSLContext,
    private val producer: Producer<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal>,
) {

    suspend fun run(dcCodes: Set<String>) {
        val distributionCenters = dcCodes.mapNotNull {
            dcConfigService.dcConfigurations[it]
                ?: run {
                    logger.warn("No DC configuration found for $it")
                    null
                }
        }.toSet()

        kotlin.runCatching {
            withContext(Dispatchers.IO) {
                val deferredWeeklyCalculations =
                    async { weeklyCalculationsRepository.fetchWeeklyCalculations(distributionCenters) }
                val deferredSQRConfigs = async {
                    supplyQuantityRecommendationConfigRepository.fetchSupplyQuantityRecommendationConfigurations(
                        distributionCenters,
                    )
                }
                val deferredSafetyStocks = async {
                    SafetyStocks(safetyStockRepository.fetchSafetyStocksFromLatestProductionWeek(distributionCenters))
                }

                val supplyQuantityRecommendations =
                    calculateSupplyQuantityRecommendations(
                        distributionCenters,
                        deferredWeeklyCalculations.await(),
                        deferredSQRConfigs.await(),
                        deferredSafetyStocks.await(),
                    )

                saveAndSend(supplyQuantityRecommendations)
            }
        }.onFailure {
            logger.warn("Error occurred while preparing supplied quantity recommendations", it)
        }
    }

    private fun calculateSupplyQuantityRecommendations(
        distributionCenters: Set<DistributionCenterConfiguration>,
        calculations: List<WeeklyCalculation>,
        sqrConfigurations: SQRConfigurations,
        safetyStocks: SafetyStocks
    ): List<SupplyQuantityRecommendation> {
        val dcs = distributionCenters.associateBy { it.dcCode }

        return calculations.mapNotNull { c ->
            dcs[c.dcCode]?.let { dc ->
                val productionWeek = ProductionWeek(c.week, dc.productionStart, dc.zoneId)
                val sqrConfiguration = sqrConfigurations.getConfiguration(c.dcCode, c.skuId, productionWeek)
                val safetyStockQuantity = safetyStocks.getSafetyStock(c.dcCode, c.skuId, productionWeek.dcWeek)
                    ?.let {
                        // TODO CPS UOM UPDATE WHEN SAFETY STOCK RETURNS UOM
                        SkuQuantity.fromLong(it.value, c.uom)
                    }

                val sqrValue = applySQRFormula(c, safetyStockQuantity)

                SupplyQuantityRecommendation(
                    dcCode = dc.dcCode,
                    skuId = c.skuId,
                    productionWeek = c.week,
                    supplyQuantityRecommendationValue = sqrValue,
                    aggregatedDemand = c.aggregatedDemand,
                    inventoryRollover = c.productionStartOpeningStock,
                    safetyStock = safetyStockQuantity,
                    recommendationEnabled = sqrConfiguration.recommendationEnabled,
                    multiWeekEnabled = sqrConfiguration.multiWeekEnabled,
                    inbound = c.inbound,
                    incomingPos = c.incomingPos,
                    unusableStock = c.unusableStock,
                )
            }
        }
    }

    private fun applySQRFormula(weeklyCalculation: WeeklyCalculation, safetyStock: SkuQuantity?): SkuQuantity {
        val zero = SkuQuantity.fromLong(0, weeklyCalculation.uom)
        return max(
            zero,
            (safetyStock ?: zero) + weeklyCalculation.aggregatedDemand - weeklyCalculation.productionStartOpeningStock,
        )
    }

    private suspend fun saveAndSend(
        supplyQuantityRecommendations: List<SupplyQuantityRecommendation>,
    ) {
        logger.info("Saving ${supplyQuantityRecommendations.size} supplyQuantityRecommendations")
        val updatedSqRecommendations = readWriteMetricsDSLContext.withTagName(
            "save-all-supply-quantity-recommendations",
        )
            .transactionResultAsync { conf ->
                val txDsl = readWriteMetricsDSLContext.withMeteredConfiguration(conf)
                val sqrRecommendations = supplyQuantityRecommendations
                    .chunked(BATCH_SIZE)
                    .flatMap { batch -> txDsl.upsertSupplyQuantityRecommendations(batch) }

                sqrRecommendations.forEach {
                    logger.debug(
                        "Starting to publish weekly sqr calculation to dcCode = ${it.dcCode}, skuId = ${it.skuId} " +
                            "production week = ${it.productionWeek}",
                    )
                    producer.send(
                        createRecord(it),
                    )
                }

                producer.flush()

                sqrRecommendations
            }.await()

        logger.info("Updated ${updatedSqRecommendations.size} supplyRecommendations")
    }

    private fun createRecord(
        supplyQuantityRecommendation: SupplyQuantityRecommendation
    ) =
        with(supplyQuantityRecommendation) {
            val dcWeek = DcWeek(productionWeek)
            val key = SupplyQuantityRecommendationKey.newBuilder()
                .setDistributionCenterBobCode(dcCode)
                .setProductionWeek(
                    com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.ProductionWeek.newBuilder()
                        .setWeek(dcWeek.week)
                        .setYear(dcWeek.year),
                ).setSkuId(skuId.toString())

            val value = SupplyQuantityRecommendationVal.newBuilder()
                .setDistributionCenterBobCode(key.distributionCenterBobCode)
                .setProductionWeek(key.productionWeek)
                .setSkuId(key.skuId)
                .setSupplyQuantityRecommendation(
                    supplyQuantityRecommendationValue.getValue().toProtoDecimal(),
                )
                .setInventoryRollover(inventoryRollover.getValue().toProtoDecimal())
                .setDemand(aggregatedDemand.getValue().toProtoDecimal())
                .setRecommendationEnabled(recommendationEnabled)
                .setMultiWeekEnabled(multiWeekEnabled)
                .setInbound(inbound.getValue().toProtoDecimal())
                .setIncomingPos(incomingPos.getValue().toProtoDecimal())
                .setUnusableStock(unusableStock.getValue().toProtoDecimal())
                .setUnit(mapToUom(uom))

            if (safetyStock != null) {
                value.setSafetyStock(safetyStock.getValue().toProtoDecimal())
            }

            ProducerRecord(SQR_TOPIC_NAME, null, key.build(), value.build())
        }

    companion object : Logging {

        private fun BigDecimal.toProtoDecimal() = Decimal.newBuilder().setValue(
            this.stripTrailingZeros().toPlainString(),
        ).build()

        fun createProducer(kafkaProducerConfiguration: Map<String, String>):
            Producer<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal> =

            KafkaProducer(
                kafkaProducerConfiguration +
                    mapOf(
                        ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION to 1,
                        ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG to true,
                        ProducerConfig.ACKS_CONFIG to "all",
                    ),
                serializer<SupplyQuantityRecommendationKey>(),
                serializer<SupplyQuantityRecommendationVal>(),
            )

        private data class SafetyStocks(private val safetyStocks: List<SafetyStock>) {

            private val safetyStockMap = safetyStocks.associateBy { it.toKey() }

            fun getStrategyBySkuDcWeek(
                dcCode: String,
                skuId: UUID,
                productionWeek: DcWeek
            ) = safetyStocks.firstOrNull {
                it.skuId == skuId && it.dcCode == dcCode && it.week == productionWeek.value
            }?.strategy

            fun getSafetyStock(dcCode: String, skuId: UUID, productionWeek: DcWeek): SafetyStock? =
                safetyStockMap[SafetyStockKey(dcCode, productionWeek, skuId)]
        }

        private fun mapToUom(skuUOM: SkuUOM) =
            when (skuUOM) {
                SkuUOM.UOM_UNSPECIFIED, SkuUOM.UOM_UNRECOGNIZED -> UOM.UOM_UNSPECIFIED
                SkuUOM.UOM_UNIT -> UOM.UOM_UNIT
                SkuUOM.UOM_KG -> UOM.UOM_KG
                SkuUOM.UOM_LBS -> UOM.UOM_LBS
                SkuUOM.UOM_GAL -> UOM.UOM_GAL
                SkuUOM.UOM_LITRE -> UOM.UOM_LITRE
                SkuUOM.UOM_OZ -> UOM.UOM_OZ
            }
    }
}
