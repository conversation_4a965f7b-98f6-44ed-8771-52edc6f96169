package com.hellofresh.cif.sqr.shortshelflife.repository

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationRepository.mapToSkuQuantity
import com.hellofresh.cif.sqr.schema.Tables.SQR_SHORT_SHELF_LIFE
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.sqr.schema.tables.records.SqrShortShelfLifeRecord
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Condition
import org.jooq.JSONB
import org.jooq.TableField
import org.jooq.impl.DSL

private val objectMapper = ObjectMapper().findAndRegisterModules()
private val emptyJsonB = JSONB.valueOf("{}")
private val poDetailsCoalesce = DSL.coalesce(SQR_SHORT_SHELF_LIFE.PO_DETAILS, emptyJsonB)
private val excludedPoDetailsCoalesce = DSL.coalesce(
    DSL.excluded(SQR_SHORT_SHELF_LIFE.PO_DETAILS),
    emptyJsonB,
)
private val poDetailsDistinctCondition = DSL.not(
    poDetailsCoalesce.contains(excludedPoDetailsCoalesce)
        .and(
            excludedPoDetailsCoalesce.contains(poDetailsCoalesce),
        ),
)

object SQRShortShelfLifeRepository : Logging {

    fun MetricsDSLContext.upsertSqrShortShelfLifeRecommendations(
        sqrShortShelfLifeList: List<SQRShortShelfLife>
    ): List<SQRShortShelfLife> {
        val records = toRecords(sqrShortShelfLifeList)

        return this.withTagName("upsert-sqr-short-shelf-life")
            .insertInto(SQR_SHORT_SHELF_LIFE)
            .set(records)
            .onDuplicateKeyUpdate()
            .set(SQR_SHORT_SHELF_LIFE.SQR, DSL.excluded(SQR_SHORT_SHELF_LIFE.SQR))
            .set(SQR_SHORT_SHELF_LIFE.OPENING_SOCK, DSL.excluded(SQR_SHORT_SHELF_LIFE.OPENING_SOCK))
            .set(SQR_SHORT_SHELF_LIFE.UNUSABLE_STOCK, DSL.excluded(SQR_SHORT_SHELF_LIFE.UNUSABLE_STOCK))
            .set(SQR_SHORT_SHELF_LIFE.CONSUMPTION, DSL.excluded(SQR_SHORT_SHELF_LIFE.CONSUMPTION))
            .set(SQR_SHORT_SHELF_LIFE.STOCK_UPDATE, DSL.excluded(SQR_SHORT_SHELF_LIFE.STOCK_UPDATE))
            .set(SQR_SHORT_SHELF_LIFE.BUFFER_PERCENTAGE, DSL.excluded(SQR_SHORT_SHELF_LIFE.BUFFER_PERCENTAGE))
            .set(SQR_SHORT_SHELF_LIFE.BUFFER_ADDITIONAL, DSL.excluded(SQR_SHORT_SHELF_LIFE.BUFFER_ADDITIONAL))
            .set(SQR_SHORT_SHELF_LIFE.PRODUCTION_WEEK, DSL.excluded(SQR_SHORT_SHELF_LIFE.PRODUCTION_WEEK))
            .set(SQR_SHORT_SHELF_LIFE.PO_DETAILS, DSL.excluded(SQR_SHORT_SHELF_LIFE.PO_DETAILS))
            .set(
                SQR_SHORT_SHELF_LIFE.TOUCHLESS_ORDERING_ENABLED,
                DSL.excluded(SQR_SHORT_SHELF_LIFE.TOUCHLESS_ORDERING_ENABLED),
            )
            .set(SQR_SHORT_SHELF_LIFE.UOM, DSL.excluded(SQR_SHORT_SHELF_LIFE.UOM))
            .where(
                isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.SQR)
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.OPENING_SOCK))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.UNUSABLE_STOCK))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.CONSUMPTION))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.STOCK_UPDATE))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.BUFFER_PERCENTAGE))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.BUFFER_ADDITIONAL))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.TOUCHLESS_ORDERING_ENABLED))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.UOM))
                    .or(isDistinctFromExcluded(SQR_SHORT_SHELF_LIFE.PRODUCTION_WEEK))
                    .or(poDetailsDistinctCondition),
            )
            .returning()
            .fetch()
            .map { record ->
                SQRShortShelfLife(
                    dcCode = record.dcCode,
                    date = record.date,
                    skuId = record.skuId,
                    openingStock = record.openingSock.mapToSkuQuantity(record.uom),
                    unusable = record.unusableStock.mapToSkuQuantity(record.uom),
                    consumption = record.consumption.mapToSkuQuantity(record.uom),
                    stockUpdate = record.stockUpdate?.mapToSkuQuantity(record.uom),
                    bufferPercentage = record.bufferPercentage,
                    bufferAdditional = record.bufferAdditional,
                    touchlessOrderingEnabled = record.touchlessOrderingEnabled,
                    sqr = record.sqr.mapToSkuQuantity(record.uom),
                    productionWeek = record.productionWeek,
                    poData = objectMapper.readValue<PoData>(
                        record.poDetails.data(),
                    ),
                )
            }
    }

    fun MetricsDSLContext.getTouchlessOrderingFlags(
        dcCodes: Set<String>?,
        week: String?
    ): Map<Pair<UUID, String>, Boolean> = this.select(
        SQR_SHORT_SHELF_LIFE.SKU_ID,
        SQR_SHORT_SHELF_LIFE.DC_CODE,
        SQR_SHORT_SHELF_LIFE.TOUCHLESS_ORDERING_ENABLED
    )
        .from(SQR_SHORT_SHELF_LIFE)
        .where(
            SQR_SHORT_SHELF_LIFE.DC_CODE.`in`(dcCodes)
                .and(SQR_SHORT_SHELF_LIFE.PRODUCTION_WEEK.eq(week))
        )
        .fetch()
        .groupBy { record ->
            record.get(SQR_SHORT_SHELF_LIFE.SKU_ID) to record.get(SQR_SHORT_SHELF_LIFE.DC_CODE)
        }
        .mapValues { (_, records) ->
            records.all { it.get(SQR_SHORT_SHELF_LIFE.TOUCHLESS_ORDERING_ENABLED) == true }
        }

    private fun toRecords(sqrShortShelfLife: List<SQRShortShelfLife>): List<SqrShortShelfLifeRecord> =
        sqrShortShelfLife.map {
            SqrShortShelfLifeRecord().apply {
                dcCode = it.dcCode
                date = it.date
                skuId = it.skuId
                openingSock = it.openingStock.getValue()
                unusableStock = it.unusable.getValue()
                consumption = it.consumption.getValue()
                stockUpdate = it.stockUpdate?.getValue()
                bufferPercentage = it.bufferPercentage
                bufferAdditional = it.bufferAdditional
                sqr = it.sqr.getValue()
                uom = mapToUom(it.uom)
                touchlessOrderingEnabled = it.touchlessOrderingEnabled
                productionWeek = it.productionWeek
                poDetails = JSONB.jsonbOrNull(
                    it.poData.let {
                        objectMapper.writeValueAsString(it)
                    },
                )
            }
        }

    private fun <T> isDistinctFromExcluded(field: TableField<SqrShortShelfLifeRecord, T>): Condition =
        field.isDistinctFrom(DSL.excluded(field))

    fun mapToUom(skuUOM: SkuUOM) =
        when (skuUOM) {
            SkuUOM.UOM_UNSPECIFIED -> UOM_UNSPECIFIED
            SkuUOM.UOM_UNRECOGNIZED -> UOM_UNRECOGNIZED
            SkuUOM.UOM_UNIT -> UOM_UNIT
            SkuUOM.UOM_KG -> UOM_KG
            SkuUOM.UOM_LBS -> UOM_LBS
            SkuUOM.UOM_GAL -> UOM_GAL
            SkuUOM.UOM_LITRE -> UOM_LITRE
            SkuUOM.UOM_OZ -> UOM_OZ
        }
}

data class SQRShortShelfLife(
    val dcCode: String,
    val date: LocalDate,
    val skuId: UUID,
    val openingStock: SkuQuantity,
    val unusable: SkuQuantity,
    val consumption: SkuQuantity,
    val stockUpdate: SkuQuantity?,
    val bufferPercentage: BigDecimal,
    val bufferAdditional: BigDecimal,
    val sqr: SkuQuantity,
    val touchlessOrderingEnabled: Boolean,
    val productionWeek: String,
    val poData: PoData,
) {
    val uom: SkuUOM
        get() = sqr.unitOfMeasure

    companion object
}

data class PoData(
    val poDetails: List<PoDetails>,
    val inboundDetails: List<PoDetails>,
)

data class PoDetails(
    val qty: BigDecimal,
    val poNumber: String,
)
