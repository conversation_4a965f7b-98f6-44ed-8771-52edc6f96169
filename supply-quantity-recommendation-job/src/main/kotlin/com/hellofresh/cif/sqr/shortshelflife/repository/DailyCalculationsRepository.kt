package com.hellofresh.cif.sqr.shortshelflife.repository

import com.google.common.base.Stopwatch
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationRepository.mapToSkuUom
import com.hellofresh.cif.sqr.schema.Tables.CALCULATION
import com.hellofresh.cif.sqr.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sqr.schema.enums.Uom
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.TimeUnit.MILLISECONDS
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Record
import org.jooq.impl.DSL.select

const val SEEK_SIZE = 10000

class DailyCalculationsRepository(private val dslContext: MetricsDSLContext) {

    fun fetchDailyCalculations(
        distributionCenters: Set<DistributionCenterConfiguration>,
    ): Flow<List<DailyCalculation>> =
        flow {
            distributionCenters.groupBy {
                it.getLatestProductionStart()
            }
                .forEach { (productionStart, dcs) ->
                    val dcMap = dcs.associateBy { it.dcCode }
                    val dcCodes = dcMap.keys
                    var lastKey: CalculationKey? = null
                    var moreResults = true
                    while (moreResults) {
                        val timer = Stopwatch.createStarted()
                        kotlin.runCatching {
                            selectSeekQuery(lastKey, productionStart, dcCodes)
                                .await()
                                .also { results ->
                                    if (results.isEmpty()) {
                                        moreResults = false
                                    } else {
                                        logger.info(
                                            "DailyCalculation seek: ${
                                                timer.stop().elapsed(
                                                    MILLISECONDS,
                                                )
                                            }ms - Count:${results.count()}",
                                        )
                                        emit(
                                            results.map {
                                                toDailyCalculation(it)
                                                    .also {
                                                        lastKey = toKey(it)
                                                    }
                                            },
                                        )
                                    }
                                }
                        }.onFailure {
                            logger.warn("Error while fetching daily calculations", it)
                        }
                    }
                }
        }.flowOn(Dispatchers.IO)

    private fun selectSeekQuery(
        lastKey: CalculationKey?,
        productionStart: LocalDate,
        dcs: Set<String>,
    ) =
        dslContext.withTagName("fetch-sqr-daily-calculations-by-dcs-seek")
            .fetchAsync(
                select(
                    CALCULATION.DC_CODE,
                    CALCULATION.CSKU_ID,
                    CALCULATION.DATE,
                    CALCULATION.OPENING_STOCK,
                    CALCULATION.EXPIRED,
                    CALCULATION.DEMANDED,
                    CALCULATION.STOCK_UPDATE,
                    CALCULATION.UOM,
                    CALCULATION.PRODUCTION_WEEK,
                    SKU_SPECIFICATION_VIEW.CATEGORY,
                ).from(CALCULATION)
                    .join(SKU_SPECIFICATION_VIEW).on(SKU_SPECIFICATION_VIEW.ID.eq(CALCULATION.CSKU_ID))
                    .where(
                        CALCULATION.DC_CODE.`in`(dcs)
                            .and(CALCULATION.DATE.ge(productionStart)),
                    ).orderBy(
                        CALCULATION.CSKU_ID,
                        CALCULATION.DC_CODE,
                        CALCULATION.DATE,
                    ).let { query ->
                        lastKey
                            ?.let {
                                query.seek(lastKey.skuId, lastKey.dcCode, lastKey.date)
                                    .limit(SEEK_SIZE)
                            }
                            ?: query.limit(SEEK_SIZE)
                    },
            )

    private fun toDailyCalculation(record: Record) =
        DailyCalculation(
            dcCode = record[CALCULATION.DC_CODE],
            date = record[CALCULATION.DATE],
            skuId = record[CALCULATION.CSKU_ID],
            skuCategory = record[SKU_SPECIFICATION_VIEW.CATEGORY],
            openingStock = record[CALCULATION.OPENING_STOCK].mapToSkuQuantity(record[CALCULATION.UOM]),
            unusable = record[CALCULATION.EXPIRED].mapToSkuQuantity(record[CALCULATION.UOM]),
            consumption = record[CALCULATION.DEMANDED].mapToSkuQuantity(record[CALCULATION.UOM]),
            stockUpdate = record[CALCULATION.STOCK_UPDATE]?.mapToSkuQuantity(record[CALCULATION.UOM]),
            productionWeek = record[CALCULATION.PRODUCTION_WEEK],
        )

    private fun toKey(dailyCalculation: DailyCalculation) = CalculationKey(
        dailyCalculation.dcCode,
        dailyCalculation.skuId,
        dailyCalculation.date,
    )

    companion object : Logging {
        private fun BigDecimal.mapToSkuQuantity(uom: Uom) =
            SkuQuantity.fromBigDecimal(this, mapToSkuUom(uom))

        private data class CalculationKey(val dcCode: String, val skuId: UUID, val date: LocalDate)
    }
}

data class DailyCalculation(
    val dcCode: String,
    val date: LocalDate,
    val skuId: UUID,
    val skuCategory: String,
    val openingStock: SkuQuantity,
    val unusable: SkuQuantity,
    val consumption: SkuQuantity,
    val stockUpdate: SkuQuantity?,
    val productionWeek: String,
) {
    val uom: SkuUOM
        get() = openingStock.unitOfMeasure

    companion object
}
