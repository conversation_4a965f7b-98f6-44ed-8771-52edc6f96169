package com.hellofresh.cif.sqr.model

import com.hellofresh.cif.fileconsumer.model.ParsedFile
import org.apache.commons.csv.CSVRecord

class BNLBufferCalculationParsedFile(records: List<CSVRecord>) : ParsedFile {
    override val columns = Companion.columns
    override val data = data(records)
    override val pKey: List<String> = listOf()
    override val numericColumnDataTypes: Set<String> = Companion.numericColumnDataTypes
    override val allowedBlank: Set<String> = Companion.allowedBlank

    companion object {
        const val WEEK = "Week"
        const val DATE_HEADER = "Date"
        const val DC_HEADER = "DC"
        const val SKU_CODE_HEADER = "SKUCode"
        const val BUFFER_VALUE_HEADER = "BufferValue"

        val columns: List<String> = listOf(
            WEEK,
            DATE_HEADER,
            DC_HEADER,
            SKU_CODE_HEADER,
            BUFFER_VALUE_HEADER,
        )

        val numericColumnDataTypes = setOf(
            BUFFER_VALUE_HEADER
        )

        val allowedBlank = emptySet<String>()
    }
}
