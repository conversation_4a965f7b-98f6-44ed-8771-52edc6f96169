import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.repository.WeeklyCalculation
import java.util.UUID
import kotlin.random.Random

fun WeeklyCalculation.Companion.random(
    dcCode: String = "DC",
    skuId: UUID = UUID.randomUUID(),
    week: String = "2055-W05",
    uom: SkuUOM? = null
) =
    with(uom ?: SkuUOM.entries.random()) {
        WeeklyCalculation(
            dcCode,
            skuId,
            week,
            SkuQuantity.fromDouble(Random.nextDouble(), this),
            SkuQuantity.fromDouble(Random.nextDouble(), this),
            SkuQuantity.fromDouble(Random.nextDouble(), this),
            SkuQuantity.fromDouble(Random.nextDouble(), this),
            SkuQuantity.fromDouble(Random.nextDouble(), this),
        )
    }
