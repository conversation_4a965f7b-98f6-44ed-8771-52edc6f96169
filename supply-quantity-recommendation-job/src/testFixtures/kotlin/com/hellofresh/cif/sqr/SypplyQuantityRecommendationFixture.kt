package com.hellofresh.cif.sqr

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendation
import com.hellofresh.cif.sqr.shortshelflife.repository.PoData
import com.hellofresh.cif.sqr.shortshelflife.repository.PoDetails
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLife
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random

fun SupplyQuantityRecommendation.Companion.random(dcCode: String = "DC", skuId: UUID = UUID.randomUUID(), week: String = "2055-W05") =
    with(SkuUOM.entries.random()) {
        SupplyQuantityRecommendation(
            dcCode = dcCode,
            skuId = skuId,
            productionWeek = week,
            supplyQuantityRecommendationValue = SkuQuantity.fromDouble(Random.nextDouble(), this),
            aggregatedDemand = SkuQuantity.fromDouble(Random.nextDouble(), this),
            inventoryRollover = SkuQuantity.fromDouble(Random.nextDouble(), this),
            safetyStock = if (Random.nextBoolean()) SkuQuantity.fromDouble(Random.nextDouble(), this) else null,
            recommendationEnabled = Random.nextBoolean(),
            multiWeekEnabled = Random.nextBoolean(),
            incomingPos = SkuQuantity.fromDouble(Random.nextDouble(), this),
            inbound = SkuQuantity.fromDouble(Random.nextDouble(), this),
            unusableStock = SkuQuantity.fromDouble(Random.nextDouble(), this),
        )
    }

fun SQRShortShelfLife.Companion.random(
    dcCode: String = "DC",
    skuId: UUID = UUID.randomUUID(),
    date: LocalDate = LocalDate.now(
        UTC,
    ),
    sqr: SkuQuantity = SkuQuantity.fromDouble(Random.nextDouble(), SkuUOM.entries.random()),
) =
    with(SkuUOM.entries.random()) {
        SQRShortShelfLife(
            dcCode = dcCode,
            skuId = skuId,
            date = date,
            openingStock = SkuQuantity.fromDouble(Random.nextDouble(), this),
            unusable = SkuQuantity.fromDouble(Random.nextDouble(), this),
            consumption = SkuQuantity.fromDouble(Random.nextDouble(), this),
            stockUpdate = if (Random.nextBoolean()) SkuQuantity.fromDouble(Random.nextDouble(), this) else null,
            bufferPercentage = BigDecimal.valueOf(Random.nextDouble()),
            bufferAdditional = BigDecimal(Random.nextInt()),
            sqr = sqr,
            touchlessOrderingEnabled = false,
            productionWeek = UUID.randomUUID().toString(),
            poData = PoData(
                poDetails = listOf(
                    PoDetails(
                        poNumber = UUID.randomUUID().toString(),
                        qty = BigDecimal(Random.nextInt()),
                    )
                ),
                inboundDetails = listOf(
                    PoDetails(
                        poNumber = UUID.randomUUID().toString(),
                        qty = BigDecimal(Random.nextInt()),
                    )
                ),
            ),
        )
    }
