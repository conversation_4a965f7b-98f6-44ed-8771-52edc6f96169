package com.hellofresh.cif.goodsreceivednote.models

import com.hellofresh.cif.models.SkuUOM
import java.time.LocalDate
import java.time.OffsetDateTime

data class GRNKey(val dcCode: String, val reference: String)

data class GRNValue(
    val deliveriesList: List<GRNPurchaseOrderDelivery>,
)

data class GRNPurchaseOrderDelivery(
    val id: String,
    val deliveryTime: OffsetDateTime,
    val linesList: List<GRNPurchaseOrderDeliveryLine>
)

data class GRNPurchaseOrderDeliveryLine(
    val skuCode: String,
    val state: GRNDeliveryLineState,
    val skuUom: SkuUOM,
    val palletizedQuantity: String,
    val expirationDate: LocalDate?,
)

enum class GRNDeliveryLineState {
    DELIVERY_LINE_STATE_UNSPECIFIED,
    DELIVERY_LINE_STATE_EXPECTED,
    DELIVERY_LINE_STATE_OPEN,
    DELIVERY_LINE_STATE_RECEIVED,
    DELIVERY_LINE_STATE_REJECTED,
    DELIVERY_LINE_STATE_CLOSED,
    DELIVERY_LINE_STATE_CANCELLED,
    UNRECOGNIZED
}
