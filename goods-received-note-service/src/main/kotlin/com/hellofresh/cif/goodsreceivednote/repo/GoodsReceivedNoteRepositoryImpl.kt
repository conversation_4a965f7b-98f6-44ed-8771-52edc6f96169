package com.hellofresh.cif.goodsreceivednote.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.goodsReceivedNote.schema.Tables.GOODS_RECEIVED_NOTE
import com.hellofresh.cif.goodsReceivedNote.schema.enums.GrnSource as GrnSourceTypeInDb
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.goodsreceivednote.service.Grn
import com.hellofresh.cif.goodsreceivednote.service.GrnSourceType
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL.insertInto

class GoodsReceivedNoteRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) :
    GoodsReceivedNoteRepository {

    private val saveGoodsReceivedNote = "save-goods-received-note"

    override suspend fun save(goodsReceivedNotes: List<Grn>) {
        metricsDSLContext.withTagName(saveGoodsReceivedNote)
            .transactionAsync { tx ->
                goodsReceivedNotes.forEach { grn ->
                    tx.dsl().deleteFrom(GOODS_RECEIVED_NOTE)
                        .where(GOODS_RECEIVED_NOTE.DC_CODE.eq(grn.dcCode))
                        .and(GOODS_RECEIVED_NOTE.PO_NUMBER.eq(grn.poNumber))
                        .execute()

                    tx.dsl().batch(
                        insertInto(GOODS_RECEIVED_NOTE)
                            .columns(
                                GOODS_RECEIVED_NOTE.SKU_ID,
                                GOODS_RECEIVED_NOTE.DC_CODE,
                                GOODS_RECEIVED_NOTE.PO_REF,
                                GOODS_RECEIVED_NOTE.PO_NUMBER,
                                GOODS_RECEIVED_NOTE.QUANTITY,
                                GOODS_RECEIVED_NOTE.DELIVERY_ID,
                                GOODS_RECEIVED_NOTE.DELIVERY_TIME,
                                GOODS_RECEIVED_NOTE.DELIVERY_STATUS,
                                GOODS_RECEIVED_NOTE.EXPIRY_DATE,
                                GOODS_RECEIVED_NOTE.UOM,
                                GOODS_RECEIVED_NOTE.SOURCE,
                            )
                            .values(
                                UUID(0, 0), "", "", "", BigDecimal.ZERO, "",
                                OffsetDateTime.MIN, "", null, UOM_UNIT, GrnSourceTypeInDb.PURCHASE_ORDER,
                            ),
                    ).apply {
                        grn.skus.forEach {
                            bind(
                                it.skuId, grn.dcCode, grn.poRef, grn.poNumber, it.totalPalletizedQty.getValue(),
                                it.deliveryId, it.deliveryDateTime, it.status, it.expiryDate, toUom(it.skuUOM),
                                mapToGrnSourceDbType(grn.sourceType),
                            )
                        }
                        execute()
                    }
                }
            }.await()
    }

    private fun mapToGrnSourceDbType(sourceType: GrnSourceType) = when (sourceType) {
        GrnSourceType.PURCHASE_ORDER -> GrnSourceTypeInDb.PURCHASE_ORDER
        GrnSourceType.TRANSFER_ORDER -> GrnSourceTypeInDb.TRANSFER_ORDER
    }

    private fun toUom(skuUOM: SkuUOM): Uom =
        when (skuUOM) {
            SkuUOM.UOM_UNIT -> UOM_UNIT

            SkuUOM.UOM_KG -> Uom.UOM_KG
            SkuUOM.UOM_LBS -> Uom.UOM_LBS
            SkuUOM.UOM_OZ -> Uom.UOM_OZ
            SkuUOM.UOM_GAL -> Uom.UOM_GAL
            SkuUOM.UOM_LITRE -> Uom.UOM_LITRE
            SkuUOM.UOM_UNRECOGNIZED, SkuUOM.UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
        }
}
