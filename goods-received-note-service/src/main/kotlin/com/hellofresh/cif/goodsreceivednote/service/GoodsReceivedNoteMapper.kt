package com.hellofresh.cif.goodsreceivednote.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState
import com.hellofresh.cif.goodsreceivednote.models.GRNKey
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDeliveryLine
import com.hellofresh.cif.goodsreceivednote.models.GRNValue
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus.CLOSED
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus.OPEN
import com.hellofresh.cif.goodsreceivednote.utils.GrnSourceTypeFinder
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PoUtils.isPoRefValidForDc
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuCodeLookUp
import com.hellofresh.sku.models.SkuSpecification
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import org.apache.logging.log4j.Level.ERROR
import org.apache.logging.log4j.Level.WARN
import org.apache.logging.log4j.kotlin.Logging

const val EPOCH_0_YEAR = 1970
private const val AU_MARKET = "AU"
private const val YOUFOODZ_SKU_CODE_PREFIX = "YFZ_"

object GoodsReceivedNoteMapper : Logging {

    fun getPoNr(poRef: String) = poRef.split("_")[0]

    @Suppress("LongParameterList")
    fun mapGrn(
        key: GRNKey,
        value: GRNValue,
        dc: DistributionCenterConfiguration,
        skuCodeLookUp: SkuCodeLookUp,
    ): Grn? =
        value
            .takeIf { value ->
                isPoRefValidForDc(dc.dcCode, key.reference)
            }
            ?.deliveriesList?.groupBy { it.id to it.deliveryTime }
            ?.flatMap {
                val (deliveryId, deliveryDateTime) = it.key
                it.value.flatMap { delivery -> delivery.linesList }
                    .groupBy { v -> v.skuCode }
                    .mapNotNull { (skuCode, deliveryLines) ->
                        skuCodeLookUp.getSku(skuCode, dc, key.reference)
                            ?.let { (skuId, sku) ->
                                mapGrnSku(deliveryLines, sku, skuId, deliveryId, deliveryDateTime)
                            }
                    }
            }
            ?.let {
                if (it.isNotEmpty()) {
                    Grn(dc.dcCode, key.reference, GrnSourceTypeFinder.findGrnSourceType(key.reference), it)
                } else {
                    null
                }
            }

    private fun mapGrnSku(
        deliveryLines: List<GRNPurchaseOrderDeliveryLine>,
        sku: SkuSpecification,
        skuId: UUID,
        deliveryId: String,
        deliveryDateTime: OffsetDateTime
    ): GrnSku? {
        val closedDeliveriesForSku =
            deliveryLines.filter { x -> x.state == GRNDeliveryLineState.DELIVERY_LINE_STATE_CLOSED }
        val deliveryStatus = getDeliveryStatus(closedDeliveriesForSku, deliveryLines)
        val skuUOM = deliveryLines.first().skuUom
        val sumPalletizedQtyClosedDeliveries =
            SkuQuantity.fromBigDecimal(
                closedDeliveriesForSku.sumOf { qty ->
                    SkuQuantity.fromString(
                        qty.palletizedQuantity,
                        qty.skuUom,
                    ).getValue()
                },
                skuUOM,
            )

        val expiryDate = getExpiryDate(closedDeliveriesForSku)
        return if (skuUOM != sku.uom) {
            logger.warn(
                "UOM mismatch for skuId: $skuId, deliveryId: $deliveryId, expected: ${sku.uom}, received: $skuUOM",
            )
            null
        } else {
            GrnSku(
                deliveryId,
                deliveryDateTime,
                skuId,
                sumPalletizedQtyClosedDeliveries,
                deliveryStatus,
                expiryDate,
                skuUOM,
            )
        }
    }

    private fun getExpiryDate(deliveries: List<GRNPurchaseOrderDeliveryLine>): LocalDate? =
        deliveries.mapNotNull { it.expirationDate }.minOrNull()

    private fun SkuCodeLookUp.getSku(
        skuCode: String,
        dc: DistributionCenterConfiguration,
        poReference: String
    ): Pair<UUID, SkuSpecification>? {
        val sku = this[SkuCodeDcKey(skuCode, dc.dcCode)]
        return sku ?: run {
            val logLevel =
                if (dc.market == AU_MARKET && poReference.startsWith(YOUFOODZ_SKU_CODE_PREFIX)) {
                    WARN
                } else {
                    ERROR
                }
            logger.log(
                logLevel,
                "Cannot find skuId for GRN with skuCode, dcCode, poRef. Skipping $skuCode, ${dc.dcCode}, $poReference",
            )
            null
        }
    }

    private fun getDeliveryStatus(
        closedDeliveries: List<GRNPurchaseOrderDeliveryLine>,
        deliveryLines: List<GRNPurchaseOrderDeliveryLine>
    ): DeliveryStatus {
        val deliveryStatus = if (closedDeliveries.size < deliveryLines.size) {
            OPEN
        } else {
            CLOSED
        }
        return deliveryStatus
    }
}
