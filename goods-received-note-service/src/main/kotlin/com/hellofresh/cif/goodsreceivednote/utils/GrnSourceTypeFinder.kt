package com.hellofresh.cif.goodsreceivednote.utils

import com.hellofresh.cif.goodsreceivednote.service.GrnSourceType

object GrnSourceTypeFinder {

    private val purchaseOrderRegex = Regex("""^\d{4}[A-Za-z]{2}\d{5,}_?[A-Za-z0-9:]*$""")
    private val transferOrderRegex = Regex("""^\d{4}[A-Z]{3}\d{5}$""")

    fun findGrnSourceType(reference: String): GrnSourceType =
        when {
            purchaseOrderRegex.matches(reference) -> GrnSourceType.PURCHASE_ORDER
            transferOrderRegex.matches(reference) -> GrnSourceType.TRANSFER_ORDER
            else -> GrnSourceType.PURCHASE_ORDER
        }
}
