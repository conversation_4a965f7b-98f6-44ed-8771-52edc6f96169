package com.hellofresh.cif.goodsreceivednote

import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.goodsreceivednote.deserializer.GoodsReceivedNoteKeyDeserializer
import com.hellofresh.cif.goodsreceivednote.deserializer.GoodsReceivedNoteValueDeserializer
import com.hellofresh.cif.goodsreceivednote.deserializer.YfGoodsReceivedNoteKeyDeserializer
import com.hellofresh.cif.goodsreceivednote.deserializer.YfGoodsReceivedNoteValueDeserializer
import com.hellofresh.cif.goodsreceivednote.models.GRNKey
import com.hellofresh.cif.goodsreceivednote.models.GRNValue
import com.hellofresh.cif.goodsreceivednote.repo.GoodsReceivedNoteRepositoryImpl
import com.hellofresh.cif.goodsreceivednote.service.GoodsReceivedNoteProcessor
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepositoryImpl
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig

private const val STATUS_SERVER_HTTP_PORT = 8081
private const val GOODS_RECEIVED_NOTE_TOPIC = "public.distribution-center.inbound.goods-received-note.v1"
private const val YOU_FOODZ_GOODS_RECEIVED_NOTE_TOPIC = "public.ye.distribution-center.inbound.goods-received-note.v1"

@Suppress("MagicNumber", "LongMethod")
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val parallelism = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)
    val metricsDSLContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)

    StatusServer.run(
        meterRegistry,
        STATUS_SERVER_HTTP_PORT,
    )
    val pollTimeout = Duration.parse(
        ConfigurationLoader.getStringOrDefault("poll.timeout", "PT1S"),
    )
    val pollInterval = ConfigurationLoader.getIntegerOrDefault("poll.interval_ms", 20).toLong()
    val processTimeout = Duration.parse(
        ConfigurationLoader.getStringOrDefault("process.timeout", "PT15S"),
    )
    val kafkaConfiguration = ConfigurationLoader.loadKafkaConsumerConfigurations() + mapOf(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false")

    val goodsReceivedNoteRepository = GoodsReceivedNoteRepositoryImpl(metricsDSLContext)
    val statsigFeatureFlagClient = statsigFeatureFlagClient()
    val dcConfigService = DcConfigService(meterRegistry)
    val poInfoRepository = PurchaseOrderRepositoryImpl(
        metricsDSLContext,
        dcConfigService,
        statsigFeatureFlagClient,
    )

    val goodsReceivedNoteProcessor =
        GoodsReceivedNoteProcessor(
            goodsReceivedNoteRepository,
            poInfoRepository,
            SkuSpecificationService(meterRegistry),
            dcConfigService,
        )

    val consumerProcessorConfig = ConsumerProcessorConfig(
        kafkaConfiguration,
        GoodsReceivedNoteKeyDeserializer,
        GoodsReceivedNoteValueDeserializer,
        listOf(GOODS_RECEIVED_NOTE_TOPIC),
    )

    val consumerProcessorConfigYf = ConsumerProcessorConfig(
        kafkaConfiguration,
        YfGoodsReceivedNoteKeyDeserializer,
        YfGoodsReceivedNoteValueDeserializer,
        listOf(YOU_FOODZ_GOODS_RECEIVED_NOTE_TOPIC),
    )

    withContext(Dispatchers.IO) {
        repeat(parallelism) {
            launch {
                runGRNProcessor(
                    pollConfig = PollConfig(pollTimeout, pollInterval, processTimeout),
                    consumerProcessorConfig = consumerProcessorConfig,
                    meterRegistry = meterRegistry,
                    goodsReceivedNoteProcessor = goodsReceivedNoteProcessor,
                )
            }
        }
        repeat(parallelism) {
            launch {
                runYfGRNProcessor(
                    pollConfig = PollConfig(pollTimeout, pollInterval, processTimeout),
                    consumerProcessorConfig = consumerProcessorConfigYf,
                    meterRegistry = meterRegistry,
                    goodsReceivedNoteProcessor = goodsReceivedNoteProcessor,
                )
            }
        }
    }
}

private fun statsigFeatureFlagClient() = StatsigFactory.build(
    ::shutdownHook,
    sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
    userId = ConfigurationLoader.getStringOrFail("application.name"),
    isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
    hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
)

private suspend fun runGRNProcessor(
    pollConfig: PollConfig,
    consumerProcessorConfig: ConsumerProcessorConfig<GRNKey, GRNValue>,
    meterRegistry: HelloFreshMeterRegistry,
    goodsReceivedNoteProcessor: GoodsReceivedNoteProcessor,
) {
    CoroutinesProcessor(
        pollConfig = pollConfig,
        consumerProcessorConfig = consumerProcessorConfig,
        meterRegistry = meterRegistry,
        process = goodsReceivedNoteProcessor::processRecords,
        handleDeserializationException = DeserializationExceptionStrategy.create(
            LOG_ERROR_IGNORE,
            meterRegistry,
        ),
        recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
            meterRegistry,
            "grn_processor_write_failure",
        ),
    ).also {
        shutdownHook(it)
        HealthChecks.add(it)
        StartUpChecks.add(it)
    }.run()
}

private suspend fun runYfGRNProcessor(
    pollConfig: PollConfig,
    consumerProcessorConfig: ConsumerProcessorConfig<GRNKey, GRNValue>,
    meterRegistry: HelloFreshMeterRegistry,
    goodsReceivedNoteProcessor: GoodsReceivedNoteProcessor,
) {
    CoroutinesProcessor(
        pollConfig = pollConfig,
        consumerProcessorConfig = consumerProcessorConfig,
        meterRegistry = meterRegistry,
        process = goodsReceivedNoteProcessor::processRecords,
        handleDeserializationException = DeserializationExceptionStrategy.create(
            LOG_ERROR_IGNORE,
            meterRegistry,
        ),
        recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
            meterRegistry,
            "yf_grn_processor_write_failure",
        ),
    ).also {
        shutdownHook(it)
        HealthChecks.add(it)
        StartUpChecks.add(it)
    }.run()
}
