package com.hellofresh.cif.goodsreceivednote.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.goodsreceivednote.models.GRNKey
import com.hellofresh.cif.goodsreceivednote.models.GRNValue
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus
import com.hellofresh.cif.goodsreceivednote.repo.GoodsReceivedNoteRepository
import com.hellofresh.cif.goodsreceivednote.service.GoodsReceivedNoteMapper.getPoNr
import com.hellofresh.cif.goodsreceivednote.service.GoodsReceivedNoteMapper.mapGrn
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.skuSpecificationLib.SkuCodeLookUp
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.logging.log4j.kotlin.Logging

class GoodsReceivedNoteProcessor(
    private val goodsReceivedNoteRepo: GoodsReceivedNoteRepository,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val skuSpecificationService: SkuSpecificationService,
    private val dcConfigService: DcConfigService,
) {
    suspend fun processRecords(rawRecords: ConsumerRecords<GRNKey, GRNValue>) {
        if (rawRecords.isEmpty) {
            return
        }
        val poNrs = rawRecords.map { getPoNr(it.key().reference) }.toSet()
        val poInfoMap = purchaseOrderRepository.findPurchaseOrders(poNrs)
            .associateBy { v -> getPoNr(v.poReference) }

        val dcMap = resolveDcConfigurations(rawRecords, poInfoMap)
        val skuInfoMap = skuSpecificationService.skuCodeLookUp(dcMap.values.toList())

        val grns = toGrnWithFallbackDcCode(
            rawRecords,
            dcMap,
            skuInfoMap,
            poInfoMap,
        )

        if (grns.isNotEmpty()) {
            logger.info("GRN record count: ${grns.size}")
            goodsReceivedNoteRepo.save(grns)
        }
    }

    private fun toGrnWithFallbackDcCode(
        records: ConsumerRecords<GRNKey, GRNValue>,
        dcMap: Map<String, DistributionCenterConfiguration>,
        skuCodeLookUp: SkuCodeLookUp,
        poInfoMap: Map<String, PurchaseOrder>,
    ): List<Grn> = records.mapNotNull { record ->
        val resolveDcCode = resolveDcCode(record.key(), poInfoMap)
        kotlin.runCatching {
            val dc = requireNotNull(dcMap[resolveDcCode]) { " Dc Code not found $resolveDcCode" }
            mapGrn(
                record.key(),
                record.value(),
                dc,
                skuCodeLookUp,
            )
        }.onFailure {
            logger.error("Couldn't process Grn: Dc: ${resolveDcCode}Code,  po: ${record.key().reference}", it)
        }.getOrNull()
    }

    private fun resolveDcConfigurations(
        records: ConsumerRecords<GRNKey, GRNValue>,
        poInfoMap: Map<String, PurchaseOrder>
    ): Map<String, DistributionCenterConfiguration> =
        records.mapNotNull { record ->
            val dcCode = poInfoMap[getPoNr(record.key().reference)]?.dcCode ?: record.key().dcCode
            dcConfigService.dcConfigurations[dcCode] ?: run {
                logger.error("Dc $dcCode not Found for GRN ${record.key().reference}")
                null
            }
        }.associateBy { distributionCenterConfiguration -> distributionCenterConfiguration.dcCode }

    private fun resolveDcCode(goodsReceivedNoteKey: GRNKey, poInfoMap: Map<String, PurchaseOrder>) =
        poInfoMap[getPoNr(goodsReceivedNoteKey.reference)]?.dcCode ?: goodsReceivedNoteKey.dcCode

    companion object : Logging
}

data class Grn(
    val dcCode: String,
    val poRef: String,
    val sourceType: GrnSourceType,
    val skus: List<GrnSku>,
) : Logging {
    val poNumber = poRef.split("_").let {
        if (it.size != 2) {
            logger.warn("PoRef cannot be parsed, using it as-is for Po number. $poRef")
        }
        it[0]
    }
}

enum class GrnSourceType {
    PURCHASE_ORDER,
    TRANSFER_ORDER,
}

data class GrnSku(
    val deliveryId: String,
    val deliveryDateTime: OffsetDateTime,
    val skuId: UUID,
    val totalPalletizedQty: SkuQuantity,
    val status: DeliveryStatus,
    val expiryDate: LocalDate?,
    val skuUOM: SkuUOM,
)
