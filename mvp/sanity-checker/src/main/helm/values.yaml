---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

serviceAccountAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/procurement-inventory-live-staging

cronJobs:
  cronjob:
    repository: '@dockerRepository@'
    pullPolicy: Always
    schedule: "*/10 * * * *"
    concurrencyPolicy: "Forbid"
    env:
      HF_INVENTORY_READONLY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#READONLY_DB_PASSWORD'
      HF_AIVEN_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_TAPIOCA_DB_PASSWORD: 'vault:@tier@/key-value/data/misc#TAPIOCA_DB_PASSWORD'

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'

prometheus-pushgateway:
  fullnameOverride: "@applicationId@-prometheus-pushgateway"
  image:
    repository: docker.io/prom/pushgateway
  podLabels:
    app:  cif-prometheus-pushgateway
    environment: '@tier@'
    tribe: '@tribe@'
    squad: '@squad@'
  resources:
    limits:
      memory: 200Mi
      cpu: 100m
    requests:
      cpu: 100m
      memory: 200Mi
  service:
    type: ClusterIP
    port: 9091
    targetPort: 9091
  ingress:
    enabled: true
    className: edge-stack-private
    hosts:
      - "cif-sanity-checker-prometheus-pushgateway.@<EMAIL>"
  serviceMonitor:
    enabled: true
    namespace: scm
    additionalLabels:
      prometheus: kube-prometheus


