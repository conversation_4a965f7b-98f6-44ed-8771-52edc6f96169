package com.hellofresh.cif.sanityChecker

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.sanity_checker.schema.Tables
import io.github.resilience4j.retry.Retry
import java.time.DayOfWeek
import java.time.ZoneId
import kotlinx.coroutines.future.await
import org.jooq.DSLContext

object DcConfig {
    lateinit var dcStore: Map<String, DistributionCenterConfiguration>
    suspend fun loadDCs(retry: Retry, dslContext: DSLContext) {
        dcStore = Retry.decorateSupplier(retry) {
            dslContext.selectFrom(Tables.DC_CONFIG)
                .fetchAsync()
                .thenApply { results ->
                    results.map { dcRecord ->
                        DistributionCenterConfiguration(
                            dcCode = dcRecord.dcCode,
                            productionStart = DayOfWeek.valueOf(dcRecord.productionStart),
                            cleardown = DayOfWeek.valueOf(dcRecord.cleardown),
                            market = dcRecord.market,
                            zoneId = ZoneId.of(dcRecord.zoneId),
                            enabled = dcRecord.enabled,
                            hasCleardown = dcRecord.hasCleardown,
                            wmsType = WmsSystem.valueOf(dcRecord.wmsType),
                            poCutoffTime = dcRecord.poCutoffTime,
                            brands = dcRecord.brands.toList(),
                        )
                    }.associateBy { it.dcCode }
                }
        }.get().await()
    }
}
