package com.hellofresh.cif.sanityChecker

import io.github.resilience4j.core.IntervalFunction.ofExponentialBackoff
import io.github.resilience4j.retry.Retry
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.prometheus.client.Gauge
import io.prometheus.client.exporter.PushGateway
import java.time.Duration

const val MAX_PUSH_METRICS_SUBMIT_RETRIES = 4
const val INITIAL_INTERVAL_MS = 3000L
private const val SOURCE_LITERAL = "source"

class PrometheusClient(private val prometheus: PushGateway) {

    private val retry = RetryRegistry.of(
        RetryConfig.custom<Any>()
            .maxAttempts(MAX_PUSH_METRICS_SUBMIT_RETRIES)
            .intervalFunction(ofExponentialBackoff(Duration.ofMillis(INITIAL_INTERVAL_MS), 2.0))
            .failAfterMaxAttempts(true)
            .build(),
    )
        .retry("prometheus-push-metrics")

    fun push(gauge: Gauge, source: String, groupingLabels: Map<String, String>) = Retry.decorateCallable(retry) {
        prometheus.pushAdd(gauge, JOB_NAME, mapOf(SOURCE_LITERAL to source) + groupingLabels)
    }.call()

    fun push(gauge: Gauge, source: String) = push(gauge, source, emptyMap())
}

open class PrometheusMetrics(val gauge: Gauge, prometheus: PushGateway) {

    private val prometheusClient = PrometheusClient(prometheus)
    fun push(source: String) = prometheusClient.push(gauge, source)
}

class ComparisonMetrics(gauge: Gauge, prometheus: PushGateway) : PrometheusMetrics(gauge, prometheus) {

    private fun setMissedCif(source: String, dcCode: String, productionWeek: String, value: Long) =
        gauge.labels(source, dcCode, productionWeek, "missed_in_cif").set(value.toDouble())

    private fun setMissedSourceOfTruth(source: String, dcCode: String, productionWeek: String, value: Long) =
        gauge.labels(source, dcCode, productionWeek, "missed_in_source_of_truth").set(value.toDouble())

    private fun setMismatchCount(source: String, dcCode: String, productionWeek: String, value: Long) =
        gauge.labels(source, dcCode, productionWeek, "mismatch_count").set(value.toDouble())

    private fun setMismatchAmount(source: String, dcCode: String, productionWeek: String, value: Long) =
        gauge.labels(source, dcCode, productionWeek, "mismatch_amount").set(value.toDouble())

    fun set(source: String, dcCode: String, productionWeek: String, comp: Comparison) = comp.let {
        setMissedCif(source, dcCode, productionWeek, comp.missedCifCount.toLong())
        setMissedSourceOfTruth(source, dcCode, productionWeek, comp.missedSourceOfTruthCount.toLong())
        setMismatchCount(source, dcCode, productionWeek, comp.mismatchCount.toLong())
        setMismatchAmount(source, dcCode, productionWeek, comp.totalMismatch)
    }
}

class CronJobMetrics(gauge: Gauge, prometheus: PushGateway) : PrometheusMetrics(gauge, prometheus) {
    fun setFailedJobCount(source: String, failedCronJobCount: Int, type: String, jobName: String) =
        gauge.labels(source, type, jobName).set(failedCronJobCount.toDouble())
}

fun failedCronJobMetrics(prometheus: PushGateway) = CronJobMetrics(
    Gauge.build(
        "procurement_csku_inventory_forecast_sanity_checker_monitor_inventory_cron_jobs",
        "Notification for failed inventory cron jobs",
    ).labelNames(SOURCE_LITERAL, "type", "jobName").create(),
    prometheus,
)
