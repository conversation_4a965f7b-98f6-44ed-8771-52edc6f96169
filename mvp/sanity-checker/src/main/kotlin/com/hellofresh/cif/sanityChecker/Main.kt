package com.hellofresh.cif.sanityChecker

import com.hellofresh.cif.sanityChecker.models.cifDataSource
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.service.Config
import com.zaxxer.hikari.HikariDataSource
import io.github.resilience4j.core.IntervalFunction
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.prometheus.client.exporter.PushGateway
import java.time.Duration
import java.util.concurrent.Executors
import kotlin.system.exitProcess
import org.apache.logging.log4j.kotlin.logger
import org.jooq.SQLDialect.POSTGRES
import org.jooq.conf.Settings
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration

val log = logger("com.hellofresh.cif.sanityChecker")
const val JOB_NAME = "csku-inventory-forecast-sanity-checker"

private const val CLIENT_DB_TIMEOUT_IN_MILLISECONDS = 30000
private const val MAX_DATABASE_CALLS_RETRIES = 4
private const val INITIAL_INTERVAL_MS_DATABASE_CALLS = 10000L

suspend fun main() {
    val retry = RetryRegistry.of(
        RetryConfig.custom<Any>()
            .maxAttempts(MAX_DATABASE_CALLS_RETRIES)
            .intervalFunction(
                IntervalFunction.ofExponentialBackoff(
                    Duration.ofMillis(INITIAL_INTERVAL_MS_DATABASE_CALLS),
                    2.0
                )
            )
            .failAfterMaxAttempts(true)
            .build(),
    )
        .retry("sanity-checker-database-fetch")
    val config = Config.load()
    val cifDatasource = shutdownNeeded { cifDataSource(config) }
    val cifDsl = jooqDsl(cifDatasource)

    val prometheus = PushGateway(config["metrics.gateway"])
    DcConfig.loadDCs(retry, cifDsl)

    runCatching {
        matchLiveInventory(prometheus, cifDsl)
    }.onFailure { log.error("Unable to match live inventory", it) }

    runCatching {
        monitorSkuIdCollision(prometheus, cifDsl)
    }.onFailure { log.error("Unable to monitor Sku Specification Id Collisions", it) }

    runCatching {
        monitorInventoryCronJobs(retry, prometheus, cifDatasource)
    }.onFailure { log.error("Unable to monitor inventory cron jobs", it) }

    exitProcess(0)
}

private fun jooqDsl(dataSource: HikariDataSource) =
    DSL.using(
        DefaultConfiguration().apply {
            setSQLDialect(POSTGRES)
            setDataSource(dataSource)
            setExecutor(
                Executors.newFixedThreadPool(2)
                    .also {
                        shutdownHook(AutoCloseable { it.shutdownNow() })
                    },
            )
            setSettings(Settings().withQueryTimeout(CLIENT_DB_TIMEOUT_IN_MILLISECONDS))
        },
    )
