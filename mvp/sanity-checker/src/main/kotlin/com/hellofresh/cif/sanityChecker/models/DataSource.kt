package com.hellofresh.cif.sanityChecker.models

import com.hellofresh.service.Config
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.github.resilience4j.retry.Retry
import java.sql.ResultSet

fun cifDataSource(config: Config) = HikariDataSource(
    HikariConfig().also {
        it.driverClassName = org.postgresql.Driver::class.java.name
        it.jdbcUrl = "jdbc:postgresql://${config["inventory.db.host"]}/inventory"
        it.username = config["inventory.readonly.db.username"]
        it.password = config["inventory.readonly.db.password"]
        it.isReadOnly = true
        it.validate()
    },
)

fun <K, V> HikariDataSource.fetchData(retry: Retry, query: String, f: ((ResultSet) -> Pair<K, V>?)) =
    Retry.decorateSupplier(retry) {
        connection.use { conn ->
            val result = mutableMapOf<K, V>()
            conn.autoCommit = false
            conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY).use { stmt ->
                stmt.executeQuery(query).use { rs ->
                    while (rs.next()) {
                        f(rs)?.let {
                            val prev = result.putIfAbsent(it.first, it.second)
                            assert(prev == null) // There shouldn't be a previous mapping
                        }
                    }
                }
            }
            result.toMap()
        }
    }.get()

@Suppress("NestedBlockDepth")
fun <V> HikariDataSource.fetchDataList(retry: Retry, query: String, f: ((ResultSet) -> V)) =
    Retry.decorateSupplier(retry) {
        connection.use { conn ->
            val result = mutableListOf<V>()
            conn.autoCommit = false
            conn.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY).use { stmt ->
                stmt.executeQuery(query).use { rs ->
                    while (rs.next()) {
                        result.add(f(rs))
                    }
                }
            }
            result.toList()
        }
    }.get()
