package com.hellofresh.cif.sanityChecker.models

import java.time.LocalDate
import java.time.format.DateTimeFormatter.ISO_WEEK_DATE

class ProductionWeeks(fromDate: LocalDate = LocalDate.now()) {
    private var _weeks: List<String> = (0 until WEEKS_AHEAD).map {
        fromDate
            .plusWeeks(it.toLong())
            .format(ISO_WEEK_DATE)
            .replaceFirst(regex = "(-)\\d$".toRegex(), replacement = "")
    }

    val weeks = _weeks.toSet()
    override fun toString(): String = weeks.joinToString(separator = "', '", prefix = "'", postfix = "'")

    companion object {
        private const val WEEKS_AHEAD = 5
        fun from(currentWeek: String): ProductionWeeks =
            ProductionWeeks(LocalDate.parse(currentWeek.plus("-1"), ISO_WEEK_DATE))
    }
}
